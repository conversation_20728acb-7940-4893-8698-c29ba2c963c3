python解釋器路徑跟py主程序位置如下
/home/<USER>/Document/.venv/bin/python /home/<USER>/Document/flask-api/app.py
我有啟用debug模式故修改後不需要重起py
每個階段修改後您不用測試直接告訴我即可，頂多是需要提示我怎麼測試

第一個需求是路徑http://127.0.0.1:5000/market-data.html (等同路由 /market-data ) 推測試透過 static/js/price-reader.js 讀取 /api/prices?t=xxxxx 的數據返回內容
需要您修改的部份是 實物交易以公克報價為主
項目、買入[g]、賣出[g] 三個直欄項目需要在買入[g]、賣出[g]中間加上一欄位名為 買入(錢) ，這點對於您來說應該比較容易，

但第二個需求是其數據需要參照 原先js取得的 實物交易以公克報價為主 買入[g] 的數據乘上3.75作為 買入(錢) 該直欄的對應數值。

整體問題可能要注意js修改的準確性，避免發生改一個地方導致js運行故障，因為會多一個表直欄，可能也需要同時挪動既有的js。