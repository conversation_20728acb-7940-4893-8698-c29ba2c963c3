<!DOCTYPE html>
<html lang="zh-TW">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="最新金屬市場價格與分析，包含黃金、白銀、銅、鋁、不鏽鋼等金屬價格資訊">
    <meta name="keywords" content="金屬價格, 黃金價格, 白銀價格, 銅價, 鋁價, 不鏽鋼價格, 金屬回收">
    <meta name="author" content="迪奕科技">
    <title>金屬價格 - 迪奕科技金屬回收服務</title>

    <!-- Bulma CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='vendor/bulma/css/bulma.min.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/navbar.css') }}">
    
    <!-- 自定義樣式 -->
    <style>
        /* 自定義 Bulma 導航樣式 */
        .navbar {
            background-color: #2c3e50 !important;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 99999;
            box-shadow: 0 2px 10px rgba(44, 62, 80, 0.1);
        }
        
        .navbar-brand .navbar-item {
            font-size: 20px;
            font-weight: 700;
            color: #D4AF37 !important;
        }
        
        .navbar-item {
            color: #ecf0f1 !important;
            font-weight: 500;
        }
        
        .navbar-item:hover,
        .navbar-item.is-active {
            background-color: #D4AF37 !important;
            color: white !important;
        }
        
        .navbar-burger {
            color: #ecf0f1 !important;
            border: 2px solid #ecf0f1;
            border-radius: 4px;
            width: 44px;
            height: 44px;
        }
        
        .navbar-burger span {
            background-color: #ecf0f1 !important;
        }
        
        .navbar-menu {
            background-color: #2c3e50 !important;
            box-shadow: 0 4px 16px rgba(44, 62, 80, 0.2);
        }
        
        @media screen and (max-width: 1023px) {
            .navbar-menu {
                border-top: 1px solid #95a5a6;
            }
            
            .navbar-item {
                padding: 16px 24px;
                font-size: 18px;
            }
        }
        
        /* 主要內容樣式 */
        main {
            margin-top: 3.25rem; /* Bulma navbar 高度 */
            padding: 20px;
        }
        
        .page-header {
            background: linear-gradient(135deg, #D4AF37 0%, #B8860B 100%);
            color: white;
            padding: 40px 20px;
            text-align: center;
            border-radius: 10px;
            margin-bottom: 40px;
        }
        
        .page-header h1 {
            font-size: 2.5rem;
            margin-bottom: 20px;
        }
        
        .page-header p {
            font-size: 1.2rem;
            margin-bottom: 15px;
        }
        
        .market-overview {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(44, 62, 80, 0.1);
            margin-bottom: 30px;
        }
        
        .market-overview h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .market-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .market-card {
            background: #ecf0f1;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            border-left: 4px solid #D4AF37;
        }
        
        .market-card h3 {
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 1.2rem;
        }
        
        .market-value {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 5px;
            color: #2c3e50;
        }
        
        .market-change {
            font-size: 0.9rem;
            padding: 4px 8px;
            border-radius: 4px;
            display: inline-block;
        }
        
        .market-change.positive {
            background-color: #d4edda;
            color: #155724;
        }
        
        .market-change.negative {
            background-color: #f8d7da;
            color: #721c24;
        }
        
        .market-change.neutral {
            background-color: #e2e3e5;
            color: #383d41;
        }
        
        .chart-section {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(44, 62, 80, 0.1);
            margin-bottom: 30px;
        }
        
        .chart-section h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .chart-placeholder {
            background: #ecf0f1;
            border: 2px dashed #95a5a6;
            border-radius: 8px;
            padding: 60px 20px;
            text-align: center;
            color: #95a5a6;
        }
        
        .chart-placeholder h3 {
            margin-bottom: 10px;
            color: #2c3e50;
        }
        
        .news-section {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(44, 62, 80, 0.1);
        }
        
        .news-section h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .news-item {
            padding: 15px 0;
            border-bottom: 1px solid #ecf0f1;
        }
        
        .news-item:last-child {
            border-bottom: none;
        }
        
        .news-title {
            color: #2c3e50;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .news-summary {
            color: #95a5a6;
            font-size: 0.9rem;
            line-height: 1.4;
        }
        
        .news-time {
            color: #95a5a6;
            font-size: 0.8rem;
            margin-top: 5px;
        }

        /* 金屬價格表格樣式 - 避免與 Bulma 衝突 */
        .metal-tables-left {
            padding-right: 15px;
        }

        .metal-tables-right {
            padding-left: 15px;
        }

        .metal-price-table-container {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(44, 62, 80, 0.1);
            margin-bottom: 20px;
            overflow: hidden;
        }

        .metal-table-title {
            background: linear-gradient(135deg, #D4AF37 0%, #B8860B 100%);
            color: white;
            padding: 12px 16px;
            margin: 0;
            font-size: 1rem;
            font-weight: 600;
            text-align: center;
        }

        .metal-price-table {
            display: flex;
            flex-direction: column;
        }

        .metal-table-header {
            display: flex;
            background-color: #f8f9fa;
            border-bottom: 2px solid #D4AF37;
            font-weight: 600;
            color: #2c3e50;
        }

        .metal-table-row {
            display: flex;
            border-bottom: 1px solid #e9ecef;
            transition: background-color 0.2s ease;
        }

        .metal-table-row:hover {
            background-color: #f8f9fa;
        }

        .metal-table-row:last-child {
            border-bottom: none;
        }

        .metal-table-cell {
            flex: 1;
            padding: 10px 8px;
            text-align: center;
            font-size: 0.9rem;
            border-right: 1px solid #e9ecef;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 40px;
        }

        .metal-table-cell:last-child {
            border-right: none;
        }

        .metal-name {
            font-weight: 600;
            color: #2c3e50;
            background-color: #f8f9fa;
        }

        /* 響應式設計 */
        @media screen and (max-width: 1023px) {
            .columns.is-gapless {
                flex-direction: column;
            }

            .metal-tables-left,
            .metal-tables-right {
                padding: 0;
            }

            .metal-table-cell {
                font-size: 0.8rem;
                padding: 8px 4px;
                min-height: 35px;
            }

            .metal-table-title {
                font-size: 0.9rem;
                padding: 10px 12px;
            }
        }

        @media screen and (max-width: 768px) {
            .metal-table-cell {
                font-size: 0.75rem;
                padding: 6px 2px;
                min-height: 30px;
            }

            .metal-table-title {
                font-size: 0.85rem;
                padding: 8px 10px;
            }

            /* 在手機上隱藏部分欄位以節省空間 */
            .metal-table-header .metal-table-cell:nth-child(5),
            .metal-table-header .metal-table-cell:nth-child(6),
            .metal-table-row .metal-table-cell:nth-child(5),
            .metal-table-row .metal-table-cell:nth-child(6) {
                display: none;
            }
        }

        /* 價格變動顏色 */
        .price-positive {
            color: #28a745;
            font-weight: 600;
        }

        .price-negative {
            color: #dc3545;
            font-weight: 600;
        }

        .price-neutral {
            color: #6c757d;
        }

        /* 載入中動畫 */
        .loading-price {
            color: #95a5a6;
            font-style: italic;
        }

    </style>
    <style>
    .tv-quotes-container {
        padding: 424px 0 0 0;
        position: relative;
    }

    /* 手機端樣式：螢幕寬度小於 768px 時套用 */
    @media (max-width: 768px) {
        .tv-quotes-container {
            padding: 424px 0 0 0;
            display: none;
        }
    }
    </style>
</head>

<body>
    <!-- Bulma 導航選單 -->
    {% set active_page = 'market' %}
    {% include 'partials/navbar.html' %}

    <!-- 主要內容 -->
    <main>
        <!-- 頁面標題 -->
        <section class="page-header">
            <h1>金屬價格</h1>
            <p>最新金屬市場價格與專業分析</p>
            <p>掌握金屬市場脈動，了解回收價格趨勢</p>
        </section>

        <!-- 市場概覽 -->
        <section class="market-overview">
            <h2>市場概覽</h2>
            <h6 style="font-size: 80%;text-align: center;">報價由資訊服務商提供，價格可能有延遲或者錯誤的可能，將不負任何法律責任，實體回售交易請<a href="/booking.html">參考回購給我們</a></h6>
            <!-- 主要布局容器：左右並排 -->
            <div class="columns is-gapless">
                <!-- 左側 2/3 區塊 -->
                <div class="column is-two-thirds">
                    <div class="metal-tables-left">
                        <!-- 商品/美元 表格 -->
                        <div class="metal-price-table-container">
                            <h3 class="metal-table-title">國際報價 (美元)</h3>
                            <div class="metal-price-table">
                                <!-- 表頭 -->
                                <div class="metal-table-header">
                                    <div class="metal-table-cell">商品/美元</div>
                                    <div class="metal-table-cell">買入/盎司</div>
                                    <div class="metal-table-cell">賣出/盎司</div>
                                    <div class="metal-table-cell">漲/跌</div>
                                    <div class="metal-table-cell">最高</div>
                                    <div class="metal-table-cell">最低</div>
                                </div>
                                <!-- 數據行 -->
                                <div class="metal-table-row">
                                    <div class="metal-table-cell metal-name">金 Au</div>
                                    <div class="metal-table-cell" id="gold-usd-buy">載入中...</div>
                                    <div class="metal-table-cell" id="gold-usd-sell">載入中...</div>
                                    <div class="metal-table-cell" id="gold-usd-change">載入中...</div>
                                    <div class="metal-table-cell" id="gold-usd-high">載入中...</div>
                                    <div class="metal-table-cell" id="gold-usd-low">載入中...</div>
                                </div>
                                <div class="metal-table-row">
                                    <div class="metal-table-cell metal-name">銀 Ag</div>
                                    <div class="metal-table-cell" id="silver-usd-buy">載入中...</div>
                                    <div class="metal-table-cell" id="silver-usd-sell">載入中...</div>
                                    <div class="metal-table-cell" id="silver-usd-change">載入中...</div>
                                    <div class="metal-table-cell" id="silver-usd-high">載入中...</div>
                                    <div class="metal-table-cell" id="silver-usd-low">載入中...</div>
                                </div>
                                <div class="metal-table-row">
                                    <div class="metal-table-cell metal-name">鈀 Pd</div>
                                    <div class="metal-table-cell" id="palladium-usd-buy">載入中...</div>
                                    <div class="metal-table-cell" id="palladium-usd-sell">載入中...</div>
                                    <div class="metal-table-cell" id="palladium-usd-change">載入中...</div>
                                    <div class="metal-table-cell" id="palladium-usd-high">載入中...</div>
                                    <div class="metal-table-cell" id="palladium-usd-low">載入中...</div>
                                </div>
                                <div class="metal-table-row">
                                    <div class="metal-table-cell metal-name">鉑 Pt</div>
                                    <div class="metal-table-cell" id="platinum-usd-buy">載入中...</div>
                                    <div class="metal-table-cell" id="platinum-usd-sell">載入中...</div>
                                    <div class="metal-table-cell" id="platinum-usd-change">載入中...</div>
                                    <div class="metal-table-cell" id="platinum-usd-high">載入中...</div>
                                    <div class="metal-table-cell" id="platinum-usd-low">載入中...</div>
                                </div>
                            </div>
                        </div>

                        <!-- 商品/台幣 表格 -->
                        <div class="metal-price-table-container">
                            <h3 class="metal-table-title">國際報價 (台幣)</h3>
                            <div class="metal-price-table">
                                <!-- 表頭 -->
                                <div class="metal-table-header">
                                    <div class="metal-table-cell">商品/台幣</div>
                                    <div class="metal-table-cell">買入/台錢</div>
                                    <div class="metal-table-cell">賣出/台錢</div>
                                    <div class="metal-table-cell">漲/跌</div>
                                    <div class="metal-table-cell">最高</div>
                                    <div class="metal-table-cell">最低</div>
                                </div>
                                <!-- 數據行 -->
                                <div class="metal-table-row">
                                    <div class="metal-table-cell metal-name">金 Au</div>
                                    <div class="metal-table-cell" id="gold-twd-buy">載入中...</div>
                                    <div class="metal-table-cell" id="gold-twd-sell">載入中...</div>
                                    <div class="metal-table-cell" id="gold-twd-change">載入中...</div>
                                    <div class="metal-table-cell" id="gold-twd-high">載入中...</div>
                                    <div class="metal-table-cell" id="gold-twd-low">載入中...</div>
                                </div>
                                <div class="metal-table-row">
                                    <div class="metal-table-cell metal-name">銀 Ag</div>
                                    <div class="metal-table-cell" id="silver-twd-buy">載入中...</div>
                                    <div class="metal-table-cell" id="silver-twd-sell">載入中...</div>
                                    <div class="metal-table-cell" id="silver-twd-change">載入中...</div>
                                    <div class="metal-table-cell" id="silver-twd-high">載入中...</div>
                                    <div class="metal-table-cell" id="silver-twd-low">載入中...</div>
                                </div>
                                <div class="metal-table-row">
                                    <div class="metal-table-cell metal-name">鈀 Pd</div>
                                    <div class="metal-table-cell" id="palladium-twd-buy">載入中...</div>
                                    <div class="metal-table-cell" id="palladium-twd-sell">載入中...</div>
                                    <div class="metal-table-cell" id="palladium-twd-change">載入中...</div>
                                    <div class="metal-table-cell" id="palladium-twd-high">載入中...</div>
                                    <div class="metal-table-cell" id="palladium-twd-low">載入中...</div>
                                </div>
                                <div class="metal-table-row">
                                    <div class="metal-table-cell metal-name">鉑 Pt</div>
                                    <div class="metal-table-cell" id="platinum-twd-buy">載入中...</div>
                                    <div class="metal-table-cell" id="platinum-twd-sell">載入中...</div>
                                    <div class="metal-table-cell" id="platinum-twd-change">載入中...</div>
                                    <div class="metal-table-cell" id="platinum-twd-high">載入中...</div>
                                    <div class="metal-table-cell" id="platinum-twd-low">載入中...</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 右側 1/3 區塊 -->
                <div class="column is-one-third">
                    <div class="metal-tables-right">
                        <!-- 實物交易以公克報價 -->
                        <div class="metal-price-table-container">
                            <h3 class="metal-table-title">實物交易以公克報價為主</h3>
                            <div class="metal-price-table">
                                <!-- 表頭 -->
                                <div class="metal-table-header">
                                    <div class="metal-table-cell">項目</div>
                                    <div class="metal-table-cell">買入[g]</div>
                                    <div class="metal-table-cell">賣出[g]</div>
                                </div>
                                <!-- 數據行 -->
                                <div class="metal-table-row">
                                    <div class="metal-table-cell metal-name">金 Au</div>
                                    <div class="metal-table-cell" id="gold-gram-buy">載入中...</div>
                                    <div class="metal-table-cell" id="gold-gram-sell">載入中...</div>
                                </div>
                                <div class="metal-table-row">
                                    <div class="metal-table-cell metal-name">銀 Ag</div>
                                    <div class="metal-table-cell" id="silver-gram-buy">載入中...</div>
                                    <div class="metal-table-cell" id="silver-gram-sell">載入中...</div>
                                </div>
                                <div class="metal-table-row">
                                    <div class="metal-table-cell metal-name">鈀 Pd</div>
                                    <div class="metal-table-cell" id="palladium-gram-buy">載入中...</div>
                                    <div class="metal-table-cell" id="palladium-gram-sell">載入中...</div>
                                </div>
                                <div class="metal-table-row">
                                    <div class="metal-table-cell metal-name">鉑 Pt</div>
                                    <div class="metal-table-cell" id="platinum-gram-buy">載入中...</div>
                                    <div class="metal-table-cell" id="platinum-gram-sell">載入中...</div>
                                </div>
                                <div class="metal-table-row">
                                    <div class="metal-table-cell metal-name">銠 Rh</div>
                                    <div class="metal-table-cell" id="rhodium-gram-buy">載入中...</div>
                                    <div class="metal-table-cell" id="rhodium-gram-sell">載入中...</div>
                                </div>
                                <div class="metal-table-row">
                                    <div class="metal-table-cell metal-name">釕 Ru</div>
                                    <div class="metal-table-cell" id="ruthenium-gram-buy">載入中...</div>
                                    <div class="metal-table-cell" id="ruthenium-gram-sell">載入中...</div>
                                </div>
                                <div class="metal-table-row">
                                    <div class="metal-table-cell metal-name">銥 Ir</div>
                                    <div class="metal-table-cell" id="iridium-gram-buy">載入中...</div>
                                    <div class="metal-table-cell" id="iridium-gram-sell">載入中...</div>
                                </div>
                                <div class="metal-table-row">
                                    <div class="metal-table-cell metal-name">鋨 Os</div>
                                    <div class="metal-table-cell" id="osmium-gram-buy">載入中...</div>
                                    <div class="metal-table-cell" id="osmium-gram-sell">載入中...</div>
                                </div>
                            </div>
                        </div>

                        <!-- 其他材料以公斤報價 -->
                        <div class="metal-price-table-container">
                            <h3 class="metal-table-title">其他材料以公斤報價</h3>
                            <div class="metal-price-table">
                                <!-- 表頭 -->
                                <div class="metal-table-header">
                                    <div class="metal-table-cell">項目</div>
                                    <div class="metal-table-cell">買入[kg]</div>
                                    <div class="metal-table-cell">賣出[kg]</div>
                                </div>
                                <!-- 數據行 -->
                                <div class="metal-table-row">
                                    <div class="metal-table-cell metal-name">鈷 Co</div>
                                    <div class="metal-table-cell" id="cobalt-kg-buy">載入中...</div>
                                    <div class="metal-table-cell" id="cobalt-kg-sell">載入中...</div>
                                </div>
                                <div class="metal-table-row">
                                    <div class="metal-table-cell metal-name">錫 Sn</div>
                                    <div class="metal-table-cell" id="tin-kg-buy">載入中...</div>
                                    <div class="metal-table-cell" id="tin-kg-sell">載入中...</div>
                                </div>
                            </div>
                        </div>

                        <!-- 貨幣項目 -->
                        <div class="metal-price-table-container">
                            <h3 class="metal-table-title">貨幣項目</h3>
                            <div class="metal-price-table">
                                <!-- 表頭 -->
                                <div class="metal-table-header">
                                    <div class="metal-table-cell">貨幣項目</div>
                                    <div class="metal-table-cell">買入</div>
                                    <div class="metal-table-cell">賣出</div>
                                </div>
                                <!-- 數據行 -->
                                <div class="metal-table-row">
                                    <div class="metal-table-cell metal-name">美元</div>
                                    <div class="metal-table-cell" id="usd-buy">載入中...</div>
                                    <div class="metal-table-cell" id="usd-sell">載入中...</div>
                                </div>
                                <div class="metal-table-row">
                                    <div class="metal-table-cell metal-name">人民幣</div>
                                    <div class="metal-table-cell" id="cny-buy">載入中...</div>
                                    <div class="metal-table-cell" id="cny-sell">載入中...</div>
                                </div>
                                <div class="metal-table-row">
                                    <div class="metal-table-cell metal-name">歐元</div>
                                    <div class="metal-table-cell" id="eur-buy">載入中...</div>
                                    <div class="metal-table-cell" id="eur-sell">載入中...</div>
                                </div>
                            </div>
                            <div class="mt-2 text-center">
                                <small class="text-muted">供參考無外幣買賣</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 價格更新時間 -->
            <div style="text-align: center; margin-top: 20px; color: #95a5a6; font-size: 0.9rem;">
                <i class="fas fa-clock"></i> <span id="price-update-time">價格更新時間：載入中...</span>
            </div>
        </section>

        <!-- 圖表區域 -->
        <section class="chart-section">
            <h2>市場走勢圖</h2>
            <h6 style="font-size: 80%;
    text-align: center;">報價由資訊服務商提供，價格可能有延遲或者錯誤的可能，將不負任何法律責任，實體回售交易請<a href="/booking.html">參考回購給我們</a></h6>
            <!-- TradingView Widget BEGIN -->
             <div class="tv-ticker-container" style="padding: 76px 0 0 0;
        position: relative;"></div>
            <script>
            const config = {
                symbols: [
                { description: "🟡 現貨黃金", proName: "XAUUSD" },
                { description: "⚪ 白銀期貨", proName: "XAGUSD" },
                { description: "🔩 鉑金現貨", proName: "XPTUSD" },
                { description: "🔩 鈀金現貨", proName: "XPDUSD" },
                { description: "🛢️ WTI 原油", proName: "TVC:USOIL" }
                ],
                showSymbolLogo: true,
                colorTheme: "light",
                isTransparent: false,
                displayMode: "adaptive",
                width: "100%",
                height: 76
            };

            const encoded = encodeURIComponent(JSON.stringify(config));
            const src = `https://www.tradingview-widget.com/embed-widget/ticker-tape/?locale=zh_TW#${encoded}`;

            const iframe = document.createElement("iframe");
            iframe.className = "tv-ticker"; // ✅ class 標記
            iframe.src = src;
            iframe.width = "100%";
            iframe.height = "76";
            iframe.frameBorder = "0";
            iframe.scrolling = "no";
            iframe.allowTransparency = "true";
            iframe.style = "display: block; user-select: none; box-sizing: border-box;";

            document.querySelector(".tv-ticker-container").appendChild(iframe);
            </script>
            <div class="columns">
                <div class="column is-half">
                    <div class="tv-quotes-container" style="height: 400px; position: relative;">
                        <!-- TradingView 報價表將會動態載入到這裡 -->
                        <div class="loading">載入中...</div>
                    </div>
                </div>
                <div class="column is-half">
                    <div style="padding:424px 0 0 0;position:relative; height: 0; overflow: hidden;"> 
                        <iframe scrolling="no" allowtransparency="true" frameborder="0"
                            src="https://www.tradingview-widget.com/embed-widget/advanced-chart/?locale=zh_TW#%7B%22symbol%22%3A%22TVC%3AGOLD%22%2C%22interval%22%3A%22D%22%2C%22theme%22%3A%22light%22%2C%22autosize%22%3Atrue%7D"
                            title="advanced chart TradingView widget" lang="zh-TW"
                            style="position:absolute;top:0;left:0;width:100%;height:100%;">
                        </iframe>
                    </div>
                </div>
            </div>
            <script>
                    // 等待頁面載入完成
                    document.addEventListener('DOMContentLoaded', function() {
                        initTradingViewQuotesWidget();
                    });
                    
                    function initTradingViewQuotesWidget() {
                        try {
                            // 結構化配置
                            const widgetConfig = {
                                "width": "100%",
                                "height": "100%",
                                "symbolsGroups": [
                                    {
                                        "name": "美元 / 盎司",
                                        "symbols": [
                                            {"name": "XAUUSD", "displayName": "黃金"},
                                            {"name": "XAGUSD", "displayName": "白銀"},
                                            {"name": "XPTUSD", "displayName": "鉑金"},
                                            {"name": "XPDUSD", "displayName": "鈀金"}
                                        ]
                                    },
                                    {
                                        "name": "台幣 / 台錢",
                                        "symbols": [
                                            {"name": "XAUUSD*USDTWD/8.2944", "displayName": "黃金"},
                                            {"name": "XAGUSD*USDTWD/8.2944", "displayName": "白銀"},
                                            {"name": "XPTUSD*USDTWD/8.2944", "displayName": "鉑金"},
                                            {"name": "XPDUSD*USDTWD/8.2944", "displayName": "鈀金"}
                                        ]
                                    }
                                ],
                                "showSymbolLogo": false,
                                "colorTheme": "light",
                                "isTransparent": false,
                                "utm_source": "widget_demo",
                                "utm_medium": "widget",
                                "utm_campaign": "market-quotes"
                            };
                            
                            // 編碼配置為 JSON 字符串
                            const configString = JSON.stringify(widgetConfig);
                            const encodedConfig = encodeURIComponent(configString);
                            
                            // 構建完整的 iframe src
                            const baseUrl = "https://www.tradingview-widget.com/embed-widget/market-quotes/";
                            const src = `${baseUrl}?locale=zh_TW#${encodedConfig}`;
                            
                            console.log('Quotes Widget config:', widgetConfig);
                            console.log('Generated URL:', src);
                            
                            // 創建 iframe
                            const iframe = document.createElement("iframe");
                            iframe.src = src;
                            iframe.width = "100%";
                            iframe.height = "100%";
                            iframe.frameBorder = "0";
                            iframe.scrolling = "no";
                            iframe.allowTransparency = "true";
                            iframe.className = "tv-widget-market-quotes";
                            iframe.title = "TradingView 貴金屬報價表";
                            iframe.lang = "zh-TW";
                            iframe.style.cssText = "position:absolute;top:0;left:0;height: 100%; width: 100%;";
                            
                            // 載入事件處理
                            iframe.onload = function() {
                                console.log('TradingView quotes widget loaded successfully');
                                const loadingDiv = document.querySelector('.tv-quotes-container .loading');
                                if (loadingDiv) {
                                    loadingDiv.style.display = 'none';
                                }
                            };
                            
                            iframe.onerror = function() {
                                console.error('TradingView quotes widget failed to load');
                                showQuotesError('報價表載入失敗，請稍後再試');
                            };
                            
                            // 清空容器並添加 iframe
                            const container = document.querySelector(".tv-quotes-container");
                            container.innerHTML = '<div class="loading">載入中...</div>';
                            container.appendChild(iframe);
                            
                            // 設置超時檢查
                            setTimeout(function() {
                                const loadingDiv = document.querySelector('.tv-quotes-container .loading');
                                if (loadingDiv && loadingDiv.style.display !== 'none') {
                                    showQuotesError('報價表載入超時，請檢查網絡連接或稍後再試');
                                }
                            }, 15000);
                            
                        } catch (error) {
                            console.error('Error initializing TradingView quotes widget:', error);
                            showQuotesError('初始化報價表時發生錯誤: ' + error.message);
                        }
                    }
                    
                    function showQuotesError(message) {
                        const container = document.querySelector(".tv-quotes-container");
                        container.innerHTML = `
                            <div class="quotes-error-message" style="padding: 20px; text-align: center; color: #e74c3c; background: #ffeaea; border-radius: 4px;">
                                <h3>❌ 載入錯誤</h3>
                                <p>${message}</p>
                                <button onclick="initTradingViewQuotesWidget()" style="margin-top: 10px; padding: 8px 16px; background: #3498db; color: white; border: none; border-radius: 4px; cursor: pointer;">
                                    重新載入
                                </button>
                            </div>
                        `;
                    }
                    
                    // 添加錯誤處理
                    window.addEventListener('error', function(e) {
                        console.error('Global error (quotes widget):', e.error);
                    });
            </script>
            <!-- TradingView Widget END -->
        </section>

        <!-- 市場新聞 -->
        <section class="news-section">
            <h2>最新市場動態</h2>

            <!-- 動態新聞容器 -->
            <div id="news-container">
                <!-- 載入中的預設內容 -->
                <div class="news-item mb-3 p-3 border rounded">
                    <div class="d-flex justify-content-between align-items-start mb-2">
                        <h6 class="news-title mb-0 text-primary">載入中...</h6>
                        <small class="text-muted">--</small>
                    </div>
                    <p class="news-content mb-2 text-muted">正在載入最新市場動態...</p>
                    <span class="badge bg-secondary">系統</span>
                </div>
            </div>

            <!-- 新聞更新時間 -->
            <div class="text-end mt-3">
                <small id="news-update-time" class="text-muted">新聞更新時間：載入中...</small>
            </div>
        </section>
    </main>

    {% include 'partials/footer.html' %}

    <!-- Bulma 導航 JavaScript -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 初始化 Bulma 導航系統...');
            
            // 獲取 Bulma navbar burger
            const navbarBurger = document.querySelector('.navbar-burger');
            const navbarMenu = document.getElementById('navbarMenu');
            
            if (navbarBurger && navbarMenu) {
                // 漢堡選單點擊事件
                navbarBurger.addEventListener('click', function() {
                    console.log('🖱️ Bulma 漢堡選單被點擊');
                    
                    // 切換活動狀態
                    navbarBurger.classList.toggle('is-active');
                    navbarMenu.classList.toggle('is-active');
                    
                    // 更新 aria 屬性
                    const isExpanded = navbarBurger.classList.contains('is-active');
                    navbarBurger.setAttribute('aria-expanded', isExpanded);
                    
                    console.log(isExpanded ? '🔓 導航已開啟' : '🔒 導航已關閉');
                });
                
                // 點擊選單項目時關閉手機版選單
                const navbarItems = navbarMenu.querySelectorAll('.navbar-item');
                navbarItems.forEach(item => {
                    item.addEventListener('click', function() {
                        if (navbarMenu.classList.contains('is-active')) {
                            console.log('🔗 選單項目被點擊，關閉手機版選單');
                            navbarBurger.classList.remove('is-active');
                            navbarMenu.classList.remove('is-active');
                            navbarBurger.setAttribute('aria-expanded', 'false');
                        }
                        
                        // 更新活動狀態
                        navbarItems.forEach(nav => nav.classList.remove('is-active'));
                        this.classList.add('is-active');
                    });
                });
                
                console.log('✅ Bulma 導航系統初始化完成');
            } else {
                console.log('❌ 找不到 Bulma 導航元素');
            }
        });
    </script>

    <!-- Chart.js -->
    <script src="{{ url_for('static', filename='vendor/chartjs/js/chart.min.js') }}"></script>
    
    <!-- Font Awesome for icons -->
    <script src="{{ url_for('static', filename='vendor/fontawesome/js/all.min.js') }}"></script>
    
    <!-- 圖表功能 -->
    <script src="/static/js/chart.js"></script>

    <!-- 新的價格讀取器 - 使用Unix時間戳緩存 -->
    <script src="/static/js/price-reader.js"></script>

    <!-- 新聞讀取器 - 使用Unix時間戳緩存 -->
    <script src="/static/js/news-reader.js"></script>
</body>

</html>
