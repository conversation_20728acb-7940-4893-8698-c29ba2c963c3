<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理員登入 - 金屬價格管理系統</title>
    
    <!-- Bootstrap CSS -->
    <link href="{{ url_for('static', filename='vendor/bootstrap/css/bootstrap.min.css') }}" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="{{ url_for('static', filename='vendor/fontawesome/css/all.min.css') }}">

    <!-- Toastr CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='vendor/toastr/css/toastr.min.css') }}">
    
    <style>
        body {
            background: linear-gradient(135deg, #D4AF37, #B8860B);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .login-container {
            width: 100%;
            max-width: 400px;
            padding: 20px;
        }

        .login-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            padding: 2rem;
        }

        .login-header {
            text-align: center;
            margin-bottom: 2rem;
            color: #D4AF37;
        }

        .token-input {
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 12px;
            font-size: 1rem;
        }

        .token-input:focus {
            border-color: #D4AF37;
            box-shadow: 0 0 0 0.2rem rgba(212, 175, 55, 0.25);
        }

        .btn-gold {
            background-color: #D4AF37;
            border-color: #D4AF37;
            color: white;
        }
        
        .btn-gold:hover {
            background-color: #B8860B;
            border-color: #B8860B;
            color: white;
        }

        .form-label {
            font-weight: 600;
            color: #495057;
        }

        .alert {
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <!-- 登入界面 -->
    <div class="login-container">
        <div class="login-card">
            <div class="login-header">
                <h3><i class="fas fa-lock me-2"></i>管理員認證</h3>
                <p class="text-muted">請輸入管理員 Token 以訪問價格管理系統</p>
            </div>

            <form id="loginForm">
                <div class="mb-3">
                    <label for="adminToken" class="form-label">管理員 Token</label>
                    <input type="password" class="form-control token-input" id="adminToken"
                           placeholder="請輸入您的管理員 Token" required>
                </div>
                <div class="d-grid">
                    <button type="submit" class="btn btn-gold btn-lg">
                        <i class="fas fa-sign-in-alt me-2"></i>登入
                    </button>
                </div>
            </form>

            <div id="loginError" class="alert alert-danger mt-3" style="display: none;"></div>

            <div class="mt-4 text-center">
                <small class="text-muted">
                    <i class="fas fa-info-circle me-1"></i>
                    請注意輸入大小寫與特殊字符。
                </small>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="{{ url_for('static', filename='vendor/bootstrap/js/bootstrap.bundle.min.js') }}"></script>

    <!-- jQuery (toastr 需要) -->
    <script src="{{ url_for('static', filename='vendor/jquery/jquery.min.js') }}"></script>

    <!-- Toastr JS -->
    <script src="{{ url_for('static', filename='vendor/toastr/js/toastr.min.js') }}"></script>

    <script>
        // 配置 toastr
        toastr.options = {
            "closeButton": true,
            "debug": false,
            "newestOnTop": true,
            "progressBar": true,
            "positionClass": "toast-top-right",
            "preventDuplicates": false,
            "onclick": null,
            "showDuration": "300",
            "hideDuration": "1000",
            "timeOut": "5000",
            "extendedTimeOut": "1000",
            "showEasing": "swing",
            "hideEasing": "linear",
            "showMethod": "fadeIn",
            "hideMethod": "fadeOut"
        };

        // 登入處理
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const token = document.getElementById('adminToken').value;
            const errorDiv = document.getElementById('loginError');
            
            if (!token) {
                showError('請輸入管理員 Token');
                return;
            }
            
            try {
                // 驗證 Token
                const response = await fetch('/api/validate-token', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ token: token })
                });
                
                if (response.ok) {
                    // Token 有效，保存到 localStorage 並跳轉到管理面板
                    localStorage.setItem('admin_token', token);
                    toastr.success('登入成功，正在跳轉...');
                    
                    // 延遲跳轉以顯示成功消息
                    setTimeout(() => {
                        window.location.href = '/admin-panel';
                    }, 1000);
                } else {
                    const errorData = await response.json();
                    showError(errorData.error || '登入失敗');
                }
            } catch (error) {
                console.error('登入錯誤:', error);
                showError('網絡錯誤，請稍後重試');
            }
        });
        
        function showError(message) {
            const errorDiv = document.getElementById('loginError');
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
            toastr.error(message);
        }
        
        // 頁面載入時檢查是否已經登入
        document.addEventListener('DOMContentLoaded', function() {
            const token = localStorage.getItem('admin_token');
            if (token) {
                // 驗證現有 Token 是否仍然有效
                fetch('/api/validate-token', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ token: token })
                })
                .then(response => {
                    if (response.ok) {
                        // Token 仍然有效，直接跳轉到管理面板
                        window.location.href = '/admin-panel';
                    } else {
                        // Token 無效，清除並保持在登入頁面
                        localStorage.removeItem('admin_token');
                    }
                })
                .catch(error => {
                    console.error('Token 驗證錯誤:', error);
                    localStorage.removeItem('admin_token');
                });
            }
        });
    </script>
</body>
</html>
