<!DOCTYPE html>
<html lang="zh-TW">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="預約專業金屬回收服務，包含黃金、白銀、銅、鋁、不鏽鋼等金屬回收諮詢">
    <meta name="keywords" content="回收預約, 金屬回收, 黃金回收, 白銀回收, 銅回收, 鋁回收, 不鏽鋼回收">
    <meta name="author" content="迪奕科技">
    <title>回收預約 - 迪奕科技金屬回收服務</title>

    <!-- Bulma CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='vendor/bulma/css/bulma.min.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/navbar.css') }}">
    
    <!-- 自定義樣式 -->
    <style>
        /* 自定義 Bulma 導航樣式 */
        .navbar {
            background-color: #2c3e50 !important;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 99999;
            box-shadow: 0 2px 10px rgba(44, 62, 80, 0.1);
        }
        
        .navbar-brand .navbar-item {
            font-size: 20px;
            font-weight: 700;
            color: #D4AF37 !important;
        }
        
        .navbar-item {
            color: #ecf0f1 !important;
            font-weight: 500;
        }
        
        .navbar-item:hover,
        .navbar-item.is-active {
            background-color: #D4AF37 !important;
            color: white !important;
        }
        
        .navbar-burger {
            color: #ecf0f1 !important;
            border: 2px solid #ecf0f1;
            border-radius: 4px;
            width: 44px;
            height: 44px;
        }
        
        .navbar-burger span {
            background-color: #ecf0f1 !important;
        }
        
        .navbar-menu {
            background-color: #2c3e50 !important;
            box-shadow: 0 4px 16px rgba(44, 62, 80, 0.2);
        }
        
        @media screen and (max-width: 1023px) {
            .navbar-menu {
                border-top: 1px solid #95a5a6;
            }
            
            .navbar-item {
                padding: 16px 24px;
                font-size: 18px;
            }
        }
        
        /* 主要內容樣式 */
        main {
            margin-top: 3.25rem; /* Bulma navbar 高度 */
            padding: 20px;
        }
        
        .page-header {
            background: linear-gradient(135deg, #D4AF37 0%, #B8860B 100%);
            color: white;
            padding: 40px 20px;
            text-align: center;
            border-radius: 10px;
            margin-bottom: 40px;
        }
        
        .page-header h1 {
            font-size: 2.5rem;
            margin-bottom: 20px;
        }
        
        .page-header p {
            font-size: 1.2rem;
            margin-bottom: 15px;
        }
        
        .booking-container {
            max-width: 800px;
            margin: 0 auto;
        }
        
        .booking-form {
            background: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(44, 62, 80, 0.1);
            margin-bottom: 30px;
        }
        
        .form-section {
            margin-bottom: 30px;
        }
        
        .form-section h3 {
            color: #2c3e50;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #D4AF37;
        }
        
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group.full-width {
            grid-column: 1 / -1;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #2c3e50;
            font-weight: 500;
        }
        
        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #95a5a6;
            border-radius: 6px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        
        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #D4AF37;
        }
        
        .form-group textarea {
            height: 120px;
            resize: vertical;
        }
        
        .service-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .service-option {
            display: flex;
            align-items: center;
            padding: 15px;
            border: 2px solid #95a5a6;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .service-option:hover {
            border-color: #D4AF37;
            background-color: #ecf0f1;
        }
        
        .service-option input[type="checkbox"] {
            margin-right: 10px;
            width: auto;
        }
        
        .service-option label {
            margin: 0;
            cursor: pointer;
            font-weight: normal;
            color: #2c3e50;
        }
        
        .submit-btn {
            background: linear-gradient(135deg, #D4AF37 0%, #B8860B 100%);
            color: white;
            padding: 15px 40px;
            border: none;
            border-radius: 6px;
            font-size: 18px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s;
            width: 100%;
        }
        
        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(44, 62, 80, 0.2);
        }
        
        .contact-info {
            background: #ecf0f1;
            padding: 30px;
            border-radius: 10px;
            text-align: center;
        }
        
        .contact-info h3 {
            color: #2c3e50;
            margin-bottom: 20px;
        }
        
        .contact-item {
            margin: 15px 0;
            color: #95a5a6;
        }
        
        .contact-item strong {
            color: #2c3e50;
        }
        
        /* 聯絡資訊區域樣式 - 參考 contact.html */
        .contact-info-section {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(44, 62, 80, 0.1);
            margin-bottom: 30px;
        }
        
        .contact-items-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-top: 20px;
        }
        
        .contact-info-section h3 {
            color: #D4AF37;
            margin-bottom: 25px;
            padding-bottom: 10px;
            border-bottom: 2px solid #D4AF37;
            font-size: 1.5rem;
            font-weight: bold;
            text-align: left;
        }
        
        .contact-info-section .contact-item {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            padding: 15px;
            background: #ecf0f1;
            border-radius: 8px;
            transition: all 0.3s;
            text-align: left;
        }
        
        .contact-info-section .contact-item:hover {
            background: #D4AF37;
            transform: translateX(5px);
        }
        
        .contact-info-section .contact-item:hover .contact-details h4,
        .contact-info-section .contact-item:hover .contact-details p {
            color: white !important;
        }
        
        .contact-icon {
            font-size: 1.5rem;
            margin-right: 15px;
            color: #D4AF37;
            min-width: 30px;
        }
        
        .contact-details h4 {
            margin: 0 0 5px 0;
            color: #D4AF37 !important;
            font-size: 1.1rem;
            font-weight: bold;
        }
        
        .contact-details p {
            margin: 0;
            color: #000000 !important;
            line-height: 1.4;
        }
        
        .contact-details a {
            color: #000000;
            text-decoration: none;
            font-weight: 500;
        }
        
        .contact-details a:hover {
            text-decoration: underline;
            color: #2c3e50;
        }
        
        /* 移動端響應式 */
        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
                gap: 0;
            }
            
            .booking-form {
                padding: 20px;
            }
            
            .service-options {
                grid-template-columns: 1fr;
            }
            
            .contact-items-grid {
                grid-template-columns: 1fr;
                gap: 10px;
            }
        }
        
    </style>
</head>

<body>

    <!-- Bulma 導航選單 -->
    {% set active_page = 'booking' %}
    {% include 'partials/navbar.html' %}

    <!-- 主要內容 -->
    <main>
        <!-- 頁面標題 -->
        <section class="page-header">
            <h1>回收預約</h1>
            <p>專業的金屬回收服務，為您的金屬回收需求提供專業建議</p>
            <p>填寫下方表單，我們將盡快與您聯繫安排回收時間</p>
        </section>

        <div class="booking-container">
            <!-- 消息提示區域 -->
            <div id="messageContainer"></div>

            <!-- 預約表單 -->
            <form class="booking-form" id="bookingForm">
                <!-- 基本資料 -->
                <div class="form-section">
                    <h3>📋 基本資料</h3>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="name">姓名 *</label>
                            <input type="text" id="name" name="name" required>
                        </div>
                        <div class="form-group">
                            <label for="phone">聯絡電話 *</label>
                            <input type="tel" id="phone" name="phone" required>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="email">電子郵件 *</label>
                            <input type="email" id="email" name="email" required>
                        </div>
                        <div class="form-group">
                            <label for="address">回收地址 *</label>
                            <input type="text" id="address" name="address" required placeholder="請提供詳細地址">
                        </div>
                    </div>
                </div>

                <!-- 回收金屬資訊 -->
                <div class="form-section">
                    <h3>🏆 回收金屬資訊</h3>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="metal_type">金屬類型 *</label>
                            <input type="text" id="metal_type" name="metal_type" required placeholder="請輸入金屬類型（例如：黃金、白銀、銅、鋁、不鏽鋼等）">
                        </div>
                        <div class="form-group">
                            <label for="quantity">預估數量/重量 *</label>
                            <input type="text" id="quantity" name="quantity" required placeholder="例如：5公斤、10件、約2箱">
                        </div>
                    </div>
                </div>

                <!-- 預約時間 -->
                <div class="form-section">
                    <h3>📅 預約時間</h3>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="preferred_date">偏好日期 *</label>
                            <input type="date" id="preferred_date" name="preferred_date" required>
                        </div>
                        <div class="form-group">
                            <label for="preferred_time">偏好時段</label>
                            <select id="preferred_time" name="preferred_time">
                                <option value="">請選擇時段（可選）</option>
                                <option value="09:00-12:00">上午 (11:00-13:00)</option>
                                <option value="13:00-17:00">下午 (13:00-16:00)</option>
                                
                            </select>
                        </div>
                    </div>
                </div>

                <!-- 備註 -->
                <div class="form-section">
                    <h3>💬 備註說明</h3>
                    <div class="form-group full-width">
                        <label for="notes">其他說明（可選）</label>
                        <textarea id="notes" name="notes" placeholder="請描述金屬的狀況、特殊要求或其他需要說明的事項..."></textarea>
                    </div>
                </div>

                <!-- 人機驗證 -->
                <div class="form-section" id="captchaContainer" style="display: none;">
                    <h3>🔐 人機驗證</h3>
                    <div class="form-row">
                        <div class="form-group" style="flex: 2;">
                            <label id="captchaQuestion">請計算：載入中...</label>
                            <input type="number" id="captchaAnswer" name="captchaAnswer" placeholder="請輸入答案">
                        </div>
                        <div class="form-group" style="flex: 1; display: flex; align-items: flex-end;">
                            <button type="button" id="refreshCaptcha" class="submit-btn" style="background-color: #95a5a6; width: 100%; white-space: nowrap;">
                                🔄 重新載入
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 提交按鈕 -->
                <button type="submit" class="submit-btn">
                    <i class="fas fa-paper-plane"></i> 提交預約
                </button>
            </form>

            <!-- 聯絡資訊 -->
            <section class="contact-info-section">
                <h3>📞 聯絡資訊</h3>
                
                <div class="contact-items-grid">
                    <div class="contact-item">
                        <div class="contact-icon">📱</div>
                        <div class="contact-details">
                            <h4>客服專線</h4>
                            <p><a href="tel:+886422202326">04-22202326</a></p>
                        </div>
                    </div>
                    
                    <div class="contact-item">
                        <div class="contact-icon">📧</div>
                        <div class="contact-details">
                            <h4>電子郵件</h4>
                            <p><a href="mailto:<EMAIL>"><EMAIL></a></p>
                        </div>
                    </div>
                    
                    <div class="contact-item">
                        <div class="contact-icon">📍</div>
                        <div class="contact-details">
                            <h4>門市地址</h4>
                            <p>台中市中區光復里臺灣大道一段311號一樓</p>
                        </div>
                    </div>
                    
                    <div class="contact-item">
                        <div class="contact-icon">🕒</div>
                        <div class="contact-details">
                            <h4>服務時間</h4>
                            <p>週一至週五<br>10:00 ～ 17:00</p>
                        </div>
                    </div>
                </div>
            </section>
        </div>
    </main>

    {% include 'partials/footer.html' %}

    <!-- Font Awesome -->
    <script src="{{ url_for('static', filename='vendor/fontawesome/js/all.min.js') }}"></script>

    <!-- Bootstrap JS -->
    <script src="{{ url_for('static', filename='vendor/bootstrap/js/bootstrap.bundle.min.js') }}"></script>

    <!-- jQuery (toastr 需要) -->
    <script src="{{ url_for('static', filename='vendor/jquery/jquery.min.js') }}"></script>

    <!-- Toastr CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='vendor/toastr/css/toastr.min.css') }}">

    <!-- Toastr JS -->
    <script src="{{ url_for('static', filename='vendor/toastr/js/toastr.min.js') }}"></script>

    <!-- 回收預約表單處理器 -->
    <script src="/static/js/booking-form.js"></script>

    <!-- Bulma 導航 JavaScript -->
    <script>
        
        // 表單提交現在由 booking-form.js 處理
        
        // 等待DOM完全載入
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 初始化 Bulma 導航和預約表單系統...');
            
            // 初始化 Bulma navbar
            const navbarBurger = document.querySelector('.navbar-burger');
            const navbarMenu = document.getElementById('navbarMenu');
            const bookingForm = document.getElementById('bookingForm');
            
            if (navbarBurger && navbarMenu) {
                // 漢堡選單點擊事件
                navbarBurger.addEventListener('click', function() {
                    console.log('🖱️ Bulma 漢堡選單被點擊');
                    
                    // 切換活動狀態
                    navbarBurger.classList.toggle('is-active');
                    navbarMenu.classList.toggle('is-active');
                    
                    // 更新 aria 屬性
                    const isExpanded = navbarBurger.classList.contains('is-active');
                    navbarBurger.setAttribute('aria-expanded', isExpanded);
                    
                    console.log(isExpanded ? '🔓 導航已開啟' : '🔒 導航已關閉');
                });
                
                // 點擊選單項目時關閉手機版選單
                const navbarItems = navbarMenu.querySelectorAll('.navbar-item');
                navbarItems.forEach(item => {
                    item.addEventListener('click', function() {
                        if (navbarMenu.classList.contains('is-active')) {
                            console.log('🔗 選單項目被點擊，關閉手機版選單');
                            navbarBurger.classList.remove('is-active');
                            navbarMenu.classList.remove('is-active');
                            navbarBurger.setAttribute('aria-expanded', 'false');
                        }
                        
                        // 更新活動狀態
                        navbarItems.forEach(nav => nav.classList.remove('is-active'));
                        this.classList.add('is-active');
                    });
                });
                
                console.log('✅ Bulma 導航系統初始化完成');
            } else {
                console.log('❌ 找不到 Bulma 導航元素');
            }
            
            // 表單提交事件現在由 booking-form.js 處理
            
            console.log('✅ 預約服務頁面系統初始化完成');
        });
    </script>
</body>

</html>