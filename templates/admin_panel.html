<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理員控制台 - 金屬價格管理系統</title>
    
    <!-- Bootstrap CSS -->
    <link href="{{ url_for('static', filename='vendor/bootstrap/css/bootstrap.min.css') }}" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="{{ url_for('static', filename='vendor/fontawesome/css/all.min.css') }}">

    <!-- Toastr CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='vendor/toastr/css/toastr.min.css') }}">
    
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .header {
            background: linear-gradient(135deg, #D4AF37, #B8860B);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
        }
        
        .price-card {
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 1.5rem;
            margin-bottom: 1rem;
            border-left: 4px solid #D4AF37;
        }
        
        .price-value {
            font-size: 1.5rem;
            font-weight: bold;
            color: #D4AF37;
        }
        
        .form-container {
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 2rem;
        }
        
        .btn-gold {
            background-color: #D4AF37;
            border-color: #D4AF37;
            color: white;
        }
        
        .btn-gold:hover {
            background-color: #B8860B;
            border-color: #B8860B;
            color: white;
        }
        
        .alert {
            border-radius: 8px;
        }
        
        .form-label {
            font-weight: 600;
            color: #495057;
        }
        
        .form-control:focus {
            border-color: #D4AF37;
            box-shadow: 0 0 0 0.2rem rgba(212, 175, 55, 0.25);
        }
        
        .update-time {
            color: #6c757d;
            font-size: 0.9rem;
        }

        /* 管理員表格樣式 */
        .admin-tables-left {
            padding-right: 15px;
        }

        .admin-tables-right {
            padding-left: 15px;
        }

        .admin-price-table-container {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
            overflow: hidden;
            border: 1px solid #e9ecef;
        }

        .admin-table-header {
            background: linear-gradient(135deg, #D4AF37 0%, #B8860B 100%);
            color: white;
            padding: 12px 16px;
            margin: 0;
        }

        .admin-table-header h4 {
            margin: 0;
            font-size: 1rem;
            font-weight: 600;
        }

        .admin-table-header small {
            opacity: 0.9;
            font-size: 0.8rem;
        }

        .admin-price-table {
            display: flex;
            flex-direction: column;
        }

        .admin-table-row {
            display: flex;
            border-bottom: 1px solid #e9ecef;
            transition: background-color 0.2s ease;
        }

        .admin-table-row:hover {
            background-color: #f8f9fa;
        }

        .admin-table-row:last-child {
            border-bottom: none;
        }

        .admin-table-header-row {
            background-color: #f8f9fa;
            font-weight: 600;
            color: #2c3e50;
        }

        .admin-table-header-row:hover {
            background-color: #f8f9fa;
        }

        .admin-table-cell {
            flex: 1;
            padding: 8px 6px;
            text-align: center;
            font-size: 0.85rem;
            border-right: 1px solid #e9ecef;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 45px;
        }

        .admin-table-cell:last-child {
            border-right: none;
        }

        .admin-metal-name {
            font-weight: 600;
            color: #2c3e50;
            background-color: #f8f9fa;
        }

        .admin-table-cell .form-control-sm {
            font-size: 0.8rem;
            padding: 4px 6px;
            height: auto;
            min-height: 28px;
        }

        .admin-table-cell .form-check {
            margin: 0;
            display: flex;
            justify-content: center;
        }

        .admin-table-cell .form-check-input {
            margin: 0;
        }

        /* 響應式設計 */
        @media screen and (max-width: 1199px) {
            .admin-tables-left,
            .admin-tables-right {
                padding: 0;
            }

            .admin-table-cell {
                font-size: 0.75rem;
                padding: 6px 4px;
                min-height: 40px;
            }

            .admin-table-header h4 {
                font-size: 0.9rem;
            }
        }

        @media screen and (max-width: 991px) {
            .admin-table-cell {
                font-size: 0.7rem;
                padding: 4px 2px;
                min-height: 35px;
            }

            /* 在平板上隱藏部分欄位 */
            .admin-table-row .admin-table-cell:nth-child(4),
            .admin-table-row .admin-table-cell:nth-child(5) {
                display: none;
            }
        }

        @media screen and (max-width: 767px) {
            /* 手機端進一步簡化 */
            .admin-table-cell {
                font-size: 0.65rem;
                padding: 3px 1px;
                min-height: 30px;
            }
        }

        /* TwelveData 控制面板樣式 */
        .card-header {
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
            color: white;
            border-bottom: none;
        }

        .badge {
            font-size: 0.8rem;
        }

        #apiLogContainer {
            font-family: 'Courier New', monospace;
            font-size: 0.75rem;
            line-height: 1.2;
        }

        .log-entry {
            margin-bottom: 2px;
            padding: 2px 0;
        }

        .log-success {
            color: #28a745;
        }

        .log-error {
            color: #dc3545;
        }

        .log-warning {
            color: #ffc107;
        }

        .log-info {
            color: #17a2b8;
        }

        /* 特殊輸入欄位樣式 */
        .multiplier-input {
            background-color: #fff3cd;
            border-color: #ffeaa7;
        }

        .multiplier-input:focus {
            background-color: #fff3cd;
            border-color: #D4AF37;
            box-shadow: 0 0 0 0.2rem rgba(212, 175, 55, 0.25);
        }

        .frequency-input {
            background-color: #d1ecf1;
            border-color: #bee5eb;
        }

        .frequency-input:focus {
            background-color: #d1ecf1;
            border-color: #17a2b8;
            box-shadow: 0 0 0 0.2rem rgba(23, 162, 184, 0.25);
        }

        /* 開關樣式增強 */
        .form-check-input:checked {
            background-color: #D4AF37;
            border-color: #D4AF37;
        }

        .auto-update-switch .form-check-input:checked {
            background-color: #28a745;
            border-color: #28a745;
        }
    </style>
</head>
<body>
    <!-- 標題區域 -->
    <div class="header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <a href="/market-data.html" title="回到前台金屬價格" style="color: #fff;text-decoration: none;">
                        <h1><i class="fas fa-coins me-3"></i>金屬價格管理系統</h1>
                    </a>
                    <p class="mb-0">管理員控制台 - 價格與系統管理</p>
                </div>
                <div class="col-md-4 text-end">
                    <button class="btn btn-outline-light btn-sm me-3" onclick="logout()">
                        <i class="fas fa-sign-out-alt me-1"></i>登出
                    </button>
                    <div class="update-time">
                        <i class="fas fa-clock me-1"></i>
                        最後更新：<span id="lastUpdateTime">載入中...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- 消息提示 -->
        <div id="messageContainer"></div>

        <!-- 價格管理區域 -->
        <div class="row">
            <div class="col-12">
                <h3><i class="fas fa-chart-line me-2"></i>價格管理</h3>

                <!-- 主要布局容器：左右並排 -->
                <div class="row">
                    <!-- 左側 2/3 區塊縮一點改7 -->
                    <div class="col-lg-7">
                        <div class="admin-tables-left">
                            <!-- 貴金屬管理表格 -->
                            <div class="admin-price-table-container">
                                <div class="admin-table-header">
                                    <h4>貴金屬 (美元) 管理</h4>
                                    <small class="text-muted">基礎價格以美元/盎司為準</small>
                                </div>
                                <div class="admin-price-table">
                                    <!-- 表頭 -->
                                    <div class="admin-table-row admin-table-header-row">
                                        <div class="admin-table-cell">金屬</div>
                                        <div class="admin-table-cell">自動更新</div>
                                        <div class="admin-table-cell">基礎價格 ($/oz)</div>
                                        <div class="admin-table-cell">買入倍率</div>
                                        <div class="admin-table-cell">賣出倍率</div>
                                        <div class="admin-table-cell">啟用</div>
                                    </div>
                                    <!-- 貴金屬數據行 -->
                                    <div class="admin-table-row" data-metal="gold" data-type="metals">
                                        <div class="admin-table-cell admin-metal-name">金 Au</div>
                                        <div class="admin-table-cell">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input auto-update-switch" type="checkbox" id="gold_auto_update">
                                            </div>
                                        </div>
                                        <div class="admin-table-cell">
                                            <input type="number" class="form-control form-control-sm base-price-input"
                                                   id="gold_base_price" step="0.01" min="0">
                                        </div>
                                        <div class="admin-table-cell">
                                            <input type="number" class="form-control form-control-sm multiplier-input"
                                                   id="gold_buy_mult" step="0.001" min="0" max="2" value="0.98">
                                        </div>
                                        <div class="admin-table-cell">
                                            <input type="number" class="form-control form-control-sm multiplier-input"
                                                   id="gold_sell_mult" step="0.001" min="0" max="2" value="1.02">
                                        </div>
                                        <div class="admin-table-cell">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input enabled-switch" type="checkbox" id="gold_enabled" checked>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="admin-table-row" data-metal="silver" data-type="metals">
                                        <div class="admin-table-cell admin-metal-name">銀 Ag</div>
                                        <div class="admin-table-cell">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input auto-update-switch" type="checkbox" id="silver_auto_update">
                                            </div>
                                        </div>
                                        <div class="admin-table-cell">
                                            <input type="number" class="form-control form-control-sm base-price-input"
                                                   id="silver_base_price" step="0.01" min="0">
                                        </div>
                                        <div class="admin-table-cell">
                                            <input type="number" class="form-control form-control-sm multiplier-input"
                                                   id="silver_buy_mult" step="0.001" min="0" max="2" value="0.98">
                                        </div>
                                        <div class="admin-table-cell">
                                            <input type="number" class="form-control form-control-sm multiplier-input"
                                                   id="silver_sell_mult" step="0.001" min="0" max="2" value="1.02">
                                        </div>
                                        <div class="admin-table-cell">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input enabled-switch" type="checkbox" id="silver_enabled" checked>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="admin-table-row" data-metal="palladium" data-type="metals">
                                        <div class="admin-table-cell admin-metal-name">鈀 Pd</div>
                                        <div class="admin-table-cell">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input auto-update-switch" type="checkbox" id="palladium_auto_update">
                                            </div>
                                        </div>
                                        <div class="admin-table-cell">
                                            <input type="number" class="form-control form-control-sm base-price-input"
                                                   id="palladium_base_price" step="0.01" min="0">
                                        </div>
                                        <div class="admin-table-cell">
                                            <input type="number" class="form-control form-control-sm multiplier-input"
                                                   id="palladium_buy_mult" step="0.001" min="0" max="2" value="0.95">
                                        </div>
                                        <div class="admin-table-cell">
                                            <input type="number" class="form-control form-control-sm multiplier-input"
                                                   id="palladium_sell_mult" step="0.001" min="0" max="2" value="1.05">
                                        </div>
                                        <div class="admin-table-cell">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input enabled-switch" type="checkbox" id="palladium_enabled" checked>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="admin-table-row" data-metal="platinum" data-type="metals">
                                        <div class="admin-table-cell admin-metal-name">鉑 Pt</div>
                                        <div class="admin-table-cell">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input auto-update-switch" type="checkbox" id="platinum_auto_update">
                                            </div>
                                        </div>
                                        <div class="admin-table-cell">
                                            <input type="number" class="form-control form-control-sm base-price-input"
                                                   id="platinum_base_price" step="0.01" min="0">
                                        </div>
                                        <div class="admin-table-cell">
                                            <input type="number" class="form-control form-control-sm multiplier-input"
                                                   id="platinum_buy_mult" step="0.001" min="0" max="2" value="0.96">
                                        </div>
                                        <div class="admin-table-cell">
                                            <input type="number" class="form-control form-control-sm multiplier-input"
                                                   id="platinum_sell_mult" step="0.001" min="0" max="2" value="1.04">
                                        </div>
                                        <div class="admin-table-cell">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input enabled-switch" type="checkbox" id="platinum_enabled" checked>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 商品/台幣管理表格 -->
                            <div class="admin-price-table-container">
                                <div class="admin-table-header">
                                    <h4>商品/台幣管理</h4>
                                    <small class="text-muted">基礎價格以台幣/台錢為準</small>
                                </div>
                                <div class="admin-price-table">
                                    <!-- 表頭 -->
                                    <div class="admin-table-row admin-table-header-row">
                                        <div class="admin-table-cell">商品</div>
                                        <div class="admin-table-cell">自動更新</div>
                                        <div class="admin-table-cell">基礎價格<br>(NT$/錢)</div>
                                        <div class="admin-table-cell">買入倍率</div>
                                        <div class="admin-table-cell">賣出倍率</div>
                                        <div class="admin-table-cell">啟用</div>
                                    </div>
                                    <!-- 台幣數據行 -->
                                    <div class="admin-table-row" data-metal="gold_twd" data-type="metals_twd">
                                        <div class="admin-table-cell admin-metal-name">金 Au</div>
                                        <div class="admin-table-cell">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input auto-update-switch" type="checkbox" id="gold_twd_auto_update"
                                                       onchange="toggleBasePriceReadonly('gold_twd_base_price', this.checked)">
                                            </div>
                                        </div>
                                        <div class="admin-table-cell">
                                            <input type="number" class="form-control form-control-sm base-price-input"
                                                   id="gold_twd_base_price" step="0.01" min="0" placeholder="手動輸入價格">
                                        </div>
                                        <div class="admin-table-cell">
                                            <input type="number" class="form-control form-control-sm multiplier-input"
                                                   id="gold_twd_buy_mult" step="0.001" min="0" max="2" value="0.98">
                                        </div>
                                        <div class="admin-table-cell">
                                            <input type="number" class="form-control form-control-sm multiplier-input"
                                                   id="gold_twd_sell_mult" step="0.001" min="0" max="2" value="1.02">
                                        </div>
                                        <div class="admin-table-cell">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input enabled-switch" type="checkbox" id="gold_twd_enabled" checked>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="admin-table-row" data-metal="silver_twd" data-type="metals_twd">
                                        <div class="admin-table-cell admin-metal-name">銀 Ag</div>
                                        <div class="admin-table-cell">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input auto-update-switch" type="checkbox" id="silver_twd_auto_update">
                                            </div>
                                        </div>
                                        <div class="admin-table-cell">
                                            <input type="number" class="form-control form-control-sm base-price-input"
                                                   id="silver_twd_base_price" step="1" min="0">
                                        </div>
                                        <div class="admin-table-cell">
                                            <input type="number" class="form-control form-control-sm multiplier-input"
                                                   id="silver_twd_buy_mult" step="0.001" min="0" max="2" value="0.98">
                                        </div>
                                        <div class="admin-table-cell">
                                            <input type="number" class="form-control form-control-sm multiplier-input"
                                                   id="silver_twd_sell_mult" step="0.001" min="0" max="2" value="1.02">
                                        </div>
                                        <div class="admin-table-cell">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input enabled-switch" type="checkbox" id="silver_twd_enabled" checked>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="admin-table-row" data-metal="palladium_twd" data-type="metals_twd">
                                        <div class="admin-table-cell admin-metal-name">鈀 Pd</div>
                                        <div class="admin-table-cell">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input auto-update-switch" type="checkbox" id="palladium_twd_auto_update">
                                            </div>
                                        </div>
                                        <div class="admin-table-cell">
                                            <input type="number" class="form-control form-control-sm base-price-input"
                                                   id="palladium_twd_base_price" step="1" min="0">
                                        </div>
                                        <div class="admin-table-cell">
                                            <input type="number" class="form-control form-control-sm multiplier-input"
                                                   id="palladium_twd_buy_mult" step="0.001" min="0" max="2" value="0.95">
                                        </div>
                                        <div class="admin-table-cell">
                                            <input type="number" class="form-control form-control-sm multiplier-input"
                                                   id="palladium_twd_sell_mult" step="0.001" min="0" max="2" value="1.05">
                                        </div>
                                        <div class="admin-table-cell">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input enabled-switch" type="checkbox" id="palladium_twd_enabled" checked>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="admin-table-row" data-metal="platinum_twd" data-type="metals_twd">
                                        <div class="admin-table-cell admin-metal-name">鉑 Pt</div>
                                        <div class="admin-table-cell">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input auto-update-switch" type="checkbox" id="platinum_twd_auto_update">
                                            </div>
                                        </div>
                                        <div class="admin-table-cell">
                                            <input type="number" class="form-control form-control-sm base-price-input"
                                                   id="platinum_twd_base_price" step="1" min="0">
                                        </div>
                                        <div class="admin-table-cell">
                                            <input type="number" class="form-control form-control-sm multiplier-input"
                                                   id="platinum_twd_buy_mult" step="0.001" min="0" max="2" value="0.96">
                                        </div>
                                        <div class="admin-table-cell">
                                            <input type="number" class="form-control form-control-sm multiplier-input"
                                                   id="platinum_twd_sell_mult" step="0.001" min="0" max="2" value="1.04">
                                        </div>
                                        <div class="admin-table-cell">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input enabled-switch" type="checkbox" id="platinum_twd_enabled" checked>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="mt-2">
                                    <small class="text-muted">
                                        <strong>計算說明：</strong>台錢基礎價格 = 美元價格 × 美元匯率 × 台錢轉換係數 (31.1035g/oz ÷ 3.75g/錢)
                                    </small>
                                </div>
                            </div>

                            <!-- 其他材料管理表格（公斤報價） -->
                            <div class="admin-price-table-container">
                                <div class="admin-table-header">
                                    <h4>其他材料管理（公斤報價）</h4>
                                    <small class="text-muted">基礎價格以台幣/公斤為準</small>
                                </div>
                                <div class="admin-price-table">
                                    <!-- 表頭 -->
                                    <div class="admin-table-row admin-table-header-row">
                                        <div class="admin-table-cell">項目</div>
                                        <div class="admin-table-cell">自動更新</div>
                                        <div class="admin-table-cell">基礎價格 (NT$/kg)</div>
                                        <div class="admin-table-cell">買入倍率</div>
                                        <div class="admin-table-cell">賣出倍率</div>
                                        <div class="admin-table-cell">啟用</div>
                                    </div>
                                    <!-- 其他材料數據行 -->
                                    <div class="admin-table-row" data-metal="cobalt_kg" data-type="other_materials">
                                        <div class="admin-table-cell admin-metal-name">鈷 Co</div>
                                        <div class="admin-table-cell">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input auto-update-switch" type="checkbox" id="cobalt_kg_auto_update"
                                                       onchange="toggleBasePriceReadonly('cobalt_kg_base_price', this.checked)">
                                            </div>
                                        </div>
                                        <div class="admin-table-cell">
                                            <input type="number" class="form-control form-control-sm base-price-input"
                                                   id="cobalt_kg_base_price" step="0.01" min="0" placeholder="手動輸入價格">
                                        </div>
                                        <div class="admin-table-cell">
                                            <input type="number" class="form-control form-control-sm multiplier-input"
                                                   id="cobalt_kg_buy_mult" step="0.001" min="0" max="2" value="0.90">
                                        </div>
                                        <div class="admin-table-cell">
                                            <input type="number" class="form-control form-control-sm multiplier-input"
                                                   id="cobalt_kg_sell_mult" step="0.001" min="0" max="2" value="1.10">
                                        </div>
                                        <div class="admin-table-cell">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input enabled-switch" type="checkbox" id="cobalt_kg_enabled" checked>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="admin-table-row" data-metal="tin_kg" data-type="other_materials">
                                        <div class="admin-table-cell admin-metal-name">錫 Sn</div>
                                        <div class="admin-table-cell">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input auto-update-switch" type="checkbox" id="tin_kg_auto_update"
                                                       onchange="toggleBasePriceReadonly('tin_kg_base_price', this.checked)">
                                            </div>
                                        </div>
                                        <div class="admin-table-cell">
                                            <input type="number" class="form-control form-control-sm base-price-input"
                                                   id="tin_kg_base_price" step="0.01" min="0" placeholder="手動輸入價格">
                                        </div>
                                        <div class="admin-table-cell">
                                            <input type="number" class="form-control form-control-sm multiplier-input"
                                                   id="tin_kg_buy_mult" step="0.001" min="0" max="2" value="0.92">
                                        </div>
                                        <div class="admin-table-cell">
                                            <input type="number" class="form-control form-control-sm multiplier-input"
                                                   id="tin_kg_sell_mult" step="0.001" min="0" max="2" value="1.08">
                                        </div>
                                        <div class="admin-table-cell">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input enabled-switch" type="checkbox" id="tin_kg_enabled" checked>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="mt-2">
                                    <small class="text-muted">
                                        <strong>計算說明：</strong>公斤基礎價格 = 美元價格 × 美元匯率 × 單位轉換係數 (1磅 = 0.453592公斤)
                                    </small>
                                </div>
                            </div>

                            <!-- 貨幣項目管理表格 -->
                            <div class="admin-price-table-container">
                                <div class="admin-table-header">
                                    <h4>貨幣項目管理</h4>
                                    <small class="text-muted">供參考無外幣買賣</small>
                                </div>
                                <div class="admin-price-table">
                                    <!-- 表頭 -->
                                    <div class="admin-table-row admin-table-header-row">
                                        <div class="admin-table-cell">貨幣</div>
                                        <div class="admin-table-cell">自動更新</div>
                                        <div class="admin-table-cell">基礎匯率 <br>(1外幣=X台幣)</div>
                                        <div class="admin-table-cell">買入倍率</div>
                                        <div class="admin-table-cell">賣出倍率</div>
                                        <div class="admin-table-cell">啟用</div>
                                    </div>
                                    <!-- 貨幣數據行 -->
                                    <div class="admin-table-row" data-currency="usd" data-type="currencies">
                                        <div class="admin-table-cell admin-metal-name">美元 USD</div>
                                        <div class="admin-table-cell">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input auto-update-switch" type="checkbox" id="usd_auto_update"
                                                       onchange="toggleCurrencyRateReadonly('usd_rate', this.checked)">
                                            </div>
                                        </div>
                                        <div class="admin-table-cell">
                                            <input type="number" class="form-control form-control-sm base-price-input"
                                                   id="usd_rate" step="0.01" min="0" value="31.00" placeholder="31.00">
                                        </div>
                                        <div class="admin-table-cell">
                                            <input type="number" class="form-control form-control-sm multiplier-input"
                                                   id="usd_buy_mult" step="0.001" min="0" max="2" value="0.998">
                                        </div>
                                        <div class="admin-table-cell">
                                            <input type="number" class="form-control form-control-sm multiplier-input"
                                                   id="usd_sell_mult" step="0.001" min="0" max="2" value="1.002">
                                        </div>
                                        <div class="admin-table-cell">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input enabled-switch" type="checkbox" id="usd_enabled" checked>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="admin-table-row" data-currency="cny" data-type="currencies">
                                        <div class="admin-table-cell admin-metal-name">人民幣 CNY</div>
                                        <div class="admin-table-cell">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input auto-update-switch" type="checkbox" id="cny_auto_update"
                                                       onchange="toggleCurrencyRateReadonly('cny_rate', this.checked)">
                                            </div>
                                        </div>
                                        <div class="admin-table-cell">
                                            <input type="number" class="form-control form-control-sm base-price-input"
                                                   id="cny_rate" step="0.01" min="0" value="4.50" placeholder="4.50">
                                        </div>
                                        <div class="admin-table-cell">
                                            <input type="number" class="form-control form-control-sm multiplier-input"
                                                   id="cny_buy_mult" step="0.001" min="0" max="2" value="0.995">
                                        </div>
                                        <div class="admin-table-cell">
                                            <input type="number" class="form-control form-control-sm multiplier-input"
                                                   id="cny_sell_mult" step="0.001" min="0" max="2" value="1.005">
                                        </div>
                                        <div class="admin-table-cell">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input enabled-switch" type="checkbox" id="cny_enabled" checked>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="admin-table-row" data-currency="eur" data-type="currencies">
                                        <div class="admin-table-cell admin-metal-name">歐元 EUR</div>
                                        <div class="admin-table-cell">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input auto-update-switch" type="checkbox" id="eur_auto_update"
                                                       onchange="toggleCurrencyRateReadonly('eur_rate', this.checked)">
                                            </div>
                                        </div>
                                        <div class="admin-table-cell">
                                            <input type="number" class="form-control form-control-sm base-price-input"
                                                   id="eur_rate" step="0.01" min="0" value="35.00" placeholder="35.00">
                                        </div>
                                        <div class="admin-table-cell">
                                            <input type="number" class="form-control form-control-sm multiplier-input"
                                                   id="eur_buy_mult" step="0.001" min="0" max="2" value="0.997">
                                        </div>
                                        <div class="admin-table-cell">
                                            <input type="number" class="form-control form-control-sm multiplier-input"
                                                   id="eur_sell_mult" step="0.001" min="0" max="2" value="1.003">
                                        </div>
                                        <div class="admin-table-cell">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input enabled-switch" type="checkbox" id="eur_enabled" checked>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 右側 1/3 區塊加一點改成5 -->
                    <div class="col-lg-5">
                        <div class="admin-tables-right">
                            <!-- 實物交易管理表格（8種金屬，公克報價） -->
                            <div class="admin-price-table-container">
                                <div class="admin-table-header">
                                    <h4>實物交易管理（公克報價）</h4>
                                    <small class="text-muted">基礎價格以台幣/公克為準</small>
                                </div>
                                <div class="admin-price-table">
                                    <!-- 表頭 -->
                                    <div class="admin-table-row admin-table-header-row">
                                        <div class="admin-table-cell">項目</div>
                                        <div class="admin-table-cell">基礎價格 (NT$/g)</div>
                                        <div class="admin-table-cell">買入倍率</div>
                                        <div class="admin-table-cell">賣出倍率</div>
                                        <div class="admin-table-cell">自動更新/啟用</div>
                                    </div>
                                    <!-- 8種金屬數據行 -->
                                    <div class="admin-table-row" data-metal="gold_gram" data-type="physical_metals">
                                        <div class="admin-table-cell admin-metal-name">金 Au</div>
                                        <div class="admin-table-cell">
                                            <input type="number" class="form-control form-control-sm base-price-input"
                                                   id="gold_gram_base_price" step="0.01" min="0" placeholder="手動輸入價格">
                                        </div>
                                        <div class="admin-table-cell">
                                            <input type="number" class="form-control form-control-sm multiplier-input"
                                                   id="gold_gram_buy_mult" step="0.001" min="0" max="2" value="0.98">
                                        </div>
                                        <div class="admin-table-cell">
                                            <input type="number" class="form-control form-control-sm multiplier-input"
                                                   id="gold_gram_sell_mult" step="0.001" min="0" max="2" value="1.02">
                                        </div>
                                        <div class="admin-table-cell">
                                            <div class="d-flex flex-column gap-1">
                                                <div class="form-check form-switch mb-0">
                                                    <input class="form-check-input auto-update-switch" type="checkbox" id="gold_gram_auto_update"
                                                           onchange="toggleBasePriceReadonly('gold_gram_base_price', this.checked)">
                                                    <label class="form-check-label small" for="gold_gram_auto_update">自動</label>
                                                </div>
                                                <div class="form-check form-switch mb-0">
                                                    <input class="form-check-input enabled-switch" type="checkbox" id="gold_gram_enabled" checked>
                                                    <label class="form-check-label small" for="gold_gram_enabled">啟用</label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="admin-table-row" data-metal="silver_gram" data-type="physical_metals">
                                        <div class="admin-table-cell admin-metal-name">銀 Ag</div>
                                        <div class="admin-table-cell">
                                            <input type="number" class="form-control form-control-sm base-price-input"
                                                   id="silver_gram_base_price" step="0.01" min="0" placeholder="手動輸入價格">
                                        </div>
                                        <div class="admin-table-cell">
                                            <input type="number" class="form-control form-control-sm multiplier-input"
                                                   id="silver_gram_buy_mult" step="0.001" min="0" max="2" value="0.98">
                                        </div>
                                        <div class="admin-table-cell">
                                            <input type="number" class="form-control form-control-sm multiplier-input"
                                                   id="silver_gram_sell_mult" step="0.001" min="0" max="2" value="1.02">
                                        </div>
                                        <div class="admin-table-cell">
                                            <div class="d-flex flex-column gap-1">
                                                <div class="form-check form-switch mb-0">
                                                    <input class="form-check-input auto-update-switch" type="checkbox" id="silver_gram_auto_update"
                                                           onchange="toggleBasePriceReadonly('silver_gram_base_price', this.checked)">
                                                    <label class="form-check-label small" for="silver_gram_auto_update">自動</label>
                                                </div>
                                                <div class="form-check form-switch mb-0">
                                                    <input class="form-check-input enabled-switch" type="checkbox" id="silver_gram_enabled" checked>
                                                    <label class="form-check-label small" for="silver_gram_enabled">啟用</label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="admin-table-row" data-metal="palladium_gram" data-type="physical_metals">
                                        <div class="admin-table-cell admin-metal-name">鈀 Pd</div>
                                        <div class="admin-table-cell">
                                            <input type="number" class="form-control form-control-sm base-price-input"
                                                   id="palladium_gram_base_price" step="0.01" min="0" placeholder="手動輸入價格">
                                        </div>
                                        <div class="admin-table-cell">
                                            <input type="number" class="form-control form-control-sm multiplier-input"
                                                   id="palladium_gram_buy_mult" step="0.001" min="0" max="2" value="0.98">
                                        </div>
                                        <div class="admin-table-cell">
                                            <input type="number" class="form-control form-control-sm multiplier-input"
                                                   id="palladium_gram_sell_mult" step="0.001" min="0" max="2" value="1.02">
                                        </div>
                                        <div class="admin-table-cell">
                                            <div class="d-flex flex-column gap-1">
                                                <div class="form-check form-switch mb-0">
                                                    <input class="form-check-input auto-update-switch" type="checkbox" id="palladium_gram_auto_update"
                                                           onchange="toggleBasePriceReadonly('palladium_gram_base_price', this.checked)">
                                                    <label class="form-check-label small" for="palladium_gram_auto_update">自動</label>
                                                </div>
                                                <div class="form-check form-switch mb-0">
                                                    <input class="form-check-input enabled-switch" type="checkbox" id="palladium_gram_enabled" checked>
                                                    <label class="form-check-label small" for="palladium_gram_enabled">啟用</label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="admin-table-row" data-metal="platinum_gram" data-type="physical_metals">
                                        <div class="admin-table-cell admin-metal-name">鉑 Pt</div>
                                        <div class="admin-table-cell">
                                            <input type="number" class="form-control form-control-sm base-price-input"
                                                   id="platinum_gram_base_price" step="0.01" min="0" placeholder="手動輸入價格">
                                        </div>
                                        <div class="admin-table-cell">
                                            <input type="number" class="form-control form-control-sm multiplier-input"
                                                   id="platinum_gram_buy_mult" step="0.001" min="0" max="2" value="0.98">
                                        </div>
                                        <div class="admin-table-cell">
                                            <input type="number" class="form-control form-control-sm multiplier-input"
                                                   id="platinum_gram_sell_mult" step="0.001" min="0" max="2" value="1.02">
                                        </div>
                                        <div class="admin-table-cell">
                                            <div class="d-flex flex-column gap-1">
                                                <div class="form-check form-switch mb-0">
                                                    <input class="form-check-input auto-update-switch" type="checkbox" id="platinum_gram_auto_update"
                                                           onchange="toggleBasePriceReadonly('platinum_gram_base_price', this.checked)">
                                                    <label class="form-check-label small" for="platinum_gram_auto_update">自動</label>
                                                </div>
                                                <div class="form-check form-switch mb-0">
                                                    <input class="form-check-input enabled-switch" type="checkbox" id="platinum_gram_enabled" checked>
                                                    <label class="form-check-label small" for="platinum_gram_enabled">啟用</label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="admin-table-row" data-metal="rhodium_gram" data-type="physical_metals">
                                        <div class="admin-table-cell admin-metal-name">銠 Rh</div>
                                        <div class="admin-table-cell">
                                            <input type="number" class="form-control form-control-sm base-price-input"
                                                   id="rhodium_gram_base_price" step="0.01" min="0" placeholder="手動輸入價格">
                                        </div>
                                        <div class="admin-table-cell">
                                            <input type="number" class="form-control form-control-sm multiplier-input"
                                                   id="rhodium_gram_buy_mult" step="0.001" min="0" max="2" value="0.98">
                                        </div>
                                        <div class="admin-table-cell">
                                            <input type="number" class="form-control form-control-sm multiplier-input"
                                                   id="rhodium_gram_sell_mult" step="0.001" min="0" max="2" value="1.02">
                                        </div>
                                        <div class="admin-table-cell">
                                            <div class="d-flex flex-column gap-1">
                                                <div class="form-check form-switch mb-0">
                                                    <input class="form-check-input auto-update-switch" type="checkbox" id="rhodium_gram_auto_update"
                                                           onchange="toggleBasePriceReadonly('rhodium_gram_base_price', this.checked)">
                                                    <label class="form-check-label small" for="rhodium_gram_auto_update">自動</label>
                                                </div>
                                                <div class="form-check form-switch mb-0">
                                                    <input class="form-check-input enabled-switch" type="checkbox" id="rhodium_gram_enabled" checked>
                                                    <label class="form-check-label small" for="rhodium_gram_enabled">啟用</label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="admin-table-row" data-metal="ruthenium_gram" data-type="physical_metals">
                                        <div class="admin-table-cell admin-metal-name">釕 Ru</div>
                                        <div class="admin-table-cell">
                                            <input type="number" class="form-control form-control-sm base-price-input"
                                                   id="ruthenium_gram_base_price" step="0.01" min="0" placeholder="手動輸入價格">
                                        </div>
                                        <div class="admin-table-cell">
                                            <input type="number" class="form-control form-control-sm multiplier-input"
                                                   id="ruthenium_gram_buy_mult" step="0.001" min="0" max="2" value="0.98">
                                        </div>
                                        <div class="admin-table-cell">
                                            <input type="number" class="form-control form-control-sm multiplier-input"
                                                   id="ruthenium_gram_sell_mult" step="0.001" min="0" max="2" value="1.02">
                                        </div>
                                        <div class="admin-table-cell">
                                            <div class="d-flex flex-column gap-1">
                                                <div class="form-check form-switch mb-0">
                                                    <input class="form-check-input auto-update-switch" type="checkbox" id="ruthenium_gram_auto_update"
                                                           onchange="toggleBasePriceReadonly('ruthenium_gram_base_price', this.checked)">
                                                    <label class="form-check-label small" for="ruthenium_gram_auto_update">自動</label>
                                                </div>
                                                <div class="form-check form-switch mb-0">
                                                    <input class="form-check-input enabled-switch" type="checkbox" id="ruthenium_gram_enabled" checked>
                                                    <label class="form-check-label small" for="ruthenium_gram_enabled">啟用</label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="admin-table-row" data-metal="iridium_gram" data-type="physical_metals">
                                        <div class="admin-table-cell admin-metal-name">銥 Ir</div>
                                        <div class="admin-table-cell">
                                            <input type="number" class="form-control form-control-sm base-price-input"
                                                   id="iridium_gram_base_price" step="0.01" min="0" placeholder="手動輸入價格">
                                        </div>
                                        <div class="admin-table-cell">
                                            <input type="number" class="form-control form-control-sm multiplier-input"
                                                   id="iridium_gram_buy_mult" step="0.001" min="0" max="2" value="0.98">
                                        </div>
                                        <div class="admin-table-cell">
                                            <input type="number" class="form-control form-control-sm multiplier-input"
                                                   id="iridium_gram_sell_mult" step="0.001" min="0" max="2" value="1.02">
                                        </div>
                                        <div class="admin-table-cell">
                                            <div class="d-flex flex-column gap-1">
                                                <div class="form-check form-switch mb-0">
                                                    <input class="form-check-input auto-update-switch" type="checkbox" id="iridium_gram_auto_update"
                                                           onchange="toggleBasePriceReadonly('iridium_gram_base_price', this.checked)">
                                                    <label class="form-check-label small" for="iridium_gram_auto_update">自動</label>
                                                </div>
                                                <div class="form-check form-switch mb-0">
                                                    <input class="form-check-input enabled-switch" type="checkbox" id="iridium_gram_enabled" checked>
                                                    <label class="form-check-label small" for="iridium_gram_enabled">啟用</label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="admin-table-row" data-metal="osmium_gram" data-type="physical_metals">
                                        <div class="admin-table-cell admin-metal-name">鋨 Os</div>
                                        <div class="admin-table-cell">
                                            <input type="number" class="form-control form-control-sm base-price-input"
                                                   id="osmium_gram_base_price" step="0.01" min="0" placeholder="手動輸入價格">
                                        </div>
                                        <div class="admin-table-cell">
                                            <input type="number" class="form-control form-control-sm multiplier-input"
                                                   id="osmium_gram_buy_mult" step="0.001" min="0" max="2" value="0.98">
                                        </div>
                                        <div class="admin-table-cell">
                                            <input type="number" class="form-control form-control-sm multiplier-input"
                                                   id="osmium_gram_sell_mult" step="0.001" min="0" max="2" value="1.02">
                                        </div>
                                        <div class="admin-table-cell">
                                            <div class="d-flex flex-column gap-1">
                                                <div class="form-check form-switch mb-0">
                                                    <input class="form-check-input auto-update-switch" type="checkbox" id="osmium_gram_auto_update"
                                                           onchange="toggleBasePriceReadonly('osmium_gram_base_price', this.checked)">
                                                    <label class="form-check-label small" for="osmium_gram_auto_update">自動</label>
                                                </div>
                                                <div class="form-check form-switch mb-0">
                                                    <input class="form-check-input enabled-switch" type="checkbox" id="osmium_gram_enabled" checked>
                                                    <label class="form-check-label small" for="osmium_gram_enabled">啟用</label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                            <div class="mt-2">
                                <small class="text-muted">
                                    <strong>計算說明：</strong>公克基礎價格 = 美元價格 × 美元匯率 × 公克轉換係數 (1盎司 = 31.1035公克)
                                </small>
                            </div>
                                </div>
                            </div>
                </div>
            </div>
        </div>

        <!-- 價格管理控制區域 -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="row">
                    <!-- 快速操作區域 -->
                    <div class="col-lg-4 col-md-6 mb-3">
                        <div class="admin-price-table-container">
                            <div class="admin-table-header">
                                <h4>快速操作</h4>
                                <small class="text-muted">管理員控制功能</small>
                            </div>
                            <div class="p-3">
                                <div class="d-grid gap-2">
                                    <button type="button" class="btn btn-success" id="saveAllPrices">
                                        <i class="fas fa-save me-2"></i>保存所有設定
                                    </button>
                                    <button type="button" class="btn btn-info" id="loadCurrentPrices">
                                        <i class="fas fa-sync me-2"></i>載入當前價格
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 更新頻率控制 -->
                    <div class="col-lg-4 col-md-6 mb-3">
                        <div class="admin-price-table-container">
                            <div class="admin-table-header">
                                <h4>更新頻率控制</h4>
                                <small class="text-muted">自動更新間隔設定</small>
                            </div>
                            <div class="p-3">
                                <div class="mb-3">
                                    <label for="metalUpdateFrequency" class="form-label">金屬更新間隔（秒）</label>
                                    <input type="number" class="form-control frequency-input" id="metalUpdateFrequency"
                                           min="1" value="300" step="1">
                                    <small class="text-muted">最小值1秒</small>
                                </div>
                                <div class="mb-3">
                                    <label for="currencyUpdateFrequency" class="form-label">匯率更新間隔（小時）</label>
                                    <input type="number" class="form-control frequency-input" id="currencyUpdateFrequency"
                                           min="0.1" value="1" step="0.1">
                                    <small class="text-muted">最小值0.1小時</small>
                                </div>
                                <div class="d-grid">
                                    <button type="button" class="btn btn-primary" id="updateFrequencySettings">
                                        <i class="fas fa-save me-2"></i>儲存自動更新配置
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- TwelveData API 控制面板 -->
                    <div class="col-lg-4 col-md-12 mb-3">
                        <div class="admin-price-table-container">
                            <div class="admin-table-header">
                                <h4><i class="fas fa-chart-line me-2"></i>TwelveData API 控制</h4>
                                <small class="text-muted">API 狀態與控制</small>
                            </div>
                            <div class="p-3">
                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="twelvedataEnabled">
                                        <label class="form-check-label" for="twelvedataEnabled">
                                            啟用 TwelveData 即時價格
                                        </label>
                                    </div>
                                    <div class="form-text">啟用後將使用 TwelveData API 獲取即時金屬價格</div>
                                </div>



                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="pauseOnIdle">
                                        <label class="form-check-label" for="pauseOnIdle">
                                            閒置時暫停 TwelveData 請求
                                        </label>
                                    </div>
                                    <div class="form-text">當沒有用戶訪問時自動暫停 API 請求以節省配額</div>
                                </div>

                                <div class="mb-3">
                                    <label for="idleTimeout" class="form-label">閒置超時時間（秒）</label>
                                    <input type="number" class="form-control" id="idleTimeout" min="60" max="1800" value="300">
                                    <div class="form-text">超過此時間無用戶訪問則暫停 TwelveData 請求</div>
                                </div>

                                <div class="mb-3">
                                    <h6>API 狀態</h6>
                                    <div class="mb-2">
                                        <span class="badge bg-secondary" id="apiKeyStatus">檢查中...</span>
                                    </div>
                                    <div class="mb-2">
                                        <small class="text-muted">
                                            配額使用：<span id="quotaUsage">載入中...</span>
                                        </small>
                                    </div>
                                    <div class="mb-2">
                                        <small class="text-muted">
                                            啟用項目：<span id="enabledItemsCount">0</span> 個
                                        </small>
                                    </div>
                                </div>

                                <div class="d-grid gap-2">
                                    <button type="button" class="btn btn-info btn-sm" id="testTwelveDataAPI">
                                        <i class="fas fa-vial me-1"></i>測試 API 連接
                                    </button>
                                    <button type="button" class="btn btn-success btn-sm" id="forceUpdatePrices">
                                        <i class="fas fa-download me-1"></i>立即更新價格
                                    </button>
                                </div>

                                <div class="mt-3">
                                    <h6>API 操作日誌</h6>
                                    <div id="apiLogContainer" class="border rounded p-2" style="height: 120px; overflow-y: auto; background-color: #f8f9fa;">
                                        <div class="log-entry log-info">系統啟動，等待 API 操作...</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 新聞管理區域 -->
        <div class="row mt-5">
            <div class="col-12">
                <h3><i class="fas fa-newspaper me-2"></i>新聞管理</h3>
            </div>

            <div class="col-12">
                <div class="form-container">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h4><i class="fas fa-list me-2"></i>新聞列表</h4>
                        <div class="d-flex gap-2">
                            <button type="button" class="btn btn-gold btn-sm" onclick="adminAuth.showAddNewsModal()">
                                <i class="fas fa-plus me-1"></i>新增新聞
                            </button>
                            <button type="button" class="btn btn-info btn-sm" onclick="adminAuth.loadNews()">
                                <i class="fas fa-sync-alt me-1"></i>重新載入
                            </button>
                        </div>
                    </div>

                    <div id="newsList">
                        <!-- 動態載入的新聞列表 -->
                        <div class="text-center p-4">
                            <i class="fas fa-spinner fa-spin fa-2x text-muted"></i>
                            <p class="mt-2 text-muted">載入中...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 回收預約管理區域 -->
        <div class="row mt-5">
            <div class="col-12">
                <h3><i class="fas fa-calendar-alt me-2"></i>回收預約管理</h3>
            </div>

            <div class="col-12">
                <div class="form-container">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h4><i class="fas fa-list me-2"></i>預約列表</h4>
                        <div class="d-flex gap-2">
                            <button type="button" class="btn btn-warning btn-sm" id="pendingBookingsBtn" onclick="adminAuth.scrollToBookings()" style="display: none;">
                                <i class="fas fa-exclamation-triangle me-1"></i>
                                <span id="pendingBookingsCount">0</span> 筆待處理
                            </button>
                            <button type="button" class="btn btn-info btn-sm" onclick="adminAuth.loadBookings()">
                                <i class="fas fa-sync-alt me-1"></i>重新載入
                            </button>
                        </div>
                    </div>

                    <div id="bookingsList">
                        <!-- 動態載入的預約列表 -->
                        <div class="text-center p-4">
                            <i class="fas fa-spinner fa-spin fa-2x text-muted"></i>
                            <p class="mt-2 text-muted">載入中...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 聯絡我們管理區域 -->
        <div class="row mt-5">
            <div class="col-12">
                <h3><i class="fas fa-envelope me-2"></i>聯絡我們管理</h3>
            </div>

            <div class="col-12">
                <div class="form-container">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h4><i class="fas fa-list me-2"></i>聯絡列表</h4>
                        <div class="d-flex gap-2">
                            <button type="button" class="btn btn-warning btn-sm" id="pendingContactsBtn" onclick="adminAuth.scrollToContacts()" style="display: none;">
                                <i class="fas fa-exclamation-triangle me-1"></i>
                                <span id="pendingContactsCount">0</span> 筆待回覆
                            </button>
                            <button type="button" class="btn btn-info btn-sm" onclick="adminAuth.loadContacts()">
                                <i class="fas fa-sync-alt me-1"></i>重新載入
                            </button>
                        </div>
                    </div>

                    <div id="contactsList">
                        <!-- 動態載入的聯絡列表 -->
                        <div class="text-center p-4">
                            <i class="fas fa-spinner fa-spin fa-2x text-muted"></i>
                            <p class="mt-2 text-muted">載入中...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 系統設定區域 -->
        <div class="row mt-5">
            <div class="col-12">
                <h3><i class="fas fa-cog me-2"></i>系統設定</h3>
            </div>

            <div class="col-lg-6">
                <div class="form-container">
                    <h4><i class="fas fa-shield-alt me-2"></i>人機驗證設定</h4>

                    <div class="mb-3">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="captchaEnabled">
                            <label class="form-check-label" for="captchaEnabled">
                                啟用人機驗證
                            </label>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="captchaDifficulty" class="form-label">驗證碼難度</label>
                        <select class="form-select" id="captchaDifficulty">
                            <option value="easy">簡單（個位數加減）</option>
                            <option value="medium">中等（十位數加減）</option>
                            <option value="hard">困難（乘法運算）</option>
                        </select>
                    </div>

                    <h4><i class="fas fa-clock me-2"></i>提交限制設定</h4>

                    <div class="mb-3">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="rateLimitEnabled">
                            <label class="form-check-label" for="rateLimitEnabled">
                                啟用提交頻率限制
                            </label>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="maxSubmissions" class="form-label">4小時內最大提交次數</label>
                        <input type="number" class="form-control" id="maxSubmissions" min="1" max="10" value="3">
                        <div class="form-text">每個表單的最大提交次數（回收預約和聯絡我們分別計算）</div>
                    </div>

                    <div class="mb-3">
                        <button type="button" class="btn btn-gold" onclick="adminAuth.saveSystemSettings()">
                            <i class="fas fa-save me-1"></i>保存設定
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="{{ url_for('static', filename='vendor/bootstrap/js/bootstrap.bundle.min.js') }}"></script>

    <!-- jQuery (toastr 需要) -->
    <script src="{{ url_for('static', filename='vendor/jquery/jquery.min.js') }}"></script>

    <!-- Toastr JS -->
    <script src="{{ url_for('static', filename='vendor/toastr/js/toastr.min.js') }}"></script>

    <script>
        // 配置 toastr
        toastr.options = {
            "closeButton": true,
            "debug": false,
            "newestOnTop": true,
            "progressBar": true,
            "positionClass": "toast-top-right",
            "preventDuplicates": false,
            "onclick": null,
            "showDuration": "300",
            "hideDuration": "1000",
            "timeOut": "5000",
            "extendedTimeOut": "1000",
            "showEasing": "swing",
            "hideEasing": "linear",
            "showMethod": "fadeIn",
            "hideMethod": "fadeOut"
        };

        // 管理員面板類
        class AdminPanel {
            constructor() {
                this.token = localStorage.getItem('admin_token');
                this.init();
            }

            init() {
                // 檢查認證
                if (!this.token) {
                    this.redirectToLogin();
                    return;
                }

                // 驗證 Token 是否仍然有效
                this.validateToken();

                // 綁定事件
                this.bindEvents();

                // 載入初始數據
                this.loadCurrentPrices();
                this.loadSystemStats();

                // 載入系統設定
                window.adminAuth.loadSystemSettings();
            }

            async validateToken() {
                try {
                    const response = await fetch('/api/validate-token', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({ token: this.token })
                    });

                    if (!response.ok) {
                        this.redirectToLogin();
                    }
                } catch (error) {
                    console.error('Token 驗證失敗:', error);
                    this.redirectToLogin();
                }
            }

            redirectToLogin() {
                localStorage.removeItem('admin_token');
                window.location.href = '/admin-secure';
            }

            bindEvents() {
                // 價格管理事件
                this.bindPriceManagementEvents();

                // 系統管理事件
                this.bindSystemManagementEvents();

                // 設定管理事件
                this.bindSettingsEvents();
            }

            bindPriceManagementEvents() {
                // 載入當前價格按鈕
                const loadCurrentPricesBtn = document.getElementById('loadCurrentPrices');
                if (loadCurrentPricesBtn) {
                    loadCurrentPricesBtn.addEventListener('click', () => {
                        this.loadCurrentPrices();
                    });
                }

                // 保存所有設定按鈕
                const saveAllPricesBtn = document.getElementById('saveAllPrices');
                if (saveAllPricesBtn) {
                    saveAllPricesBtn.addEventListener('click', () => {
                        this.saveAllPriceSettings();
                    });
                }



                // 更新頻率設定按鈕
                const updateFrequencyBtn = document.getElementById('updateFrequencySettings');
                if (updateFrequencyBtn) {
                    updateFrequencyBtn.addEventListener('click', () => {
                        this.updateFrequencySettings();
                    });
                }

                // TwelveData 控制按鈕
                const testTwelveDataBtn = document.getElementById('testTwelveDataAPI');
                if (testTwelveDataBtn) {
                    testTwelveDataBtn.addEventListener('click', () => {
                        this.testTwelveDataAPI();
                    });
                }

                const forceUpdateBtn = document.getElementById('forceUpdatePrices');
                if (forceUpdateBtn) {
                    forceUpdateBtn.addEventListener('click', () => {
                        this.forceUpdatePrices();
                    });
                }



                // TwelveData 設定開關
                const twelvedataEnabledSwitch = document.getElementById('twelvedataEnabled');
                if (twelvedataEnabledSwitch) {
                    twelvedataEnabledSwitch.addEventListener('change', () => {
                        this.updateEnabledItemsCount();
                    });
                }

                // 自動更新開關變化事件
                document.querySelectorAll('.auto-update-switch').forEach(switchElement => {
                    switchElement.addEventListener('change', () => {
                        this.updateEnabledItemsCount();
                    });
                });

                // 啟用開關變化事件
                document.querySelectorAll('.enabled-switch').forEach(switchElement => {
                    switchElement.addEventListener('change', () => {
                        this.updateEnabledItemsCount();
                    });
                });
            }

            bindSystemManagementEvents() {
                // 系統管理事件綁定（使用舊版方法名稱）
                // 這些方法將在 adminAuth 對象中實現，保持與舊版一致
            }

            bindSettingsEvents() {
                // 設定事件綁定（使用舊版方法名稱）
                // 這些方法將在 adminAuth 對象中實現，保持與舊版一致
            }

            // 價格管理方法
            async loadCurrentPrices() {
                try {
                    const response = await fetch('/api/admin/current-prices', {
                        headers: {
                            'Authorization': `Bearer ${this.token}`
                        }
                    });

                    if (response.ok) {
                        const data = await response.json();
                        this.populateAdminPriceInputs(data);
                        toastr.success('價格數據載入成功');
                    } else {
                        throw new Error('載入價格失敗');
                    }
                } catch (error) {
                    console.error('載入價格錯誤:', error);
                    toastr.error('載入價格失敗: ' + error.message);
                }
            }

            populateAdminPriceInputs(data) {
                // 管理員 API 直接返回價格數據，不需要 base_data 包裝
                const baseData = data;

                // 填充商品/美元數據（4種貴金屬）
                if (baseData && baseData.metals) {
                    Object.entries(baseData.metals).forEach(([metal, metalData]) => {
                        const basePriceInput = document.getElementById(`${metal}_base_price`);
                        const autoUpdateSwitch = document.getElementById(`${metal}_auto_update`);
                        const enabledSwitch = document.getElementById(`${metal}_enabled`);
                        const buyMultInput = document.getElementById(`${metal}_buy_mult`);
                        const sellMultInput = document.getElementById(`${metal}_sell_mult`);

                        if (basePriceInput) basePriceInput.value = metalData.usd_per_oz || 0;
                        if (autoUpdateSwitch) autoUpdateSwitch.checked = metalData.auto_update || false;
                        if (enabledSwitch) enabledSwitch.checked = metalData.enabled !== false;
                        if (buyMultInput) buyMultInput.value = metalData.buy_multiplier || 0.98;
                        if (sellMultInput) sellMultInput.value = metalData.sell_multiplier || 1.02;
                    });
                }

                // 填充商品/台幣數據（4種貴金屬）
                if (baseData && baseData.metals_twd) {
                    Object.entries(baseData.metals_twd).forEach(([metal, metalData]) => {
                        const basePriceInput = document.getElementById(`${metal}_base_price`);
                        const autoUpdateSwitch = document.getElementById(`${metal}_auto_update`);
                        const enabledSwitch = document.getElementById(`${metal}_enabled`);
                        const buyMultInput = document.getElementById(`${metal}_buy_mult`);
                        const sellMultInput = document.getElementById(`${metal}_sell_mult`);

                        if (basePriceInput) basePriceInput.value = metalData.twd_per_tael || 0;
                        if (autoUpdateSwitch) autoUpdateSwitch.checked = metalData.auto_update || false;
                        if (enabledSwitch) enabledSwitch.checked = metalData.enabled !== false;
                        if (buyMultInput) buyMultInput.value = metalData.buy_multiplier || 0.98;
                        if (sellMultInput) sellMultInput.value = metalData.sell_multiplier || 1.02;
                    });
                }

                // 填充實物交易數據（8種金屬）
                if (baseData && baseData.physical_metals) {
                    Object.entries(baseData.physical_metals).forEach(([metal, metalData]) => {
                        const basePriceInput = document.getElementById(`${metal}_base_price`);
                        const autoUpdateSwitch = document.getElementById(`${metal}_auto_update`);
                        const enabledSwitch = document.getElementById(`${metal}_enabled`);
                        const buyMultInput = document.getElementById(`${metal}_buy_mult`);
                        const sellMultInput = document.getElementById(`${metal}_sell_mult`);

                        if (basePriceInput) basePriceInput.value = metalData.twd_per_gram || 0;
                        if (autoUpdateSwitch) autoUpdateSwitch.checked = metalData.auto_update || false;
                        if (enabledSwitch) enabledSwitch.checked = metalData.enabled !== false;
                        if (buyMultInput) buyMultInput.value = metalData.buy_multiplier || 0.98;
                        if (sellMultInput) sellMultInput.value = metalData.sell_multiplier || 1.02;
                    });
                }

                // 填充其他材料數據（2種材料）
                if (baseData && baseData.other_materials) {
                    Object.entries(baseData.other_materials).forEach(([material, materialData]) => {
                        const basePriceInput = document.getElementById(`${material}_base_price`);
                        const autoUpdateSwitch = document.getElementById(`${material}_auto_update`);
                        const enabledSwitch = document.getElementById(`${material}_enabled`);
                        const buyMultInput = document.getElementById(`${material}_buy_mult`);
                        const sellMultInput = document.getElementById(`${material}_sell_mult`);

                        if (basePriceInput) basePriceInput.value = materialData.twd_per_kg || 0;
                        if (autoUpdateSwitch) autoUpdateSwitch.checked = materialData.auto_update || false;
                        if (enabledSwitch) enabledSwitch.checked = materialData.enabled !== false;
                        if (buyMultInput) buyMultInput.value = materialData.buy_multiplier || 0.85;
                        if (sellMultInput) sellMultInput.value = materialData.sell_multiplier || 1.15;
                    });
                }

                // 填充匯率數據
                if (baseData && baseData.currencies) {
                    Object.entries(baseData.currencies).forEach(([currency, currencyData]) => {
                        const rateInput = document.getElementById(`${currency}_rate`);
                        const autoUpdateSwitch = document.getElementById(`${currency}_auto_update`);
                        const enabledSwitch = document.getElementById(`${currency}_enabled`);
                        const buyMultInput = document.getElementById(`${currency}_buy_mult`);
                        const sellMultInput = document.getElementById(`${currency}_sell_mult`);

                        if (rateInput) rateInput.value = currencyData.twd_rate || 1.0;
                        if (autoUpdateSwitch) autoUpdateSwitch.checked = currencyData.auto_update || false;
                        if (enabledSwitch) enabledSwitch.checked = currencyData.enabled !== false;
                        if (buyMultInput) buyMultInput.value = currencyData.buy_multiplier || 0.998;
                        if (sellMultInput) sellMultInput.value = currencyData.sell_multiplier || 1.002;
                    });
                }

                // 更新系統資訊
                const lastUpdateElement = document.getElementById('lastUpdateTime');
                if (lastUpdateElement && baseData && baseData.updateTime) {
                    lastUpdateElement.textContent = baseData.updateTime;
                }

                // 更新啟用項目計數
                this.updateEnabledItemsCount();
            }

            // 載入基礎價格數據
            loadBasePrices(data) {
                try {
                    // 從 calculated_prices 載入台幣基礎價格
                    if (data.calculated_prices) {
                        // 商品/台幣管理 - 台錢基礎價格
                        this.setBasePriceValue('gold_twd_base_price', data.calculated_prices.gold_twd_per_tael);
                        this.setBasePriceValue('silver_twd_base_price', data.calculated_prices.silver_twd_per_tael);
                        this.setBasePriceValue('palladium_twd_base_price', data.calculated_prices.palladium_twd_per_tael);
                        this.setBasePriceValue('platinum_twd_base_price', data.calculated_prices.platinum_twd_per_tael);

                        // 其他材料管理 - 公斤基礎價格
                        this.setBasePriceValue('cobalt_kg_base_price', data.calculated_prices.cobalt_kg_buy / 0.9); // 反推基礎價格
                        this.setBasePriceValue('tin_kg_base_price', data.calculated_prices.tin_kg_buy / 0.92); // 反推基礎價格

                        // 實物交易管理 - 公克基礎價格
                        this.setBasePriceValue('gold_gram_base_price', data.calculated_prices.gold_gram_buy / 0.98); // 反推基礎價格
                        this.setBasePriceValue('silver_gram_base_price', data.calculated_prices.silver_gram_buy / 0.98);
                        this.setBasePriceValue('palladium_gram_base_price', data.calculated_prices.palladium_gram_buy / 0.95);
                        this.setBasePriceValue('platinum_gram_base_price', data.calculated_prices.platinum_gram_buy / 0.96);
                        this.setBasePriceValue('rhodium_gram_base_price', data.calculated_prices.rhodium_gram_buy / 0.9);
                        this.setBasePriceValue('ruthenium_gram_base_price', data.calculated_prices.ruthenium_gram_buy / 0.92);
                        this.setBasePriceValue('iridium_gram_base_price', data.calculated_prices.iridium_gram_buy / 0.88);
                        this.setBasePriceValue('osmium_gram_base_price', data.calculated_prices.osmium_gram_buy / 0.85);
                    }

                    console.log('✅ 基礎價格載入完成');
                } catch (error) {
                    console.error('❌ 載入基礎價格失敗:', error);
                }
            }

            // 設置基礎價格值（無論是否為 readonly）
            setBasePriceValue(elementId, price) {
                const element = document.getElementById(elementId);
                if (element && price !== undefined && price !== null) {
                    element.value = price.toFixed(2);
                }
            }

            async updateEnabledItemsCount() {
                try {
                    const response = await fetch('/api/admin/twelvedata/enabled-items', {
                        headers: {
                            'Authorization': `Bearer ${this.token}`
                        }
                    });

                    if (response.ok) {
                        const result = await response.json();
                        const enabledItemsElement = document.getElementById('enabledItemsCount');
                        if (enabledItemsElement) {
                            enabledItemsElement.textContent = result.enabled_count || 0;
                        }
                    } else {
                        // 如果 API 失敗，回退到前端計算
                        const autoUpdateSwitches = document.querySelectorAll('.auto-update-switch:checked');
                        const enabledItemsElement = document.getElementById('enabledItemsCount');
                        if (enabledItemsElement) {
                            enabledItemsElement.textContent = autoUpdateSwitches.length;
                        }
                    }
                } catch (error) {
                    console.error('獲取啟用項目數量失敗:', error);
                    // 如果 API 失敗，回退到前端計算
                    const autoUpdateSwitches = document.querySelectorAll('.auto-update-switch:checked');
                    const enabledItemsElement = document.getElementById('enabledItemsCount');
                    if (enabledItemsElement) {
                        enabledItemsElement.textContent = autoUpdateSwitches.length;
                    }
                }
            }

            async saveAllPriceSettings() {
                try {
                    const settings = this.collectAdminPriceSettings();

                    // 添加系統資訊
                    settings.lastUpdate = new Date().toISOString();
                    settings.updateTime = new Date().toLocaleString('zh-TW');
                    settings.updatedBy = '管理員';
                    settings.source = '管理員設定';
                    settings.version = '2.0';

                    const response = await fetch('/api/prices', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': `Bearer ${this.token}`
                        },
                        body: JSON.stringify(settings)
                    });

                    if (response.ok) {
                        toastr.success('所有價格設定保存成功');
                        this.loadCurrentPrices(); // 重新載入以確認
                    } else {
                        const errorData = await response.json();
                        throw new Error(errorData.error || '保存失敗');
                    }
                } catch (error) {
                    console.error('保存價格設定錯誤:', error);
                    toastr.error('保存失敗: ' + error.message);
                }
            }

            collectAdminPriceSettings() {
                const settings = {
                    metals: {},
                    metals_twd: {},
                    physical_metals: {},
                    other_materials: {},
                    currencies: {},
                    update_frequencies: {
                        metals_seconds: parseInt(document.getElementById('metalUpdateFrequency')?.value) || 300,
                        currencies_base_seconds: parseFloat(document.getElementById('currencyUpdateFrequency')?.value) * 3600 || 3600
                    }
                };

                // 收集商品/美元設定（4種貴金屬）
                const metalTypes = ['gold', 'silver', 'palladium', 'platinum'];
                metalTypes.forEach(metal => {
                    const basePriceInput = document.getElementById(`${metal}_base_price`);
                    const autoUpdateSwitch = document.getElementById(`${metal}_auto_update`);
                    const enabledSwitch = document.getElementById(`${metal}_enabled`);
                    const buyMultInput = document.getElementById(`${metal}_buy_mult`);
                    const sellMultInput = document.getElementById(`${metal}_sell_mult`);

                    if (basePriceInput) {
                        settings.metals[metal] = {
                            usd_per_oz: parseFloat(basePriceInput.value) || 0,
                            auto_update: autoUpdateSwitch?.checked || false,
                            enabled: enabledSwitch?.checked !== false,
                            buy_multiplier: parseFloat(buyMultInput?.value) || 0.98,
                            sell_multiplier: parseFloat(sellMultInput?.value) || 1.02
                        };
                    }
                });

                // 收集商品/台幣設定（4種貴金屬）
                const metalTypesTwd = ['gold_twd', 'silver_twd', 'palladium_twd', 'platinum_twd'];
                metalTypesTwd.forEach(metal => {
                    const basePriceInput = document.getElementById(`${metal}_base_price`);
                    const autoUpdateSwitch = document.getElementById(`${metal}_auto_update`);
                    const enabledSwitch = document.getElementById(`${metal}_enabled`);
                    const buyMultInput = document.getElementById(`${metal}_buy_mult`);
                    const sellMultInput = document.getElementById(`${metal}_sell_mult`);

                    if (basePriceInput) {
                        settings.metals_twd[metal] = {
                            twd_per_tael: parseFloat(basePriceInput.value) || 0,
                            auto_update: autoUpdateSwitch?.checked || false,
                            enabled: enabledSwitch?.checked !== false,
                            buy_multiplier: parseFloat(buyMultInput?.value) || 0.98,
                            sell_multiplier: parseFloat(sellMultInput?.value) || 1.02
                        };
                    }
                });

                // 收集實物交易設定（8種金屬，公克）
                const physicalMetals = ['gold_gram', 'silver_gram', 'palladium_gram', 'platinum_gram', 'rhodium_gram', 'ruthenium_gram', 'iridium_gram', 'osmium_gram'];
                physicalMetals.forEach(metal => {
                    const basePriceInput = document.getElementById(`${metal}_base_price`);
                    const autoUpdateSwitch = document.getElementById(`${metal}_auto_update`);
                    const enabledSwitch = document.getElementById(`${metal}_enabled`);
                    const buyMultInput = document.getElementById(`${metal}_buy_mult`);
                    const sellMultInput = document.getElementById(`${metal}_sell_mult`);

                    settings.physical_metals[metal] = {
                        twd_per_gram: parseFloat(basePriceInput?.value) || 0,
                        auto_update: autoUpdateSwitch?.checked || false,
                        enabled: enabledSwitch?.checked !== false,
                        buy_multiplier: parseFloat(buyMultInput?.value) || 0.98,
                        sell_multiplier: parseFloat(sellMultInput?.value) || 1.02
                    };
                });

                // 收集其他材料設定（2種材料，公斤）
                const otherMaterials = ['cobalt_kg', 'tin_kg'];
                otherMaterials.forEach(material => {
                    const basePriceInput = document.getElementById(`${material}_base_price`);
                    const autoUpdateSwitch = document.getElementById(`${material}_auto_update`);
                    const enabledSwitch = document.getElementById(`${material}_enabled`);
                    const buyMultInput = document.getElementById(`${material}_buy_mult`);
                    const sellMultInput = document.getElementById(`${material}_sell_mult`);

                    settings.other_materials[material] = {
                        twd_per_kg: parseFloat(basePriceInput?.value) || 0,
                        auto_update: autoUpdateSwitch?.checked || false,
                        enabled: enabledSwitch?.checked !== false,
                        buy_multiplier: parseFloat(buyMultInput?.value) || 0.85,
                        sell_multiplier: parseFloat(sellMultInput?.value) || 1.15
                    };
                });

                // 收集匯率設定
                const currencies = ['usd', 'cny', 'eur'];
                currencies.forEach(currency => {
                    const rateInput = document.getElementById(`${currency}_rate`);
                    const autoUpdateSwitch = document.getElementById(`${currency}_auto_update`);
                    const enabledSwitch = document.getElementById(`${currency}_enabled`);
                    const buyMultInput = document.getElementById(`${currency}_buy_mult`);
                    const sellMultInput = document.getElementById(`${currency}_sell_mult`);

                    if (rateInput) {
                        settings.currencies[currency] = {
                            twd_rate: parseFloat(rateInput.value) || 1.0,
                            auto_update: autoUpdateSwitch?.checked || false,
                            enabled: enabledSwitch?.checked !== false,
                            buy_multiplier: parseFloat(buyMultInput?.value) || 0.998,
                            sell_multiplier: parseFloat(sellMultInput?.value) || 1.002,
                            update_frequency_hours: 1.0
                        };
                    }
                });

                return settings;
            }

            // 系統管理方法（簡化實現）
            async loadSystemStats() {
                // 載入系統統計數據
                try {
                    // 載入新聞、預約、聯絡數據來計算統計
                    await this.loadNews();
                    await this.loadBookings();
                    await this.loadContacts();
                } catch (error) {
                    console.error('載入系統統計失敗:', error);
                }
            }

            // 新聞管理方法
            async loadNews() {
                return adminAuth.loadNews();
            }

            // 預約管理方法
            async loadBookings() {
                return adminAuth.loadBookings();
            }

            // 聯絡管理方法
            async loadContacts() {
                return adminAuth.loadContacts();
            }

            // TwelveData API 方法
            async testTwelveDataAPI() {
                this.addApiLog('測試 TwelveData API 連接...', 'info');
                try {
                    const response = await fetch('/api/admin/twelvedata/test', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': `Bearer ${this.token}`
                        }
                    });

                    const result = await response.json();

                    if (result.success) {
                        this.addApiLog('TwelveData API 連接測試成功', 'success');
                        toastr.success('TwelveData API 測試成功');

                        // 更新 API 狀態顯示
                        const apiKeyStatus = document.getElementById('apiKeyStatus');
                        if (apiKeyStatus) {
                            apiKeyStatus.textContent = 'API 正常';
                            apiKeyStatus.className = 'badge bg-success';
                        }
                    } else {
                        this.addApiLog('TwelveData API 連接測試失敗: ' + (result.error || '未知錯誤'), 'error');
                        toastr.error('TwelveData API 測試失敗: ' + (result.error || '未知錯誤'));

                        // 更新 API 狀態顯示
                        const apiKeyStatus = document.getElementById('apiKeyStatus');
                        if (apiKeyStatus) {
                            apiKeyStatus.textContent = 'API 異常';
                            apiKeyStatus.className = 'badge bg-danger';
                        }
                    }
                } catch (error) {
                    this.addApiLog('TwelveData API 連接測試失敗: ' + error.message, 'error');
                    toastr.error('API 測試失敗: ' + error.message);

                    // 更新 API 狀態顯示
                    const apiKeyStatus = document.getElementById('apiKeyStatus');
                    if (apiKeyStatus) {
                        apiKeyStatus.textContent = 'API 異常';
                        apiKeyStatus.className = 'badge bg-danger';
                    }
                }
            }

            async forceUpdatePrices() {
                this.addApiLog('強制更新價格中...', 'info');
                try {
                    const response = await fetch('/api/admin/twelvedata/force-update', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': `Bearer ${this.token}`
                        }
                    });

                    const result = await response.json();

                    if (result.success) {
                        this.addApiLog(`價格更新完成，成功更新 ${result.updated_count || 0} 個價格`, 'success');
                        this.loadCurrentPrices(); // 重新載入價格
                        toastr.success('價格更新完成');
                    } else {
                        this.addApiLog('價格更新失敗: ' + (result.error || '未知錯誤'), 'error');
                        toastr.error('價格更新失敗: ' + (result.error || '未知錯誤'));
                    }
                } catch (error) {
                    this.addApiLog('價格更新失敗: ' + error.message, 'error');
                    toastr.error('價格更新失敗: ' + error.message);
                }
            }



            addApiLog(message, type = 'info') {
                const logContainer = document.getElementById('apiLogContainer');
                if (logContainer) {
                    const timestamp = new Date().toLocaleTimeString('zh-TW');
                    const logEntry = document.createElement('div');
                    logEntry.className = `log-entry log-${type}`;
                    logEntry.textContent = `[${timestamp}] ${message}`;

                    logContainer.appendChild(logEntry);
                    logContainer.scrollTop = logContainer.scrollHeight;

                    // 限制日誌條目數量
                    const entries = logContainer.querySelectorAll('.log-entry');
                    if (entries.length > 50) {
                        entries[0].remove();
                    }
                }
            }

            // 更新頻率設定
            async updateFrequencySettings() {
                try {
                    // 收集更新頻率設定
                    const frequencyData = {
                        twelvedata: {
                            frontendRefreshRate: parseInt(document.getElementById('metalUpdateFrequency')?.value) || 300,
                            updateInterval: parseFloat(document.getElementById('currencyUpdateFrequency')?.value) * 3600 || 3600,
                            enabled: document.getElementById('twelvedataEnabled')?.checked || false,
                            pauseOnIdle: document.getElementById('pauseOnIdle')?.checked ?? true,
                            idleTimeout: parseInt(document.getElementById('idleTimeout')?.value) || 300
                        },
                        updatedBy: '管理員'
                    };

                    const response = await fetch('/api/admin/settings', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': `Bearer ${this.token}`
                        },
                        body: JSON.stringify(frequencyData)
                    });

                    const result = await response.json();

                    if (result.success) {
                        toastr.success('自動更新配置保存成功！');
                    } else {
                        toastr.error(result.error || '保存失敗');
                    }
                } catch (error) {
                    console.error('保存更新頻率設定失敗:', error);
                    toastr.error('保存失敗: ' + error.message);
                }
            }



            // // 階段四新增：手動計算並更新基礎價格（僅對未啟用自動更新的欄位）
            // calculateBasePrices() {
            //     try {
            //         // 獲取美元匯率
            //         const usdRate = parseFloat(document.getElementById('usd_rate')?.value) || 31.5;

            //         // 計算台幣基礎價格
            //         const goldUsdPrice = parseFloat(document.getElementById('gold_base_price')?.value) || 2045.80;
            //         const silverUsdPrice = parseFloat(document.getElementById('silver_base_price')?.value) || 24.50;
            //         const palladiumUsdPrice = parseFloat(document.getElementById('palladium_base_price')?.value) || 1050.0;
            //         const platinumUsdPrice = parseFloat(document.getElementById('platinum_base_price')?.value) || 950.0;

            //         // 計算台錢價格（1盎司 = 31.1035公克，1台錢 = 37.5公克）
            //         const ozToTael = 31.1035 / 37.5;

            //         // 更新台幣基礎價格（僅對未啟用自動更新的欄位）
            //         this.updateCalculatedPriceIfNotAutoUpdate('gold_twd_base_price', 'gold_twd_auto_update', goldUsdPrice * usdRate * ozToTael);
            //         this.updateCalculatedPriceIfNotAutoUpdate('silver_twd_base_price', 'silver_twd_auto_update', silverUsdPrice * usdRate * ozToTael);
            //         this.updateCalculatedPriceIfNotAutoUpdate('palladium_twd_base_price', 'palladium_twd_auto_update', palladiumUsdPrice * usdRate * ozToTael);
            //         this.updateCalculatedPriceIfNotAutoUpdate('platinum_twd_base_price', 'platinum_twd_auto_update', platinumUsdPrice * usdRate * ozToTael);

            //         // 計算公克價格（8種金屬）
            //         const ozToGram = 31.1035;
            //         this.updateCalculatedPriceIfNotAutoUpdate('gold_gram_base_price', 'gold_gram_auto_update', goldUsdPrice * usdRate / ozToGram);
            //         this.updateCalculatedPriceIfNotAutoUpdate('silver_gram_base_price', 'silver_gram_auto_update', silverUsdPrice * usdRate / ozToGram);
            //         this.updateCalculatedPriceIfNotAutoUpdate('palladium_gram_base_price', 'palladium_gram_auto_update', palladiumUsdPrice * usdRate / ozToGram);
            //         this.updateCalculatedPriceIfNotAutoUpdate('platinum_gram_base_price', 'platinum_gram_auto_update', platinumUsdPrice * usdRate / ozToGram);

            //         // 其他4種金屬的預設價格
            //         const rhodiumUsdPrice = parseFloat(document.getElementById('rhodium_base_price')?.value) || 4500.0;
            //         const rutheniumUsdPrice = parseFloat(document.getElementById('ruthenium_base_price')?.value) || 450.0;
            //         const iridiumUsdPrice = parseFloat(document.getElementById('iridium_base_price')?.value) || 1800.0;
            //         const osmiumUsdPrice = parseFloat(document.getElementById('osmium_base_price')?.value) || 400.0;

            //         this.updateCalculatedPriceIfNotAutoUpdate('rhodium_gram_base_price', 'rhodium_gram_auto_update', rhodiumUsdPrice * usdRate / ozToGram);
            //         this.updateCalculatedPriceIfNotAutoUpdate('ruthenium_gram_base_price', 'ruthenium_gram_auto_update', rutheniumUsdPrice * usdRate / ozToGram);
            //         this.updateCalculatedPriceIfNotAutoUpdate('iridium_gram_base_price', 'iridium_gram_auto_update', iridiumUsdPrice * usdRate / ozToGram);
            //         this.updateCalculatedPriceIfNotAutoUpdate('osmium_gram_base_price', 'osmium_gram_auto_update', osmiumUsdPrice * usdRate / ozToGram);

            //         // 計算其他材料價格（美元/磅 -> 台幣/公斤）
            //         const lbToKg = 0.453592;
            //         const cobaltUsdPrice = 15.50; // 預設值
            //         const tinUsdPrice = 14.20; // 預設值

            //         this.updateCalculatedPriceIfNotAutoUpdate('cobalt_kg_base_price', 'cobalt_kg_auto_update', cobaltUsdPrice * usdRate / lbToKg);
            //         this.updateCalculatedPriceIfNotAutoUpdate('tin_kg_base_price', 'tin_kg_auto_update', tinUsdPrice * usdRate / lbToKg);

            //         console.log('✅ 基礎價格計算完成');

            //     } catch (error) {
            //         console.error('❌ 計算基礎價格失敗:', error);
            //     }
            // }

            // 更新計算出的價格
            updateCalculatedPrice(elementId, price) {
                const element = document.getElementById(elementId);
                if (element) {
                    element.value = price.toFixed(2);
                }
            }

            // 僅當啟用自動更新時才更新計算出的價格
            updateCalculatedPriceIfAutoUpdate(priceElementId, autoUpdateElementId, price) {
                const autoUpdateElement = document.getElementById(autoUpdateElementId);
                if (autoUpdateElement && autoUpdateElement.checked) {
                    this.updateCalculatedPrice(priceElementId, price);
                }
            }

            // 僅當未啟用自動更新時才更新計算出的價格（用於手動計算）
            updateCalculatedPriceIfNotAutoUpdate(priceElementId, autoUpdateElementId, price) {
                const autoUpdateElement = document.getElementById(autoUpdateElementId);
                if (autoUpdateElement && !autoUpdateElement.checked) {
                    this.updateCalculatedPrice(priceElementId, price);
                }
            }
        }

        // 創建 adminAuth 對象以保持與舊版兼容
        const adminAuth = {
            tokenKey: 'admin_token',
            currentNews: null,
            currentBookings: null,
            currentContacts: null,

            // 新聞管理方法
            showAddNewsModal() {
                toastr.info('開啟新增新聞對話框...');
            },

            async loadNews() {
                const token = localStorage.getItem(this.tokenKey);
                if (!token) {
                    toastr.error('請先登入');
                    return;
                }

                try {
                    const response = await fetch('/api/admin/current-news', {
                        method: 'GET',
                        headers: {
                            'Authorization': `Bearer ${token}`
                        }
                    });

                    if (response.ok) {
                        this.currentNews = await response.json();
                        this.updateNewsDisplay();
                    } else {
                        toastr.error('載入新聞失敗');
                    }
                } catch (error) {
                    console.error('載入新聞失敗:', error);
                    toastr.error('載入新聞失敗');
                }
            },

            updateNewsDisplay() {
                const container = document.getElementById('newsList');
                if (!this.currentNews || this.currentNews.news.length === 0) {
                    container.innerHTML = '<div class="text-center p-4 text-muted">暫無新聞記錄</div>';
                    return;
                }

                container.innerHTML = '';
                this.currentNews.news.forEach((newsItem, index) => {
                    const newsDiv = document.createElement('div');
                    newsDiv.className = 'border rounded p-3 mb-3';
                    newsDiv.innerHTML = `
                        <div class="d-flex justify-content-between align-items-start mb-2">
                            <h6 class="mb-0">${this.escapeHtml(newsItem.title)}</h6>
                            <span class="badge bg-secondary">${this.escapeHtml(newsItem.category)}</span>
                        </div>
                        <p class="mb-2 text-muted">${this.escapeHtml(newsItem.content)}</p>
                        <div class="d-flex justify-content-between align-items-center">
                            <small class="text-muted">${newsItem.date} ${newsItem.time}</small>
                        </div>
                    `;
                    container.appendChild(newsDiv);
                });
            },

            // 預約管理方法
            async loadBookings() {
                const token = localStorage.getItem(this.tokenKey);
                if (!token) {
                    toastr.error('請先登入');
                    return;
                }

                try {
                    const response = await fetch('/api/admin/bookings', {
                        method: 'GET',
                        headers: {
                            'Authorization': `Bearer ${token}`
                        }
                    });

                    if (response.ok) {
                        this.currentBookings = await response.json();
                        this.updateBookingsDisplay();
                    } else {
                        toastr.error('載入預約失敗');
                    }
                } catch (error) {
                    console.error('載入預約失敗:', error);
                    toastr.error('載入預約失敗');
                }
            },

            updateBookingsDisplay() {
                const container = document.getElementById('bookingsList');
                if (!this.currentBookings || this.currentBookings.bookings.length === 0) {
                    container.innerHTML = '<div class="text-center p-4 text-muted">暫無預約記錄</div>';
                    this.updatePendingBookingsCount(0);
                    return;
                }

                container.innerHTML = '';
                let pendingCount = 0;

                this.currentBookings.bookings.forEach((booking, index) => {
                    if (booking.status === 'pending') {
                        pendingCount++;
                    }
                    const statusClass = booking.status === 'completed' ? 'success' :
                                       booking.status === 'cancelled' ? 'danger' : 'warning';
                    const statusText = booking.status === 'completed' ? '已完成' :
                                      booking.status === 'cancelled' ? '已取消' : '待處理';

                    const bookingDiv = document.createElement('div');
                    bookingDiv.className = 'border rounded p-3 mb-3';
                    bookingDiv.innerHTML = `
                        <div class="d-flex justify-content-between align-items-start mb-2">
                            <h6 class="mb-0">預約 #${booking.id}</h6>
                            <span class="badge bg-${statusClass}">${statusText}</span>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <p class="mb-1"><strong>姓名：</strong>${this.escapeHtml(booking.name)}</p>
                                <p class="mb-1"><strong>電話：</strong>${this.escapeHtml(booking.phone)}</p>
                                <p class="mb-1"><strong>Email：</strong>${this.escapeHtml(booking.email)}</p>
                                <p class="mb-1"><strong>地址：</strong>${this.escapeHtml(booking.address)}</p>
                            </div>
                            <div class="col-md-6">
                                <p class="mb-1"><strong>金屬類型：</strong>${this.escapeHtml(booking.metal_type)}</p>
                                <p class="mb-1"><strong>數量：</strong>${this.escapeHtml(booking.quantity)}</p>
                                <p class="mb-1"><strong>預約日期：</strong>${booking.preferred_date} ${booking.preferred_time || ''}</p>
                                <p class="mb-1"><strong>提交時間：</strong>${new Date(booking.submit_time).toLocaleString('zh-TW')}</p>
                            </div>
                        </div>
                        ${booking.notes ? `<p class="mb-2"><strong>備註：</strong>${this.escapeHtml(booking.notes)}</p>` : ''}
                        ${booking.admin_notes ? `<p class="mb-2"><strong>管理員備註：</strong>${this.escapeHtml(booking.admin_notes)}</p>` : ''}
                        <div class="d-flex gap-2 mt-3">
                            ${booking.status === 'pending' ? `
                                <button class="btn btn-success btn-sm" onclick="adminAuth.updateBookingStatus(${booking.id}, 'completed')">
                                    <i class="fas fa-check"></i> 標記完成
                                </button>
                                <button class="btn btn-warning btn-sm" onclick="adminAuth.updateBookingStatus(${booking.id}, 'in_progress')">
                                    <i class="fas fa-clock"></i> 處理中
                                </button>
                            ` : ''}
                            ${booking.status === 'in_progress' ? `
                                <button class="btn btn-success btn-sm" onclick="adminAuth.updateBookingStatus(${booking.id}, 'completed')">
                                    <i class="fas fa-check"></i> 標記完成
                                </button>
                                <button class="btn btn-secondary btn-sm" onclick="adminAuth.updateBookingStatus(${booking.id}, 'pending')">
                                    <i class="fas fa-undo"></i> 回到待處理
                                </button>
                            ` : ''}
                            ${booking.status === 'completed' ? `
                                <button class="btn btn-secondary btn-sm" onclick="adminAuth.updateBookingStatus(${booking.id}, 'pending')">
                                    <i class="fas fa-undo"></i> 重新開啟
                                </button>
                            ` : ''}
                            <button class="btn btn-danger btn-sm" onclick="adminAuth.deleteBooking(${booking.id})">
                                <i class="fas fa-trash"></i> 刪除
                            </button>
                        </div>
                    `;
                    container.appendChild(bookingDiv);
                });

                this.updatePendingBookingsCount(pendingCount);
            },

            updateBookingStatus(bookingId, status) {
                // 顯示管理員備註輸入模態框
                const modal = new bootstrap.Modal(document.getElementById('adminNotesModal'));

                // 設定模態框標題和內容
                const modalTitle = document.getElementById('adminNotesModalLabel');
                const statusText = {
                    'pending': '待處理',
                    'in_progress': '處理中',
                    'completed': '已完成'
                };
                modalTitle.textContent = `更新預約狀態為：${statusText[status]}`;

                // 設定隱藏欄位
                document.getElementById('modalItemId').value = bookingId;
                document.getElementById('modalItemType').value = 'booking';
                document.getElementById('modalAction').value = status;

                // 清空備註輸入
                document.getElementById('adminNotesInput').value = '';

                // 顯示備註輸入區域（狀態更新需要備註）
                const notesContainer = document.querySelector('#adminNotesModal .mb-3');
                if (notesContainer) {
                    notesContainer.style.display = 'block';
                }

                // 重置備註標籤文字
                const notesLabel = document.querySelector('#adminNotesModal label[for="adminNotesInput"]');
                if (notesLabel) {
                    notesLabel.textContent = '管理員備註（選填）';
                }

                // 重置確認按鈕文字和樣式
                const confirmBtn = document.getElementById('confirmActionBtn');
                if (confirmBtn) {
                    confirmBtn.textContent = '確認';
                    confirmBtn.className = 'btn btn-primary';
                }

                modal.show();
            },

            async executeBookingStatusUpdate(bookingId, status, adminNotes) {
                const token = localStorage.getItem(this.tokenKey);

                try {
                    const response = await fetch(`/api/admin/booking/${bookingId}`, {
                        method: 'PUT',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': `Bearer ${token}`
                        },
                        body: JSON.stringify({
                            status: status,
                            admin_notes: adminNotes || ''
                        })
                    });

                    const result = await response.json();

                    if (result.success) {
                        toastr.success('預約狀態更新成功！');
                        await this.loadBookings(); // 重新載入預約
                    } else {
                        toastr.error(result.error || '更新失敗');
                    }
                } catch (error) {
                    console.error('更新預約狀態失敗:', error);
                    toastr.error('網絡錯誤，請重試');
                }
            },

            deleteBooking(bookingId) {
                // 顯示管理員備註模態框
                const modal = new bootstrap.Modal(document.getElementById('adminNotesModal'));

                // 設定模態框標題和內容
                const modalTitle = document.getElementById('adminNotesModalLabel');
                modalTitle.textContent = '刪除預約';

                // 設定隱藏欄位
                document.getElementById('modalItemId').value = bookingId;
                document.getElementById('modalItemType').value = 'booking';
                document.getElementById('modalAction').value = 'delete';

                // 清空備註輸入
                document.getElementById('adminNotesInput').value = '';

                // 隱藏備註輸入區域（刪除操作不需要備註）
                const notesContainer = document.querySelector('#adminNotesModal .mb-3');
                if (notesContainer) {
                    notesContainer.style.display = 'none';
                }

                // 修改確認按鈕文字和樣式
                const confirmBtn = document.getElementById('confirmActionBtn');
                if (confirmBtn) {
                    confirmBtn.textContent = '確認刪除';
                    confirmBtn.className = 'btn btn-danger';
                }

                modal.show();
            },

            async executeBookingDelete(bookingId) {
                const token = localStorage.getItem(this.tokenKey);

                try {
                    const response = await fetch(`/api/admin/booking/${bookingId}`, {
                        method: 'DELETE',
                        headers: {
                            'Authorization': `Bearer ${token}`
                        }
                    });

                    const result = await response.json();

                    if (result.success) {
                        toastr.success('預約刪除成功！');
                        await this.loadBookings(); // 重新載入預約
                    } else {
                        toastr.error(result.error || '刪除失敗');
                    }
                } catch (error) {
                    console.error('刪除預約失敗:', error);
                    toastr.error('網絡錯誤，請重試');
                }
            },

            scrollToBookings() {
                const bookingsSection = document.querySelector('#bookingsList').closest('.form-container');
                if (bookingsSection) {
                    bookingsSection.scrollIntoView({ behavior: 'smooth' });
                }
            },

            // 聯絡狀態更新（簡化實作）
            async executeContactStatusUpdate(contactId, status, adminNotes) {
                toastr.info('聯絡管理功能暫未完整實作');
            },

            updatePendingBookingsCount(count) {
                const btn = document.getElementById('pendingBookingsBtn');
                const countSpan = document.getElementById('pendingBookingsCount');
                if (count > 0) {
                    btn.style.display = 'inline-block';
                    countSpan.textContent = count;
                } else {
                    btn.style.display = 'none';
                }
            },

            scrollToBookings() {
                document.getElementById('bookingsList').scrollIntoView({ behavior: 'smooth' });
            },

            // 聯絡管理方法
            async loadContacts() {
                const token = localStorage.getItem(this.tokenKey);
                if (!token) {
                    toastr.error('請先登入');
                    return;
                }

                try {
                    const response = await fetch('/api/admin/contacts', {
                        method: 'GET',
                        headers: {
                            'Authorization': `Bearer ${token}`
                        }
                    });

                    if (response.ok) {
                        this.currentContacts = await response.json();
                        this.updateContactsDisplay();
                    } else {
                        toastr.error('載入聯絡失敗');
                    }
                } catch (error) {
                    console.error('載入聯絡失敗:', error);
                    toastr.error('載入聯絡失敗');
                }
            },

            updateContactsDisplay() {
                const container = document.getElementById('contactsList');
                if (!this.currentContacts || this.currentContacts.contacts.length === 0) {
                    container.innerHTML = '<div class="text-center p-4 text-muted">暫無聯絡記錄</div>';
                    this.updatePendingContactsCount(0);
                    return;
                }

                container.innerHTML = '';
                let pendingCount = 0;

                this.currentContacts.contacts.forEach((contact, index) => {
                    if (contact.status === 'pending') {
                        pendingCount++;
                    }
                    const statusClass = contact.status === 'completed' ? 'success' :
                                       contact.status === 'cancelled' ? 'danger' : 'warning';
                    const statusText = contact.status === 'completed' ? '已回覆' :
                                      contact.status === 'cancelled' ? '已取消' : '待回覆';

                    const contactDiv = document.createElement('div');
                    contactDiv.className = 'border rounded p-3 mb-3';
                    contactDiv.innerHTML = `
                        <div class="d-flex justify-content-between align-items-start mb-2">
                            <h6 class="mb-0">聯絡 #${contact.id}</h6>
                            <span class="badge bg-${statusClass}">${statusText}</span>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <p class="mb-1"><strong>姓名：</strong>${this.escapeHtml(contact.name)}</p>
                                <p class="mb-1"><strong>Email：</strong>${this.escapeHtml(contact.email)}</p>
                                ${contact.phone ? `<p class="mb-1"><strong>電話：</strong>${this.escapeHtml(contact.phone)}</p>` : ''}
                                <p class="mb-1"><strong>主題：</strong>${this.escapeHtml(contact.subject)}</p>
                            </div>
                            <div class="col-md-6">
                                <p class="mb-1"><strong>提交時間：</strong>${new Date(contact.submit_time).toLocaleString('zh-TW')}</p>
                                <p class="mb-1"><strong>IP：</strong>${contact.submit_ip}</p>
                            </div>
                        </div>
                        <div class="mb-2">
                            <strong>訊息內容：</strong>
                            <div class="border rounded p-2 mt-1 bg-light">
                                ${this.escapeHtml(contact.message).replace(/\n/g, '<br>')}
                            </div>
                        </div>
                        ${contact.admin_notes ? `<p class="mb-2"><strong>管理員備註：</strong>${this.escapeHtml(contact.admin_notes)}</p>` : ''}
                    `;
                    container.appendChild(contactDiv);
                });

                this.updatePendingContactsCount(pendingCount);
            },

            updatePendingContactsCount(count) {
                const btn = document.getElementById('pendingContactsBtn');
                const countSpan = document.getElementById('pendingContactsCount');
                if (count > 0) {
                    btn.style.display = 'inline-block';
                    countSpan.textContent = count;
                } else {
                    btn.style.display = 'none';
                }
            },

            scrollToContacts() {
                document.getElementById('contactsList').scrollIntoView({ behavior: 'smooth' });
            },

            // 工具方法
            escapeHtml(text) {
                const div = document.createElement('div');
                div.textContent = text;
                return div.innerHTML;
            },

            // 系統設定方法
            testTwelvedataConnection() {
                toastr.info('測試 TwelveData 連接...');
                setTimeout(() => {
                    toastr.success('TwelveData 連接測試成功');
                }, 2000);
            },

            async saveSystemSettings() {
                const token = localStorage.getItem(this.tokenKey);
                if (!token) {
                    toastr.error('請先登入');
                    return;
                }

                try {
                    // 收集系統設定數據
                    const settingsData = {
                        captcha: {
                            enabled: document.getElementById('captchaEnabled')?.checked || false,
                            difficulty: document.getElementById('captchaDifficulty')?.value || 'easy'
                        },
                        rateLimit: {
                            enabled: document.getElementById('rateLimitEnabled')?.checked || false,
                            maxSubmissions: parseInt(document.getElementById('maxSubmissions')?.value) || 3,
                            timeWindow: 14400 // 4小時，固定值
                        },
                        updatedBy: '管理員'
                    };

                    const response = await fetch('/api/admin/settings', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': `Bearer ${token}`
                        },
                        body: JSON.stringify(settingsData)
                    });

                    const result = await response.json();

                    if (result.success) {
                        toastr.success('系統設定保存成功！');
                        // 重新載入設定以確認
                        await this.loadSystemSettings();
                    } else {
                        toastr.error(result.error || '保存失敗');
                    }
                } catch (error) {
                    console.error('保存系統設定失敗:', error);
                    toastr.error('保存失敗: ' + error.message);
                }
            },

            async loadSystemSettings() {
                const token = localStorage.getItem(this.tokenKey);
                if (!token) {
                    toastr.error('請先登入');
                    return;
                }

                try {
                    const response = await fetch('/api/admin/settings', {
                        method: 'GET',
                        headers: {
                            'Authorization': `Bearer ${token}`
                        }
                    });

                    if (response.ok) {
                        const settings = await response.json();
                        this.updateSystemSettingsDisplay(settings);
                    } else {
                        toastr.error('載入系統設定失敗');
                    }
                } catch (error) {
                    console.error('載入系統設定失敗:', error);
                    toastr.error('載入系統設定失敗');
                }
            },

            updateSystemSettingsDisplay(settings) {
                // 更新人機驗證設定
                if (settings.captcha) {
                    const captchaEnabled = document.getElementById('captchaEnabled');
                    const captchaDifficulty = document.getElementById('captchaDifficulty');

                    if (captchaEnabled) {
                        captchaEnabled.checked = settings.captcha.enabled || false;
                    }
                    if (captchaDifficulty) {
                        captchaDifficulty.value = settings.captcha.difficulty || 'easy';
                    }
                }

                // 更新提交限制設定
                if (settings.rateLimit) {
                    const rateLimitEnabled = document.getElementById('rateLimitEnabled');
                    const maxSubmissions = document.getElementById('maxSubmissions');

                    if (rateLimitEnabled) {
                        rateLimitEnabled.checked = settings.rateLimit.enabled || false;
                    }
                    if (maxSubmissions) {
                        maxSubmissions.value = settings.rateLimit.maxSubmissions || 3;
                    }
                }

                // 更新 TwelveData 設定
                if (settings.twelvedata) {
                    const twelvedataEnabled = document.getElementById('twelvedataEnabled');
                    const metalUpdateFrequency = document.getElementById('metalUpdateFrequency');
                    const currencyUpdateFrequency = document.getElementById('currencyUpdateFrequency');
                    const pauseOnIdle = document.getElementById('pauseOnIdle');
                    const idleTimeout = document.getElementById('idleTimeout');

                    if (twelvedataEnabled) {
                        twelvedataEnabled.checked = settings.twelvedata.enabled || false;
                    }
                    if (metalUpdateFrequency) {
                        metalUpdateFrequency.value = settings.twelvedata.frontendRefreshRate || 300;
                    }
                    if (currencyUpdateFrequency) {
                        currencyUpdateFrequency.value = (settings.twelvedata.updateInterval || 3600) / 3600;
                    }
                    if (pauseOnIdle) {
                        pauseOnIdle.checked = settings.twelvedata.pauseOnIdle !== false;
                    }
                    if (idleTimeout) {
                        idleTimeout.value = settings.twelvedata.idleTimeout || 300;
                    }
                }
            }
        }

        // 將 adminAuth 對象賦值給 window，以便全局訪問
        window.adminAuth = adminAuth;

        // 階段四新增：切換基礎價格的 readonly 狀態
        function toggleBasePriceReadonly(inputId, isAutoUpdate) {
            const input = document.getElementById(inputId);
            if (input) {
                if (isAutoUpdate) {
                    input.readOnly = true;
                    input.classList.add('calculated-price');
                    input.placeholder = '自動計算中...';
                    // 啟用自動更新時，立即計算價格
                    // if (window.adminPanel) {
                    //     window.adminPanel.calculateBasePrices();
                    // }
                    // 註解：價格計算已交由後端 app.py 處理，避免重複計算
                } else {
                    input.readOnly = false;
                    input.classList.remove('calculated-price');
                    input.placeholder = '手動輸入價格';
                }
            }
        }

        // 階段四新增：切換匯率的 readonly 狀態（匯率有不同的邏輯）
        function toggleCurrencyRateReadonly(inputId, isAutoUpdate) {
            const input = document.getElementById(inputId);
            if (input) {
                if (isAutoUpdate) {
                    input.readOnly = true;
                    input.classList.add('calculated-price');
                    input.placeholder = '自動更新中...';
                    // 匯率自動更新時不需要立即計算，等待 API 更新
                } else {
                    input.readOnly = false;
                    input.classList.remove('calculated-price');
                    input.placeholder = '手動輸入匯率';
                }
            }
        }

        // 階段四新增：初始化所有基礎價格欄位的狀態
        function initializeBasePriceStates() {
            // 所有需要檢查的基礎價格欄位和對應的自動更新開關
            const fieldMappings = [
                // 台幣基礎價格
                { priceField: 'gold_twd_base_price', autoUpdateField: 'gold_twd_auto_update' },
                { priceField: 'silver_twd_base_price', autoUpdateField: 'silver_twd_auto_update' },
                { priceField: 'palladium_twd_base_price', autoUpdateField: 'palladium_twd_auto_update' },
                { priceField: 'platinum_twd_base_price', autoUpdateField: 'platinum_twd_auto_update' },

                // 其他材料基礎價格
                { priceField: 'cobalt_kg_base_price', autoUpdateField: 'cobalt_kg_auto_update' },
                { priceField: 'tin_kg_base_price', autoUpdateField: 'tin_kg_auto_update' },

                // 實物交易基礎價格
                { priceField: 'gold_gram_base_price', autoUpdateField: 'gold_gram_auto_update' },
                { priceField: 'silver_gram_base_price', autoUpdateField: 'silver_gram_auto_update' },
                { priceField: 'palladium_gram_base_price', autoUpdateField: 'palladium_gram_auto_update' },
                { priceField: 'platinum_gram_base_price', autoUpdateField: 'platinum_gram_auto_update' },
                { priceField: 'rhodium_gram_base_price', autoUpdateField: 'rhodium_gram_auto_update' },
                { priceField: 'ruthenium_gram_base_price', autoUpdateField: 'ruthenium_gram_auto_update' },
                { priceField: 'iridium_gram_base_price', autoUpdateField: 'iridium_gram_auto_update' },
                { priceField: 'osmium_gram_base_price', autoUpdateField: 'osmium_gram_auto_update' }
            ];

            fieldMappings.forEach(mapping => {
                const priceInput = document.getElementById(mapping.priceField);
                const autoUpdateSwitch = document.getElementById(mapping.autoUpdateField);

                if (priceInput && autoUpdateSwitch) {
                    // 根據自動更新開關的當前狀態設置基礎價格欄位
                    toggleBasePriceReadonly(mapping.priceField, autoUpdateSwitch.checked);
                }
            });

            console.log('✅ 基礎價格欄位狀態初始化完成');
        }

        // 階段四新增：初始化基礎價格
        function initializeBasePrices() {
            // 等待 AdminPanel 實例創建完成
            // setTimeout(() => {
            //     if (window.adminPanel) {
            //         window.adminPanel.calculateBasePrices();
            //     }
            // }, 1000);
            // 註解：價格計算已交由後端 app.py 處理，避免重複計算
        }

        // 階段四新增：監聽美元價格和匯率變化
        function setupPriceCalculationListeners() {
            // 監聽美元基礎價格變化
            const usdPriceInputs = ['gold_base_price', 'silver_base_price', 'palladium_base_price', 'platinum_base_price'];
            usdPriceInputs.forEach(inputId => {
                const input = document.getElementById(inputId);
                if (input) {
                    input.addEventListener('input', () => {
                        // if (window.adminPanel) {
                        //     window.adminPanel.calculateBasePrices();
                        // }
                        // 註解：價格計算已交由後端 app.py 處理，避免重複計算
                    });
                }
            });

            // 監聽匯率變化
            const usdRateInput = document.getElementById('usd_rate');
            if (usdRateInput) {
                usdRateInput.addEventListener('input', () => {
                    // if (window.adminPanel) {
                    //     window.adminPanel.calculateBasePrices();
                    // }
                    // 註解：價格計算已交由後端 app.py 處理，避免重複計算
                });
            }
        }

        // 登出功能
        function logout() {
            localStorage.removeItem('admin_token');
            toastr.success('已登出，正在跳轉...');
            setTimeout(() => {
                window.location.href = '/admin-secure';
            }, 1000);
        }

        // 頁面載入完成後初始化管理員面板
        document.addEventListener('DOMContentLoaded', function() {
            window.adminPanel = new AdminPanel();

            // 階段四：初始化基礎價格欄位狀態（根據自動更新開關）
            initializeBasePriceStates();

            // 階段四：初始化基礎價格計算（僅對啟用自動更新的欄位）
            initializeBasePrices();

            // 階段四：設置價格計算監聽器
            setupPriceCalculationListeners();

            // 綁定管理員備註模態框事件
            const confirmActionBtn = document.getElementById('confirmActionBtn');
            if (confirmActionBtn) {
                confirmActionBtn.addEventListener('click', function() {
                    const itemId = document.getElementById('modalItemId').value;
                    const itemType = document.getElementById('modalItemType').value;
                    const action = document.getElementById('modalAction').value;
                    const adminNotes = document.getElementById('adminNotesInput').value;

                    if (itemType === 'booking') {
                        if (action === 'delete') {
                            adminAuth.executeBookingDelete(itemId);
                        } else {
                            adminAuth.executeBookingStatusUpdate(itemId, action, adminNotes);
                        }
                    } else if (itemType === 'contact') {
                        adminAuth.executeContactStatusUpdate(itemId, action, adminNotes);
                    }

                    // 關閉模態框
                    const modal = bootstrap.Modal.getInstance(document.getElementById('adminNotesModal'));
                    if (modal) {
                        modal.hide();
                    }
                });
            }
        });
    </script>

    <!-- 管理員備註模態框 -->
    <div class="modal fade" id="adminNotesModal" tabindex="-1" aria-labelledby="adminNotesModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="adminNotesModalLabel">更新狀態</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="adminNotesInput" class="form-label">管理員備註（選填）</label>
                        <textarea class="form-control" id="adminNotesInput" rows="3" placeholder="請輸入備註..."></textarea>
                    </div>
                    <!-- 隱藏欄位 -->
                    <input type="hidden" id="modalItemId">
                    <input type="hidden" id="modalItemType">
                    <input type="hidden" id="modalAction">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="confirmActionBtn">確認</button>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
