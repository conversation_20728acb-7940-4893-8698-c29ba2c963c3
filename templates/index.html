<!DOCTYPE html>
<html lang="zh-TW">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="專業金屬回收服務網站，提供貴金屬回收、市場價格、線上回收預約">
    <meta name="keywords" content="金屬回收, 黃金回收, 白銀回收, 銅回收, 鋁回收, 不鏽鋼回收">
    <meta name="author" content="迪奕科技">
    <title>首頁 - 迪奕科技金屬回收服務</title>

    <!-- Bulma CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='vendor/bulma/css/bulma.min.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/navbar.css') }}">
    
    <!-- 自定義樣式 -->
    <style>
        /* 自定義 Bulma 導航樣式 */
        .navbar {
            background-color: #2c3e50 !important;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 99999;
            box-shadow: 0 2px 10px rgba(44, 62, 80, 0.1);
        }
        
        .navbar-brand .navbar-item {
            font-size: 20px;
            font-weight: 700;
            color: #D4AF37 !important;
            display: flex;
            align-items: center;
        }
        
        .navbar-item {
            color: #ecf0f1 !important;
            font-weight: 500;
        }
        
        .navbar-item:hover,
        .navbar-item.is-active {
            background-color: #D4AF37 !important;
            color: white !important;
        }
        
        /* Navbar Logo 尺寸 */
        .navbar-logo {
            max-height: 44px;
            height: 44px;
            width: auto;
            display: inline-block;
            margin-right: 8px;
        }
        
        .navbar-burger {
            color: #ecf0f1 !important;
            border: 2px solid #ecf0f1;
            border-radius: 4px;
            width: 44px;
            height: 44px;
        }
        
        .navbar-burger span {
            background-color: #ecf0f1 !important;
        }
        
        .navbar-menu {
            background-color: #2c3e50 !important;
            box-shadow: 0 4px 16px rgba(44, 62, 80, 0.2);
        }
        
        /* 公告列 */
        .announcement-bar {
            background: #f5f5f5;
            color: #333;
            text-align: center;
            padding: 8px 12px;
            border-bottom: 1px solid #e0e0e0;
            margin-top: 3.25rem; /* 把原本 main 的位移放到這裡 */
        }
        .announcement-text {
            display: inline-block;
            font-size: 18px;
            letter-spacing: 0.5px;
        }
        .announcement-quotes {
            color: #7a7a7a;
            margin: 0 4px;
        }
        
        @media screen and (max-width: 1023px) {
            .navbar-menu {
                border-top: 1px solid #95a5a6;
            }
            
            .navbar-item {
                padding: 16px 24px;
                font-size: 18px;
            }
        }
        
        /* 主要內容樣式 */
        main {
            margin-top: 0; /* 改由公告列負責推開內容 */
            padding: 20px;
        }
        
        .hero {
            background: linear-gradient(135deg, #D4AF37 0%, #B8860B 100%);
            color: white;
            padding: 60px 20px;
            text-align: center;
            border-radius: 10px;
            margin-bottom: 40px;
        }
        
        .hero h1 {
            font-size: 2.5rem;
            margin-bottom: 20px;
        }
        
        .hero p {
            font-size: 1.2rem;
            margin-bottom: 15px;
        }
        
        .hero-announcement {
            font-size: 2rem;
            font-weight: 700;
            letter-spacing: 1px;
        }
        
        /* 圖片輪播自定義樣式 - 使用自定義類名避免與Bulma衝突 */
        .diyi-carousel {
            margin-top: 30px;
            position: relative;
        }
        
        .diyi-carousel-container {
            position: relative;
            max-width: 1200px;
            margin: 0 auto;
            overflow: hidden;
            border-radius: 8px;
        }
        
        .diyi-carousel-items {
            display: flex;
            transition: transform 0.5s ease;
        }
        
        .diyi-carousel-item {
            min-width: 100%;
            flex: 0 0 auto;
            position: relative;
            /* 保持固定的寬高比例 - 800:1920 = 0.4166667 */
            padding-top: 41.66667%; /* 800/1920 的比例 */
        }
        
        .diyi-carousel-item img {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: cover;
            object-position: center;
        }
        
        .diyi-carousel-arrow {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(0,0,0,0.6);
            color: white;
            border: none;
            border-radius: 50%;
            width: 48px;
            height: 48px;
            font-size: 24px;
            line-height: 1;
            cursor: pointer;
            z-index: 10;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .diyi-carousel-arrow:hover {
            background: rgba(0,0,0,0.8);
            transform: translateY(-50%) scale(1.1);
        }
        
        .diyi-carousel-arrow-prev {
            left: 20px;
        }
        
        .diyi-carousel-arrow-next {
            right: 20px;
        }
        
        .diyi-carousel-indicators {
            position: absolute;
            bottom: 20px;
            left: 0;
            right: 0;
            display: flex;
            justify-content: center;
            gap: 12px;
        }
        
        .diyi-carousel-indicator {
            width: 14px;
            height: 14px;
            border-radius: 50%;
            border: 2px solid white;
            background: rgba(255,255,255,0.5);
            padding: 0;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .diyi-carousel-indicator.active {
            background: white;
            transform: scale(1.2);
        }
        
        /* 響應式調整 */
        @media screen and (max-width: 768px) {
            .diyi-carousel-arrow {
                width: 36px;
                height: 36px;
                font-size: 18px;
            }
            
            .diyi-carousel-arrow-prev {
                left: 10px;
            }
            
            .diyi-carousel-arrow-next {
                right: 10px;
            }
            
            .diyi-carousel-indicator {
                width: 10px;
                height: 10px;
            }
        }
        
        /* 底部 2x2 連結方塊 */
        .link-grid {
            display: grid;
            grid-template-columns: repeat(2, minmax(140px, 1fr));
            gap: 24px;
            max-width: 680px;
            margin: 0 auto 40px;
        }
        .link-box {
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            min-height: 120px;
            border: 3px solid #000;
            border-radius: 4px;
            background: #ffffff;
            color: #2c3e50;
            font-weight: 700;
            text-decoration: none;
        }
        .link-box:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 14px rgba(0,0,0,0.08);
            color: #2c3e50;
        }
        .link-box.is-disabled {
            pointer-events: none;
            cursor: default;
        }
        .site-footer {
            text-align: center;
            color: #2c3e50;
            padding: 24px 0; 
            font-size: 14px;
            border-top: 1px solid #eee;
        }
        

        /* 品牌文字（Logo 右側） */
        .brand-text {
            display: flex;
            flex-direction: column;
            line-height: 1.1;
            margin-left: 8px;
        }
        .brand-title {
            color: #D4AF37 !important;
            font-weight: 700;
            font-size: 20px;
        }
        .brand-subtitle {
            color: #ecf0f1 !important;
            font-weight: 500;
            font-size: 12px;
            letter-spacing: 0.3px;
            margin-top: 2px;
            opacity: 0.9;
        }

    </style>
</head>

<body>


    <!-- Bulma 導航選單 -->
    {% set active_page = 'index' %}
    {% include 'partials/navbar.html' %}

    <!-- 公告列：顯示『 』包住的文字（請將內容填入 span.announcement-text） -->
    <div class="announcement-bar">
        <span class="announcement-quotes">『</span>
        <span class="announcement-text">唯一供應皇翔貴金屬有限公司請洽詢門市</span>
        <span class="announcement-quotes">』</span>
    </div>

    <!-- 主要內容 -->
    <main>
        <!-- Hero Section -->
        <section class="hero">
            <h2 class="hero-announcement">『唯一供應皇翔貴金屬有限公司請洽詢門市』</h2>
            <!-- 圖片輪播組件 -->
            <div class="diyi-carousel">
                <!-- 輪播容器 -->
                <div class="diyi-carousel-container">
                    <!-- 輪播項目 -->
                    <div class="diyi-carousel-items">
                        <div class="diyi-carousel-item">
                            <img src="{{ url_for('static', filename='images/home1.jpg') }}" alt="圖片1">
                        </div>
                        <div class="diyi-carousel-item">
                            <img src="{{ url_for('static', filename='images/home2.jpg') }}" alt="圖片2">
                        </div>
                        <div class="diyi-carousel-item">
                            <img src="{{ url_for('static', filename='images/home3.jpg') }}" alt="圖片3">
                        </div>
                    </div>
                    
                    <!-- 箭頭控制 -->
                    <button class="diyi-carousel-arrow diyi-carousel-arrow-prev">&#10094;</button>
                    <button class="diyi-carousel-arrow diyi-carousel-arrow-next">&#10095;</button>
                    
                    <!-- 指示器 -->
                    <div class="diyi-carousel-indicators">
                        <button class="diyi-carousel-indicator active" data-index="0"></button>
                        <button class="diyi-carousel-indicator" data-index="1"></button>
                        <button class="diyi-carousel-indicator" data-index="2"></button>
                    </div>
                </div>
            </div>
        </section>

        <!-- 底部 2x2 連結方塊 -->
        <section class="link-grid">
            <a href="products.html" class="link-box">產品介紹</a>
            <a href="booking.html" class="link-box">回收預約</a>
            <a href="market-data.html" class="link-box">價格走勢</a>
            <div class="link-box is-disabled">ESG<br>環境資源<br>永續利用</div>
        </section>
    </main>

    <footer class="site-footer">迪奕科技 版權所有</footer>

    <!-- 價格讀取器 -->
    <script src="/static/js/price-reader.js"></script>
    
    <!-- Bulma 導航 JavaScript -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 初始化 Bulma 導航系統...');
            
            // 獲取 Bulma navbar burger
            const navbarBurger = document.querySelector('.navbar-burger');
            const navbarMenu = document.getElementById('navbarMenu');
            
            if (navbarBurger && navbarMenu) {
                // 漢堡選單點擊事件
                navbarBurger.addEventListener('click', function() {
                    console.log('🖱️ Bulma 漢堡選單被點擊');
                    
                    // 切換活動狀態
                    navbarBurger.classList.toggle('is-active');
                    navbarMenu.classList.toggle('is-active');
                    
                    // 更新 aria 屬性
                    const isExpanded = navbarBurger.classList.contains('is-active');
                    navbarBurger.setAttribute('aria-expanded', isExpanded);
                    
                    console.log(isExpanded ? '🔓 導航已開啟' : '🔒 導航已關閉');
                });
                
                // 點擊選單項目時關閉手機版選單
                const navbarItems = navbarMenu.querySelectorAll('.navbar-item');
                navbarItems.forEach(item => {
                    item.addEventListener('click', function() {
                        if (navbarMenu.classList.contains('is-active')) {
                            console.log('🔗 選單項目被點擊，關閉手機版選單');
                            navbarBurger.classList.remove('is-active');
                            navbarMenu.classList.remove('is-active');
                            navbarBurger.setAttribute('aria-expanded', 'false');
                        }
                        
                        // 更新活動狀態
                        navbarItems.forEach(nav => nav.classList.remove('is-active'));
                        this.classList.add('is-active');
                    });
                });
                
                console.log('✅ Bulma 導航系統初始化完成');
            } else {
                console.log('❌ 找不到 Bulma 導航元素');
            }
            
            // 初始化圖片輪播組件
            initCarousel();
        });
        
        // 圖片輪播功能
        function initCarousel() {
            console.log('🖼️ 初始化圖片輪播組件...');
            
            const carouselItems = document.querySelector('.diyi-carousel-items');
            const prevBtn = document.querySelector('.diyi-carousel-arrow-prev');
            const nextBtn = document.querySelector('.diyi-carousel-arrow-next');
            const indicators = document.querySelectorAll('.diyi-carousel-indicator');
            let currentIndex = 0;
            const totalItems = document.querySelectorAll('.diyi-carousel-item').length;
            
            // 更新輪播位置
            function updateCarousel() {
                carouselItems.style.transform = `translateX(-${currentIndex * 100}%)`;
                
                // 更新指示器狀態
                indicators.forEach((indicator, index) => {
                    if (index === currentIndex) {
                        indicator.classList.add('active');
                    } else {
                        indicator.classList.remove('active');
                    }
                });
            }
            
            // 下一張圖片
            function nextSlide() {
                currentIndex = (currentIndex + 1) % totalItems;
                updateCarousel();
            }
            
            // 上一張圖片
            function prevSlide() {
                currentIndex = (currentIndex - 1 + totalItems) % totalItems;
                updateCarousel();
            }
            
            // 綁定按鈕事件
            if (nextBtn && prevBtn) {
                nextBtn.addEventListener('click', nextSlide);
                prevBtn.addEventListener('click', prevSlide);
            }
            
            // 綁定指示器點擊事件
            indicators.forEach((indicator, index) => {
                indicator.addEventListener('click', () => {
                    currentIndex = index;
                    updateCarousel();
                });
            });
            
            // 自動輪播
            let autoplayInterval = setInterval(nextSlide, 5000); // 5秒切換一次
            
            // 滑鼠懸停時暫停自動輪播
            const carousel = document.querySelector('.diyi-carousel');
            carousel.addEventListener('mouseenter', () => {
                clearInterval(autoplayInterval);
            });
            
            // 滑鼠離開時恢復自動輪播
            carousel.addEventListener('mouseleave', () => {
                autoplayInterval = setInterval(nextSlide, 5000);
            });
            
            console.log('✅ 輪播組件初始化完成，總共 ' + totalItems + ' 張圖片');
        }
    </script>
</body>

</html>
