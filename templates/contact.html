<!DOCTYPE html>
<html lang="zh-TW">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="聯絡迪奕科技金屬回收服務，提供多種聯絡方式和即時客服支援">
    <meta name="keywords" content="聯絡我們, 客服支援, 金屬回收, 黃金回收, 白銀回收, 聯絡方式">
    <meta name="author" content="迪奕科技">
    <title>聯絡我們 - 迪奕科技金屬回收服務</title>

    <!-- Bulma CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='vendor/bulma/css/bulma.min.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/navbar.css') }}">
    
    <!-- 自定義樣式 -->
    <style>
        /* 自定義 Bulma 導航樣式 */
        .navbar {
            background-color: #2c3e50 !important;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 99999;
            box-shadow: 0 2px 10px rgba(44, 62, 80, 0.1);
        }
        
        .navbar-brand .navbar-item {
            font-size: 20px;
            font-weight: 700;
            color: #D4AF37 !important;
        }
        
        .navbar-item {
            color: #ecf0f1 !important;
            font-weight: 500;
        }
        
        .navbar-item:hover,
        .navbar-item.is-active {
            background-color: #D4AF37 !important;
            color: white !important;
        }
        
        .navbar-burger {
            color: #ecf0f1 !important;
            border: 2px solid #ecf0f1;
            border-radius: 4px;
            width: 44px;
            height: 44px;
        }
        
        .navbar-burger span {
            background-color: #ecf0f1 !important;
        }
        
        .navbar-menu {
            background-color: #2c3e50 !important;
            box-shadow: 0 4px 16px rgba(44, 62, 80, 0.2);
        }
        
        @media screen and (max-width: 1023px) {
            .navbar-menu {
                border-top: 1px solid #95a5a6;
            }
            
            .navbar-item {
                padding: 16px 24px;
                font-size: 18px;
            }
        }
        
        /* 主要內容樣式 */
        main {
            margin-top: 3.25rem; /* Bulma navbar 高度 */
            padding: 20px;
        }
        
        .page-header {
            background: linear-gradient(135deg, #D4AF37 0%, #B8860B 100%);
            color: white;
            padding: 40px 20px;
            text-align: center;
            border-radius: 10px;
            margin-bottom: 40px;
        }
        
        .page-header h1 {
            font-size: 2.5rem;
            margin-bottom: 20px;
        }
        
        .page-header p {
            font-size: 1.2rem;
            margin-bottom: 15px;
        }
        
        .contact-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .contact-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }
        
        .contact-info-section {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(44, 62, 80, 0.1);
            height: 400px;
            display: flex;
            flex-direction: column;
        }
        
        .contact-items-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            flex: 1;
            align-content: center;
        }
        
        .contact-info-section h3 {
            color: #D4AF37;
            margin-bottom: 25px;
            padding-bottom: 10px;
            border-bottom: 2px solid #D4AF37;
            font-size: 1.5rem;
            font-weight: bold;
        }
        
        .contact-item {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            padding: 15px;
            background: #ecf0f1;
            border-radius: 8px;
            transition: all 0.3s;
        }
        
        .contact-item:hover {
            background: #D4AF37;
            transform: translateX(5px);
        }
        
        .contact-item:hover .contact-details h4,
        .contact-item:hover .contact-details p {
            color: white !important;
        }
        
        .contact-icon {
            font-size: 1.5rem;
            margin-right: 15px;
            color: #D4AF37;
            min-width: 30px;
        }
        
        .contact-details h4 {
            margin: 0 0 5px 0;
            color: #D4AF37 !important;
            font-size: 1.1rem;
            font-weight: bold;
        }
        
        .contact-details p {
            margin: 0;
            color: #000000 !important;
            line-height: 1.4;
        }
        
        .contact-details a {
            color: #000000;
            text-decoration: none;
            font-weight: 500;
        }
        
        .contact-details a:hover {
            text-decoration: underline;
            color: #2c3e50;
        }
        
        .contact-form-section {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(44, 62, 80, 0.1);
        }
        
        .contact-form-section h3 {
            color: #2c3e50;
            margin-bottom: 25px;
            padding-bottom: 10px;
            border-bottom: 2px solid #D4AF37;
            font-size: 1.5rem;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #2c3e50;
            font-weight: 500;
        }
        
        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #95a5a6;
            border-radius: 6px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        
        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #D4AF37;
        }
        
        .form-group textarea {
            height: 120px;
            resize: vertical;
        }
        
        .submit-btn {
            background: linear-gradient(135deg, #D4AF37 0%, #B8860B 100%);
            color: white;
            padding: 15px 40px;
            border: none;
            border-radius: 6px;
            font-size: 18px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s;
            width: 100%;
        }
        
        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(44, 62, 80, 0.2);
        }
        
        .office-hours {
            background: #ecf0f1;
            padding: 30px;
            border-radius: 10px;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .office-hours h3 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.5rem;
        }
        
        .hours-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }
        
        .hours-item {
            text-align: center;
            padding: 20px;
            background: white;
            border-radius: 8px;
            border-left: 4px solid #D4AF37;
        }
        
        .hours-item h4 {
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 1.1rem;
        }
        
        .hours-item p {
            color: #95a5a6;
            margin: 0;
            font-weight: 500;
        }
        
        .map-section {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(44, 62, 80, 0.1);
            height: 400px;
            display: flex;
            flex-direction: column;
        }
        
        .map-section h3 {
            color: #D4AF37;
            margin-bottom: 20px;
            text-align: center;
            font-size: 1.5rem;
            font-weight: bold;
        }
        
        .map-placeholder {
            background: #ecf0f1;
            border: 2px dashed #95a5a6;
            border-radius: 8px;
            padding: 60px 20px;
            text-align: center;
            color: #95a5a6;
        }
        
        .map-placeholder h4 {
            margin-bottom: 10px;
            color: #2c3e50;
        }
        
        /* 移動端響應式 */
        @media (max-width: 768px) {
            .contact-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .hours-grid {
                grid-template-columns: 1fr;
            }
        }
        
    </style>
</head>

<body>
    <!-- Bulma 導航選單 -->
    {% set active_page = 'contact' %}
    {% include 'partials/navbar.html' %}

    <!-- 主要內容 -->
    <main>
        <!-- 頁面標題 -->
        <section class="page-header">
            <h1>聯絡我們</h1>
            <p>我們隨時為您提供專業的金屬回收服務支援</p>
            <p>多種聯絡方式，讓您輕鬆與我們取得聯繫</p>
        </section>

        <div class="contact-container">
            <!-- 聯絡資訊 -->
            <section class="contact-info-section">
                <h3>📞 聯絡資訊</h3>
                
                <div class="contact-items-grid">
                    <div class="contact-item">
                        <div class="contact-icon">📱</div>
                        <div class="contact-details">
                            <h4>客服專線</h4>
                            <p><a href="tel:+886422202326">04-22202326</a></p>
                        </div>
                    </div>
                    
                    <div class="contact-item">
                        <div class="contact-icon">📧</div>
                        <div class="contact-details">
                            <h4>電子郵件</h4>
                            <p><a href="mailto:<EMAIL>"><EMAIL></a></p>
                        </div>
                    </div>
                    
                    <div class="contact-item">
                        <div class="contact-icon">📍</div>
                        <div class="contact-details">
                            <h4>門市地址</h4>
                            <p>台中市中區光復里臺灣大道一段311號一樓</p>
                        </div>
                    </div>
                    
                    <div class="contact-item">
                        <div class="contact-icon">🕒</div>
                        <div class="contact-details">
                            <h4>服務時間</h4>
                            <p>週一至週五<br>10:00 ～ 17:00</p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Google Maps 嵌入 -->
            <section class="map-section">
                <h3>🗺️ 門市位置</h3>
                <div class="map-container" style="width: 100%; height: 300px; border-radius: 8px; overflow: hidden; flex: 1;">
                    <iframe 
                        src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3640.8559272298985!2d120.67740757609033!3d24.141697278405537!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x34693d12d3e53ecb%3A0x6fb7602181a23e4f!2zNDAw5Y-w5Lit5biC5Lit5Y2A6Ie654Gj5aSn6YGT5LiA5q61MzEx6Jmf!5e0!3m2!1szh-TW!2stw!4v1755932238413!5m2!1szh-TW!2stw"
                        width="100%" 
                        height="300" 
                        style="border:0;" 
                        allowfullscreen="" 
                        loading="lazy" 
                        referrerpolicy="no-referrer-when-downgrade"
                        title="迪奕科技門市位置">
                    </iframe>
                </div>
                <div style="text-align: center; margin-top: 15px; color: #D4AF37; font-size: 0.9rem;">
                    <p><strong>📍 台中市中區光復里臺灣大道一段311號一樓</strong></p>
                    <p>點擊地圖可開啟 Google Maps 進行導航</p>
                </div>
            </section>




        </div>
    </main>

    {% include 'partials/footer.html' %}

    <!-- Font Awesome -->
    <script src="{{ url_for('static', filename='vendor/fontawesome/js/all.min.js') }}"></script>

    <!-- Bootstrap JS -->
    <script src="{{ url_for('static', filename='vendor/bootstrap/js/bootstrap.bundle.min.js') }}"></script>

    <!-- jQuery (toastr 需要) -->
    <script src="{{ url_for('static', filename='vendor/jquery/jquery.min.js') }}"></script>

    <!-- Toastr CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='vendor/toastr/css/toastr.min.css') }}">

    <!-- Toastr JS -->
    <script src="{{ url_for('static', filename='vendor/toastr/js/toastr.min.js') }}"></script>

    <!-- 聯絡我們表單處理器 -->
    <script src="/static/js/contact-form.js"></script>

    <!-- Bulma 導航 JavaScript -->
    <script>
        // 表單提交現在由 contact-form.js 處理
        
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 初始化 Bulma 導航系統...');
            
            // 獲取 Bulma navbar burger
            const navbarBurger = document.querySelector('.navbar-burger');
            const navbarMenu = document.getElementById('navbarMenu');
            
            if (navbarBurger && navbarMenu) {
                // 漢堡選單點擊事件
                navbarBurger.addEventListener('click', function() {
                    console.log('🖱️ Bulma 漢堡選單被點擊');
                    
                    // 切換活動狀態
                    navbarBurger.classList.toggle('is-active');
                    navbarMenu.classList.toggle('is-active');
                    
                    // 更新 aria 屬性
                    const isExpanded = navbarBurger.classList.contains('is-active');
                    navbarBurger.setAttribute('aria-expanded', isExpanded);
                    
                    console.log(isExpanded ? '🔓 導航已開啟' : '🔒 導航已關閉');
                });
                
                // 點擊選單項目時關閉手機版選單
                const navbarItems = navbarMenu.querySelectorAll('.navbar-item');
                navbarItems.forEach(item => {
                    item.addEventListener('click', function() {
                        if (navbarMenu.classList.contains('is-active')) {
                            console.log('🔗 選單項目被點擊，關閉手機版選單');
                            navbarBurger.classList.remove('is-active');
                            navbarMenu.classList.remove('is-active');
                            navbarBurger.setAttribute('aria-expanded', 'false');
                        }
                        
                        // 更新活動狀態
                        navbarItems.forEach(nav => nav.classList.remove('is-active'));
                        this.classList.add('is-active');
                    });
                });
                
                console.log('✅ Bulma 導航系統初始化完成');
            } else {
                console.log('❌ 找不到 Bulma 導航元素');
            }
            
            // 表單提交事件現在由 contact-form.js 處理
        });
    </script>
</body>

</html>