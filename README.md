# Flask 金屬價格管理 API v4.0

這是一個使用 Flask 構建的金屬價格管理系統，整合 TwelveData API 提供即時金屬價格，支持智能閒置管理和 Session 保護。

## 🎉 20250905 更新完成

### ✅ 系統修復完成（4個階段全部修復）
- **階段1**：修正實物價格控制未遵守關閉自動更新配置問題 ✅
- **階段2**：修正匯率買入賣出倍率修改未生效問題 ✅  
- **階段3**：修正金屬價格數據變成 null 問題 ✅
- **階段4**：優化 API 查詢邏輯和404處理，防止重複查詢已禁用項目 ✅

### 🔧 新增 Cron 更新機制改進

#### 雙重更新策略
系統現在提供兩種 cron 更新函數供選擇：

1. **`has_auto_update_items()`** - 純 auto_update 檢查（預設）
   - 只檢查是否有項目啟用 auto_update
   - 不考慮更新頻率，有啟用就更新
   - 效能較好，適合頻繁更新需求
   - 更新頻率由外部 cron 腳本控制

2. **`has_items_to_update()`** - 頻率控制檢查
   - 同時檢查 auto_update 和更新頻率
   - 只有超過設定頻率才觸發更新
   - 更精準的更新控制，節省 API 配額

#### 🔄 切換方法
在 `should_update_twelvedata_for_cron()` 函數中（約第3442行）：

**當前配置（純 auto_update 檢查）：**
```python
# Cron 更新只檢查是否有啟用 auto_update 的項目，不檢查更新頻率
result = has_auto_update_items()
```

**切換到頻率控制：**
```python
# 切換為頻率控制模式
result = has_items_to_update()
```

#### 📊 兩種模式比較
| 特性 | `has_auto_update_items()` | `has_items_to_update()` |
|------|---------------------------|-------------------------|
| **檢查項目** | 只檢查 auto_update | auto_update + 更新頻率 |
| **更新觸發** | 有啟用就更新 | 超過頻率才更新 |
| **效能** | 較快 | 較慢（需計算時間） |
| **API 使用** | 較多 | 較少（頻率控制） |
| **適用場景** | 外部 cron 控制頻率 | 內建頻率控制 |

---

## 🎯 階段四更新 (2025-08-26)

### 新增功能
- **完整管理員界面**：支援 8 種金屬 + 2 種其他材料 + 3 種匯率的完整管理
- **TwelveData 深度整合**：細粒度的 API 配額管理和自動更新控制
- **前端數據結構優化**：完全支援前端所需的所有表格數據
- **響應式管理面板**：三個控制面板並排布局，手機友好
- **細粒度更新頻率**：每個金屬和匯率可設定獨立的更新間隔

### 支援的金屬和材料
- **貴金屬 (8種)**：金Au、銀Ag、鈀Pd、鉑Pt、銠Rh、釕Ru、銥Ir、鋨Os
- **其他材料 (2種)**：鈷Co、錫Sn
- **匯率 (3種)**：美元、人民幣、歐元

### 前端表格支援
- **商品/美元表格**：4種貴金屬的美元報價
- **商品/台幣表格**：4種貴金屬的台幣報價
- **實物交易表格**：8種金屬的公克報價
- **其他材料表格**：2種材料的公斤報價
- **貨幣項目表格**：3種匯率（供參考無外幣買賣）

## 🚀 功能特色

### 基本功能
- **Web 管理介面**: 直觀的價格管理表單
- **表單驗證**: 完整的輸入驗證和錯誤處理
- **RESTful API**: 提供 JSON 格式的價格查詢和更新
- **CORS 支持**: 支持跨域請求
- **Unix 時間戳緩存**: 配合前端緩存機制
- **健康檢查**: 提供服務狀態監控

### TwelveData API 整合
- **即時價格**: 支援從 TwelveData API 獲取即時黃金價格
- **混合模式**: 自動混合 TwelveData 價格和管理員設定價格
- **智能緩存**: 避免超出 API 限制的智能緩存機制
- **自動重試**: 區分網絡波動和 Key 錯誤，智能重試機制

### 智能閒置管理
- **按需更新**: 只有在有用戶訪問時才向 TwelveData 請求數據
- **閒置暫停**: 超過設定時間無用戶訪問時自動暫停 API 請求
- **智能恢復**: 用戶重新訪問時自動恢復，並根據時間間隔決定是否立即更新
- **節省配額**: 有效節省免費版本的 API 請求配額

### Session 保護機制
- **防外鏈**: 只允許來自同域名的請求訪問價格 API
- **機器人過濾**: 自動識別並拒絕機器人請求
- **Session 驗證**: 每個用戶需要有效的 session 才能獲取價格數據
- **活動追蹤**: 追蹤活躍用戶數量和最後活動時間

## 📦 安裝和運行

### 1. 安裝依賴

```bash
cd flask-api
pip install -r requirements.txt
```

### 2. 運行應用

```bash
python app.py
```

首次運行會自動：
- 創建數據目錄和初始價格文件
- 檢查文件權限
- 顯示系統信息

應用將在 `http://localhost:5000` 啟動。

### 3. 配置 TwelveData API（可選）

如需使用即時價格功能，請在 `.env` 文件中設定 TwelveData API Key：

```bash
TWELVEDATA_API_KEY=your_api_key_here
```

### 4. 訪問管理介面

- **主頁面**: `http://localhost:5000`
- **市場數據**: `http://localhost:5000/market-data`
- **管理介面**: `http://localhost:5000/admin-secure`

## 🔌 API 端點

### 公開 API

#### 獲取完整價格數據（階段四）

```http
GET /api/prices?t=1755669600
```

**階段四響應示例：**
```json
{
  "gold": 2045.80,
  "silver": 24.50,
  "copper": 185.00,
  "aluminum": 45.00,
  "metals": {
    "usd_table": {
      "gold": {
        "usd_per_oz": 2045.80,
        "buy_usd_per_oz": 2004.88,
        "sell_usd_per_oz": 2086.72,
        "enabled": true
      },
      "silver": { "..." },
      "palladium": { "..." },
      "platinum": { "..." }
    },
    "twd_table": {
      "gold": {
        "twd_per_tael": 75000,
        "buy_twd_per_tael": 73500,
        "sell_twd_per_tael": 76500,
        "enabled": true
      }
    },
    "physical_table": {
      "gold": {
        "buy_twd_per_gram": 2000,
        "sell_twd_per_gram": 2040,
        "enabled": true
      },
      "rhodium": { "..." },
      "ruthenium": { "..." },
      "iridium": { "..." },
      "osmium": { "..." }
    }
  },
  "other_materials": {
    "cobalt": {
      "buy_twd_per_kg": 1050,
      "sell_twd_per_kg": 1155,
      "enabled": true
    },
    "tin": { "..." }
  },
  "currencies": {
    "usd": {
      "twd_rate": 31.5,
      "buy_rate": 31.44,
      "sell_rate": 31.56,
      "enabled": true
    },
    "cny": { "..." },
    "eur": { "..." }
  },
  "version": "4.0",
  "twelvedata_status": {
    "daily_usage": 5,
    "daily_limit": 800,
    "is_paused": false
  }
}
```

#### 獲取 TwelveData 即時價格

```http
GET /api/prices/twelvedata?t=1755669600
```

#### 獲取公開系統設定

```http
GET /api/settings/public
```

### 管理員 API（需要認證）

#### 更新 TwelveData 配置

```http
POST /api/admin/twelvedata/update-config
Authorization: Bearer YOUR_ADMIN_TOKEN
Content-Type: application/json

{
  "enabled": true,
  "api_key": "your_twelvedata_api_key",
  "update_interval_seconds": 3600,
  "pause_on_idle": true,
  "idle_timeout_seconds": 300
}
```

#### 強制更新價格

```http
POST /api/admin/twelvedata/force-update
Authorization: Bearer YOUR_ADMIN_TOKEN
```

**響應示例：**
```json
{
  "success": true,
  "message": "成功更新 4 個價格",
  "updated_count": 4,
  "errors": [],
  "quota_status": {
    "daily_usage": 8,
    "daily_limit": 800,
    "minute_usage": 4,
    "minute_limit": 8,
    "is_paused": false
  }
}
```

#### 更新價格數據

```http
POST /api/prices
Content-Type: application/json

{
  "gold": 2050.00,
  "silver": 25.00,
  "copper": 190.00,
  "aluminum": 50.00,
  "updatedBy": "API用戶"
}
```

### 健康檢查

```http
GET /health
```

**響應示例：**
```json
{
  "status": "healthy",
  "timestamp": "2024-01-20T10:30:00",
  "service": "Price API"
}
```

## 🔧 配置

### 環境變數

應用會自動生成 `.env` 文件，包含以下配置：

```bash
# Flask 應用密鑰（用於 session 和 CSRF）
FLASK_SECRET_KEY=auto_generated_key

# 管理員認證 token
ADMIN_TOKEN=auto_generated_token

# TwelveData API 金鑰（需要手動填入）
TWELVEDATA_API_KEY=

# 其他配置
FLASK_ENV=production
FLASK_HOST=0.0.0.0
FLASK_PORT=5000
```

**重要**：請在 `.env` 文件中填入您的 TwelveData API Key 以啟用即時價格功能。

### CORS 設置

默認允許的來源：
- `http://localhost:3000`
- `http://localhost:8000`
- `http://127.0.0.1:3000`
- `http://127.0.0.1:8000`

如需添加其他來源，請修改 `app.py` 中的 CORS 配置。

## 📁 文件結構

```
flask-api/
├── app.py                 # 主應用文件（包含所有功能）
├── requirements.txt       # Python 依賴
├── README.md             # 說明文件
├── data/                 # 數據目錄（自動創建）
│   └── prices.json       # 價格數據文件（自動創建）
├── templates/            # HTML 模板
│   └── price_admin.html  # 管理介面模板
└── static/              # 靜態文件（如需要）
```

## 🌍 跨平台支持

本 Flask API 支持在以下平台部署：

- **Windows** (Windows 10/11, Windows Server)
- **macOS** (macOS 10.14+)
- **Linux** (Ubuntu, CentOS, Debian, etc.)

### 路徑處理

應用使用智能路徑檢測，自動適配不同作業系統：

```python
# 自動檢測執行環境
if getattr(sys, 'frozen', False):
    # 打包成 exe 的情況
    executable_dir = os.path.dirname(sys.executable)
else:
    # 直接運行 .py 文件的情況
    executable_dir = os.path.dirname(os.path.abspath(__file__))

# 跨平台路徑構建
DATA_DIR = os.path.join(executable_dir, "data")
PRICES_FILE = os.path.join(DATA_DIR, "prices.json")
```

## 🔒 安全注意事項

1. **生產環境**: 請更改 `SECRET_KEY`
2. **CORS 設置**: 根據實際需求限制允許的來源
3. **輸入驗證**: 已實現基本的表單驗證
4. **錯誤處理**: 包含完整的錯誤處理機制

## 📊 TwelveData API 整合

### 功能特色

#### 智能閒置管理
- **按需更新**：只有在有用戶訪問時才向 TwelveData 請求數據
- **閒置暫停**：超過設定時間無用戶訪問時自動暫停 API 請求
- **智能恢復**：用戶重新訪問時自動恢復，並根據時間間隔決定是否立即更新
- **節省配額**：有效節省免費版本的 API 請求配額

#### Session 保護機制
- **防外鏈**：只允許來自同域名的請求訪問價格 API
- **機器人過濾**：自動識別並拒絕機器人請求
- **Session 驗證**：每個用戶需要有效的 session 才能獲取價格數據
- **活動追蹤**：追蹤活躍用戶數量和最後活動時間

#### 智能 API Key 管理
- **自動驗證**：每15分鐘自動測試 API Key 有效性
- **錯誤區分**：區分網絡波動和 Key 驗證錯誤
- **智能重試**：網絡錯誤自動重試，Key 錯誤需手動更新
- **狀態監控**：即時監控 API Key 狀態和網絡重試次數

### 配置說明

#### 系統設定
```json
{
  "twelvedata": {
    "enabled": false,
    "updateInterval": 3600,
    "frontendRefreshRate": 300,
    "idleTimeout": 300,
    "pauseOnIdle": true
  }
}
```

#### 免費版本限制
- 每日 800 次請求
- 只支援基本金屬價格（主要是黃金）
- 白銀、銅、鋁需要付費版本

#### 建議設定
- 更新間隔：3600 秒（1 小時）
- 前端刷新率：300 秒（5 分鐘）
- 閒置超時：300 秒（5 分鐘）
- 閒置暫停：啟用

### 價格混合策略

1. **TwelveData 啟用且 Key 有效**：
   - 黃金價格：使用 TwelveData 即時價格
   - 其他金屬：使用管理員設定價格
   - 來源標記：`TwelveData API + 管理員設定`

2. **TwelveData 停用或 Key 無效**：
   - 所有價格：使用管理員設定價格
   - 來源標記：`Flask API (TwelveData 備援)`

3. **網絡錯誤時**：
   - 自動重試最多 3 次
   - 超過重試次數後暫停請求
   - 錯誤信息記錄在日誌中

## 🧪 測試

### 單元測試
```bash
python3 test_twelvedata.py           # 測試 TwelveData API 連接
python3 test_integration.py          # 完整整合測試
python3 test_session_protection.py   # Session 保護和閒置管理測試
python3 test_api_key_management.py   # API Key 管理和智能重試測試
```

### 測試 API 端點

```bash
# 獲取管理員價格
curl http://localhost:5000/api/prices

# 獲取 TwelveData 價格（需要有效 session）
curl -b cookies.txt -c cookies.txt http://localhost:5000/api/prices/twelvedata

# 獲取公開設定
curl http://localhost:5000/api/settings/public

# 健康檢查
curl http://localhost:5000/health
```

## 🔄 與靜態網站整合

這個 Flask API 設計用來與靜態網站配合使用：

1. 靜態網站通過 `js/price-reader.js` 調用 API
2. 使用 Unix 時間戳實現 15 分鐘緩存機制
3. 支持 CORS 跨域請求

## 📝 日誌

應用使用 Python 標準日誌模組，日誌級別設置為 INFO。所有重要操作都會記錄日誌。

## 🚀 部署建議

### 開發環境
```bash
# 初始化數據
python init_data.py

# 啟動開發服務器
python app.py
```

### 生產環境

#### Linux/macOS 部署
```bash
# 1. 安裝生產服務器
pip install gunicorn

# 2. 啟動服務（會自動初始化數據）
gunicorn -w 4 -b 0.0.0.0:5000 app:app
```

#### Windows 部署
```bash
# 1. 安裝 waitress（Windows 推薦）
pip install waitress

# 2. 啟動服務（會自動初始化數據）
waitress-serve --host=0.0.0.0 --port=5000 app:app
```

#### Docker 部署
```dockerfile
FROM python:3.9-slim
WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt
COPY . .
EXPOSE 5000
CMD ["python", "app.py"]
```

### VPS 部署注意事項

1. **數據持久化**: 確保 `data/` 目錄有寫入權限
2. **防火牆設置**: 開放 5000 端口（或自定義端口）
3. **反向代理**: 建議使用 Nginx 作為反向代理
4. **SSL 證書**: 生產環境建議使用 HTTPS
5. **環境變數**: 設置 `FLASK_ENV=production`
