import re
import os
import sys
import subprocess
from pathlib import Path

def parse_requirements_stdlib_only(file_path='requirements.txt'):
    """使用純標準庫解析 requirements.txt"""
    # 使用與 app.py 相同的目錄判斷邏輯
    executable_path = sys.executable

    if getattr(sys, 'frozen', False):
        executable_dir = os.path.dirname(executable_path)
    else:
        executable_dir = os.path.dirname(os.path.abspath(__file__))

    # 構建完整路徑
    full_path = os.path.join(executable_dir, file_path)
    packages = []

    if not os.path.exists(full_path):
        print(f"❌ 未找到 {full_path}")
        return packages

    # 包名映射表 - 處理特殊情況
    package_mapping = {
        'pillow': 'PIL',  # Pillow 包的實際模組名是 PIL
        'flask-cors': 'flask_cors',
        'flask-wtf': 'flask_wtf',
        'wtforms': 'wtforms',
        'werkzeug': 'werkzeug',
    }

    with open(full_path, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()

            # 跳過空行和註釋
            if not line or line.startswith('#'):
                continue

            # 使用正則表達式提取包名
            match = re.match(r'^([a-zA-Z0-9_-]+)', line)
            if match:
                package_name = match.group(1)
                package_name_lower = package_name.lower()

                # 使用映射表或默認轉換
                if package_name_lower in package_mapping:
                    module_name = package_mapping[package_name_lower]
                else:
                    module_name = package_name_lower.replace('-', '_')

                packages.append(module_name)

    return packages

def check_system_dependencies():
    """檢查系統依賴（純標準庫）"""
    import platform
    import shutil
    
    system = platform.system().lower()
    missing_deps = []
    
    if system == 'linux':
        # 檢查常見的編譯工具
        if not shutil.which('gcc'):
            missing_deps.append('build-essential')
        
        # 檢查 patchelf (Nuitka standalone 模式必需)
        if not shutil.which('patchelf'):
            missing_deps.append('patchelf')

        # 檢查 Python 開發頭文件（通過嘗試編譯簡單的 C 擴展來檢測）
        try:
            import sysconfig
            python_h = os.path.join(sysconfig.get_path('include'), 'Python.h')
            if not os.path.exists(python_h):
                missing_deps.append('python3-dev')
        except:
            missing_deps.append('python3-dev')
    
    return missing_deps

def build_nuitka_command():
    """構建 Nuitka 命令（純標準庫）"""
    # 獲取腳本所在目錄（與 app.py 相同的邏輯）
    executable_path = sys.executable

    if getattr(sys, 'frozen', False):
        executable_dir = os.path.dirname(executable_path)
    else:
        executable_dir = os.path.dirname(os.path.abspath(__file__))

    # 檢查 .env 文件（使用完整路徑）
    env_path = os.path.join(executable_dir, '.env')
    if not os.path.exists(env_path):
        print("❌ 未找到 .env 文件")
        print(f"路徑: {env_path}")
        print("請先執行: python app.py")
        return None

    # 檢查 app.py 文件
    app_path = os.path.join(executable_dir, 'app.py')
    if not os.path.exists(app_path):
        print("❌ 未找到 app.py 文件")
        print(f"路徑: {app_path}")
        return None

    # 檢查必要的目錄
    static_dir = os.path.join(executable_dir, 'static')
    templates_dir = os.path.join(executable_dir, 'templates')

    if not os.path.exists(static_dir):
        print("❌ 未找到 static 目錄")
        print(f"路徑: {static_dir}")
        return None

    if not os.path.exists(templates_dir):
        print("❌ 未找到 templates 目錄")
        print(f"路徑: {templates_dir}")
        return None

    # 解析依賴
    packages = parse_requirements_stdlib_only()
    print(f"📦 找到的包: {packages}")

    # 檢查包是否可以導入
    missing_packages = []
    for package in packages:
        if package != 'nuitka':
            try:
                __import__(package)
                print(f"✅ {package} - 可導入")
            except ImportError:
                print(f"❌ {package} - 無法導入")
                missing_packages.append(package)

    if missing_packages:
        print(f"⚠️  以下包無法導入，但仍會嘗試包含: {missing_packages}")
        print("如果編譯失敗，請檢查虛擬環境是否正確激活")

    # 檢查系統依賴
    missing_deps = check_system_dependencies()
    if missing_deps:
        print("❌ 缺少系統依賴:")
        for dep in missing_deps:
            print(f"   sudo apt install {dep}")
        return None

    # 構建命令（使用絕對路徑）
    cmd = [
        sys.executable, '-m', 'nuitka',
        '--standalone',
        # '--onefile',
        '--follow-imports',
        # f'--include-data-dir={static_dir}=static',
        f'--include-data-dir={templates_dir}=templates',
        # f'--include-data-files={env_path}=.env',
    ]

    # 添加包依賴 - 使用 --include-module 而不是 --include-package
    # 這樣可以更精確地控制包含的模組
    for package in packages:
        if package != 'nuitka':  # 排除 nuitka 本身
            # 對於一些特殊包，使用不同的策略
            if package == 'PIL':
                # Pillow/PIL 需要特殊處理
                cmd.extend([
                    '--include-package=PIL',
                    '--include-module=PIL._imaging',
                    '--include-module=PIL.Image',
                ])
            elif package in ['flask', 'flask_cors', 'flask_wtf', 'wtforms', 'werkzeug']:
                # Flask 相關包使用 --include-package
                cmd.append(f'--include-package={package}')
            else:
                # 其他包使用 --include-module
                cmd.append(f'--include-module={package}')

    cmd.append(app_path)
    return cmd, executable_dir

if __name__ == '__main__':
    result = build_nuitka_command()
    if result:
        cmd, executable_dir = result
        print("🚀 開始編譯...")
        print(f"工作目錄: {executable_dir}")
        print(f"執行命令: {' '.join(cmd)}")

        # 切換到正確的工作目錄執行 nuitka
        original_cwd = os.getcwd()
        try:
            os.chdir(executable_dir)
            subprocess.run(cmd)
        finally:
            os.chdir(original_cwd)