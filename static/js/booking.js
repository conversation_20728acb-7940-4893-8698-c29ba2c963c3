// 預約表單功能
document.addEventListener('DOMContentLoaded', function () {
    const bookingForm = document.getElementById('booking-form');
    const successMessage = document.getElementById('booking-success');

    // 表單驗證規則
    const validators = {
        name: {
            required: true,
            minLength: 2,
            maxLength: 20,
            pattern: /^[\u4e00-\u9fa5a-zA-Z\s]+$/,
            message: '姓名必須為2-20個字符，只能包含中文、英文和空格'
        },
        phone: {
            required: true,
            pattern: /^09\d{8}$/,
            message: '請輸入正確的台灣手機號碼格式 (09xxxxxxxx)'
        },
        email: {
            required: true,
            pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
            message: '請輸入正確的電子郵件格式'
        },
        'appointment-date': {
            required: true,
            message: '請選擇預約日期'
        },
        'appointment-time': {
            required: true,
            message: '請選擇預約時間'
        }
    };

    // 設定日期選擇器的最小日期為今天
    const dateInput = document.getElementById('appointment-date');
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);
    dateInput.min = tomorrow.toISOString().split('T')[0];

    // 可用時段管理
    const availableTimeSlots = {
        '09:00': true,
        '10:00': true,
        '11:00': true,
        '14:00': true,
        '15:00': true,
        '16:00': true
    };

    // 更新可用時段顯示
    function updateAvailableTimeSlots(selectedDate) {
        const timeSelect = document.getElementById('appointment-time');
        const options = timeSelect.querySelectorAll('option[value]');

        // 根據選擇的日期更新可用時段（這裡可以添加具體的邏輯）
        console.log('更新日期的可用時段:', selectedDate);

        options.forEach(option => {
            const timeValue = option.value;
            if (timeValue && availableTimeSlots[timeValue]) {
                option.disabled = false;
                option.textContent = timeValue;
            } else if (timeValue) {
                option.disabled = true;
                option.textContent = timeValue + ' (已預約)';
            }
        });
    }

    // 日期變更事件
    dateInput.addEventListener('change', function () {
        updateAvailableTimeSlots(this.value);
        validateField('appointmentDate', this.value);
    });

    // 驗證單個欄位
    function validateField(fieldName, value) {
        const validator = validators[fieldName];
        const errorElement = document.getElementById(fieldName + '-error');

        if (!validator) return true;

        let isValid = true;
        let errorMessage = '';

        // 必填驗證
        if (validator.required && (!value || value.trim() === '')) {
            isValid = false;
            errorMessage = validator.message || `${fieldName} 為必填欄位`;
        }
        // 長度驗證
        else if (value && validator.minLength && value.length < validator.minLength) {
            isValid = false;
            errorMessage = validator.message;
        }
        else if (value && validator.maxLength && value.length > validator.maxLength) {
            isValid = false;
            errorMessage = validator.message;
        }
        // 格式驗證
        else if (value && validator.pattern && !validator.pattern.test(value)) {
            isValid = false;
            errorMessage = validator.message;
        }
        // 日期驗證 - 不能選擇過去時間
        else if (fieldName === 'appointment-date' && value) {
            const selectedDate = new Date(value);
            const today = new Date();
            today.setHours(0, 0, 0, 0);

            if (selectedDate <= today) {
                isValid = false;
                errorMessage = '預約日期不能是今天或過去的日期';
            }
        }

        // 顯示錯誤訊息
        if (errorElement) {
            if (isValid) {
                errorElement.textContent = '';
                errorElement.style.display = 'none';
            } else {
                errorElement.textContent = errorMessage;
                errorElement.style.display = 'block';
            }
        }

        return isValid;
    }

    // 為所有表單欄位添加即時驗證
    Object.keys(validators).forEach(fieldName => {
        const field = document.getElementById(fieldName);
        if (field) {
            field.addEventListener('blur', function () {
                validateField(fieldName, this.value);
            });

            field.addEventListener('input', function () {
                // 清除錯誤訊息當用戶開始輸入
                const errorElement = document.getElementById(fieldName + '-error');
                if (errorElement && errorElement.style.display === 'block') {
                    setTimeout(() => validateField(fieldName, this.value), 300);
                }
            });
        }
    });

    // 電話號碼格式化
    const phoneInput = document.getElementById('phone');
    if (phoneInput) {
        phoneInput.addEventListener('input', function () {
            // 只允許數字
            this.value = this.value.replace(/\D/g, '');
            // 限制長度為10位
            if (this.value.length > 10) {
                this.value = this.value.slice(0, 10);
            }
        });
    }

    // 表單提交處理
    bookingForm.addEventListener('submit', function (e) {
        e.preventDefault();

        // 驗證所有欄位
        let isFormValid = true;
        const formData = new FormData(this);

        Object.keys(validators).forEach(fieldName => {
            const fieldValue = formData.get(fieldName);
            if (!validateField(fieldName, fieldValue)) {
                isFormValid = false;
            }
        });

        if (!isFormValid) {
            // 滾動到第一個錯誤欄位
            const firstError = document.querySelector('.error-message[style*="block"]');
            if (firstError) {
                firstError.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }
            return;
        }

        // 模擬表單提交
        submitBooking(formData);
    });

    // 提交預約資料
    function submitBooking(formData) {
        const submitBtn = bookingForm.querySelector('.submit-btn');

        // 使用全域載入管理器
        if (window.loadingManager) {
            window.loadingManager.setButtonLoading(submitBtn, true, '處理中...');
        } else {
            // 備用載入狀態
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 處理中...';
            submitBtn.disabled = true;
            submitBtn.dataset.originalText = originalText;
        }

        // 模擬API請求延遲
        setTimeout(() => {
            try {
                // 建立預約資料物件
                const bookingData = {
                    name: formData.get('name'),
                    phone: formData.get('phone'),
                    email: formData.get('email'),
                    appointmentDate: formData.get('appointment-date'),
                    appointmentTime: formData.get('appointment-time'),
                    serviceType: formData.get('serviceType'),
                    notes: formData.get('notes'),
                    timestamp: Date.now()
                };

                // 驗證資料完整性
                if (!bookingData.name || !bookingData.phone || !bookingData.email) {
                    throw new Error('必填欄位不完整');
                }

                // 儲存到 localStorage (模擬資料庫)
                try {
                    const existingBookings = JSON.parse(localStorage.getItem('bookings') || '[]');
                    existingBookings.push(bookingData);
                    localStorage.setItem('bookings', JSON.stringify(existingBookings));
                } catch (storageError) {
                    console.warn('無法儲存到本地存儲:', storageError);
                    // 繼續執行，不中斷流程
                }

                // 更新可用時段
                const selectedTime = bookingData.appointmentTime;
                if (availableTimeSlots[selectedTime]) {
                    availableTimeSlots[selectedTime] = false;
                }

                // 顯示成功訊息
                showSuccessMessage(bookingData);

                // 無障礙通知
                if (window.accessibilityEnhancer) {
                    window.accessibilityEnhancer.announceToScreenReader('預約提交成功');
                }

            } catch (error) {
                console.error('預約提交失敗:', error);
                showErrorMessage(error.message || '預約提交失敗，請稍後再試');
            } finally {
                // 重置按鈕狀態
                if (window.loadingManager) {
                    window.loadingManager.setButtonLoading(submitBtn, false);
                } else {
                    submitBtn.innerHTML = submitBtn.dataset.originalText || '提交預約';
                    submitBtn.disabled = false;
                }
            }

        }, 1500); // 模擬1.5秒的處理時間
    }

    // 顯示成功訊息
    function showSuccessMessage(bookingData) {
        bookingForm.style.display = 'none';
        successMessage.style.display = 'block';
        successMessage.classList.add('show');

        // 更新成功訊息內容
        const successContent = successMessage.querySelector('.success-content');
        if (successContent) {
            successContent.innerHTML = `
                <h3>預約成功！</h3>
                <p>感謝您的預約，我們已收到您的申請。</p>
                <div class="booking-details">
                    <p><strong>預約人：</strong>${bookingData.name}</p>
                    <p><strong>預約時間：</strong>${bookingData.appointmentDate} ${bookingData.appointmentTime}</p>
                    <p><strong>聯絡電話：</strong>${bookingData.phone}</p>
                </div>
                <p class="success-note">我們將在24小時內與您聯繫確認預約詳情。</p>
            `;
        }

        // 滾動到成功訊息
        successMessage.scrollIntoView({ behavior: 'smooth', block: 'center' });

        // 5秒後提供重置選項
        setTimeout(() => {
            const resetBtn = document.createElement('button');
            resetBtn.className = 'btn btn-secondary';
            resetBtn.innerHTML = '<i class="fas fa-plus"></i> 預約其他時段';
            resetBtn.onclick = resetBookingForm;

            if (!successMessage.querySelector('.btn')) {
                successMessage.appendChild(resetBtn);
            }
        }, 3000);
    }

    // 顯示錯誤訊息
    function showErrorMessage(message) {
        // 創建錯誤訊息元素
        let errorDiv = document.querySelector('.booking-error');
        if (!errorDiv) {
            errorDiv = document.createElement('div');
            errorDiv.className = 'booking-error data-error';
            bookingForm.parentNode.insertBefore(errorDiv, bookingForm);
        }

        errorDiv.innerHTML = `
            <div class="data-error-icon">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
            <div class="data-error-title">預約失敗</div>
            <div class="data-error-message">${message}</div>
            <button class="retry-btn" onclick="this.parentElement.style.display='none'">
                <i class="fas fa-times"></i> 關閉
            </button>
        `;

        errorDiv.style.display = 'block';
        errorDiv.scrollIntoView({ behavior: 'smooth', block: 'center' });

        // 3秒後自動隱藏
        setTimeout(() => {
            if (errorDiv.style.display !== 'none') {
                errorDiv.style.display = 'none';
            }
        }, 5000);
    }

    // 重置預約表單
    function resetBookingForm() {
        bookingForm.reset();
        bookingForm.style.display = 'block';
        successMessage.style.display = 'none';
        successMessage.classList.remove('show');

        // 清除所有錯誤訊息
        document.querySelectorAll('.error-message').forEach(error => {
            error.style.display = 'none';
            error.textContent = '';
        });

        // 清除錯誤狀態
        document.querySelectorAll('.form-group.error').forEach(group => {
            group.classList.remove('error');
        });

        // 隱藏錯誤訊息
        const errorDiv = document.querySelector('.booking-error');
        if (errorDiv) {
            errorDiv.style.display = 'none';
        }

        // 滾動回表單頂部
        bookingForm.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }

    // 初始化可用時段
    updateAvailableTimeSlots();
});