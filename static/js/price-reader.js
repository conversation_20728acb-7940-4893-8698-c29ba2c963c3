// 新的價格讀取器 - 使用Unix時間戳緩存機制
class PriceReader {
    constructor() {
        // Flask API 設定 - 使用相對路徑（同域名）
        this.apiBaseUrl = ''; // 相對路徑，自動使用當前域名

        this.defaultPrices = {
            gold: 2045.80,
            silver: 24.50,
            copper: 185.00,
            aluminum: 45.00
        };

        this.lastPrices = null;
        this.refreshInterval = 5 * 60 * 1000; // 預設5分鐘
        this.intervalId = null;
        this.init();
    }

    // 初始化
    async init() {
        console.log('🚀 初始化價格讀取器...');

        // 先載入系統設定以獲取刷新率
        await this.loadSystemSettings();

        // 載入價格
        this.loadPrices();

        // 設定定時器
        this.startRefreshTimer();
    }

    // 載入系統設定
    async loadSystemSettings() {
        try {
            const response = await fetch('/api/settings/public', {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                const settings = await response.json();
                const frontendRefreshRate = settings.twelvedata?.frontendRefreshRate || 300; // 預設5分鐘
                this.refreshInterval = frontendRefreshRate * 1000; // 轉換為毫秒
                console.log(`📊 前端刷新率設定為: ${frontendRefreshRate}秒`);
            }
        } catch (error) {
            console.warn('無法載入系統設定，使用預設刷新率:', error);
        }
    }

    // 開始刷新定時器
    startRefreshTimer() {
        // 清除現有定時器
        if (this.intervalId) {
            clearInterval(this.intervalId);
        }

        // 設定新的定時器
        this.intervalId = setInterval(() => {
            this.loadPrices();
        }, this.refreshInterval);

        console.log(`⏰ 價格刷新定時器已設定，間隔: ${this.refreshInterval / 1000}秒`);
    }

    // 計算5分鐘緩存時間戳
    getCacheTimestamp() {
        // 獲取當前 Unix 時間戳（秒）
        const nowSeconds = Math.floor(Date.now() / 1000);
        
        // 5分鐘 = 300秒
        const cacheInterval = 5 * 60; // 300秒
        
        // 計算當前時間所屬的15分鐘區間起始點
        const cacheTimestamp = Math.floor(nowSeconds / cacheInterval) * cacheInterval;
        
        console.log(`⏰ 當前時間: ${nowSeconds}, 緩存時間戳: ${cacheTimestamp}`);
        return cacheTimestamp;
    }

    // 從 Flask API 載入價格
    async loadPrices() {
        try {
            console.log('📊 正在載入最新價格...');

            // 獲取緩存時間戳
            const cacheTimestamp = this.getCacheTimestamp();

            // 使用階段四的完整價格 API
            let apiUrl = `${this.apiBaseUrl}/api/prices?t=${cacheTimestamp}`;

            console.log(`🔗 載入完整價格數據 URL: ${apiUrl}`);

            // 發送請求到完整價格 API
            let response = await fetch(apiUrl, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'Cache-Control': 'no-cache'
                },
                cache: 'no-cache'
            });

            if (response.ok) {
                const priceData = await response.json();
                console.log('✅ 從完整價格 API 載入成功:', priceData);

                this.lastPrices = priceData;
                this.updatePriceDisplay(priceData);
                this.showUpdateNotification(priceData);

            } else {
                console.warn(`⚠️ 價格 API 響應錯誤: ${response.status}`);
                throw new Error(`API 響應錯誤: ${response.status}`);
            }

        } catch (error) {
            console.error('❌ 載入價格失敗:', error);

            // 如果 API 失敗，嘗試 TwelveData API 作為備用
            try {
                console.log('🔄 嘗試 TwelveData API 作為備用...');
                const cacheTimestamp = this.getCacheTimestamp();
                const fallbackUrl = `${this.apiBaseUrl}/api/prices/twelvedata?t=${cacheTimestamp}`;

                const fallbackResponse = await fetch(fallbackUrl, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Cache-Control': 'no-cache'
                    },
                    cache: 'no-cache'
                });

                if (fallbackResponse.ok) {
                    const fallbackData = await fallbackResponse.json();
                    console.log('✅ TwelveData API 備用載入成功');
                    this.lastPrices = fallbackData;
                    this.updatePriceDisplay(fallbackData);
                    this.showUpdateNotification(fallbackData);
                    return;
                }
            } catch (fallbackError) {
                console.warn('⚠️ TwelveData API 備用也失敗:', fallbackError);
            }

            // 最後使用預設價格
            console.log('🔄 使用預設價格作為最終備用');
            const fallbackData = {
                ...this.defaultPrices,
                lastUpdate: new Date().toISOString(),
                updateTime: new Date().toLocaleString('zh-TW', { timeZone: 'Asia/Taipei' }),
                updatedBy: '預設價格',
                source: 'Fallback Data',
                note: 'API 不可用，使用預設價格'
            };

            this.updatePriceDisplay(fallbackData);
        }
    }

    // 更新頁面上的價格顯示（階段四：支援完整數據結構）
    updatePriceDisplay(priceData) {
        try {
            console.log('🔄 開始更新價格顯示...');

            // 檢查數據結構
            const hasNewStructure = priceData.metals && priceData.other_materials && priceData.currencies;

            if (hasNewStructure) {
                console.log('✅ 檢測到階段四數據結構');
                this.updateNewStructurePrices(priceData);
            } else {
                console.log('⚠️ 使用舊版數據結構');
                this.updateLegacyPrices(priceData);
            }

            // 更新時間顯示
            const updateTimeElement = document.getElementById('price-update-time');
            if (updateTimeElement) {
                const updateTime = priceData.updateTime || priceData.lastUpdate || '未知';
                updateTimeElement.textContent = `價格更新時間：${updateTime}`;
            }

            console.log('✅ 價格顯示更新完成');

        } catch (error) {
            console.error('❌ 更新價格顯示失敗:', error);
        }
    }

    // 更新階段四新數據結構的價格
    updateNewStructurePrices(priceData) {
        try {
            // 商品/美元表格
            if (priceData.metals && priceData.metals.usd_table) {
                const usdTable = priceData.metals.usd_table;

                // 金 Au
                this.updateElementPrice('gold-usd-buy', usdTable.gold?.buy_usd_per_oz);
                this.updateElementPrice('gold-usd-sell', usdTable.gold?.sell_usd_per_oz);
                this.updateElementPrice('gold-usd-high', usdTable.gold?.daily_high_usd);
                this.updateElementPrice('gold-usd-low', usdTable.gold?.daily_low_usd);
                this.updateElementChange('gold-usd-change', usdTable.gold?.usd_per_oz, usdTable.gold?.previous_close_usd);

                // 銀 Ag
                this.updateElementPrice('silver-usd-buy', usdTable.silver?.buy_usd_per_oz);
                this.updateElementPrice('silver-usd-sell', usdTable.silver?.sell_usd_per_oz);
                this.updateElementPrice('silver-usd-high', usdTable.silver?.daily_high_usd);
                this.updateElementPrice('silver-usd-low', usdTable.silver?.daily_low_usd);
                this.updateElementChange('silver-usd-change', usdTable.silver?.usd_per_oz, usdTable.silver?.previous_close_usd);

                // 鈀 Pd
                this.updateElementPrice('palladium-usd-buy', usdTable.palladium?.buy_usd_per_oz);
                this.updateElementPrice('palladium-usd-sell', usdTable.palladium?.sell_usd_per_oz);
                this.updateElementPrice('palladium-usd-high', usdTable.palladium?.daily_high_usd);
                this.updateElementPrice('palladium-usd-low', usdTable.palladium?.daily_low_usd);
                this.updateElementChange('palladium-usd-change', usdTable.palladium?.usd_per_oz, usdTable.palladium?.previous_close_usd);

                // 鉑 Pt
                this.updateElementPrice('platinum-usd-buy', usdTable.platinum?.buy_usd_per_oz);
                this.updateElementPrice('platinum-usd-sell', usdTable.platinum?.sell_usd_per_oz);
                this.updateElementPrice('platinum-usd-high', usdTable.platinum?.daily_high_usd);
                this.updateElementPrice('platinum-usd-low', usdTable.platinum?.daily_low_usd);
                this.updateElementChange('platinum-usd-change', usdTable.platinum?.usd_per_oz, usdTable.platinum?.previous_close_usd);
            }

            // 商品/台幣表格
            if (priceData.metals && priceData.metals.twd_table) {
                const twdTable = priceData.metals.twd_table;

                // 金 Au
                this.updateElementPrice('gold-twd-buy', twdTable.gold?.buy_twd_per_tael);
                this.updateElementPrice('gold-twd-sell', twdTable.gold?.sell_twd_per_tael);
                this.updateElementPrice('gold-twd-high', twdTable.gold?.daily_high_twd);
                this.updateElementPrice('gold-twd-low', twdTable.gold?.daily_low_twd);
                this.updateElementChange('gold-twd-change', twdTable.gold?.twd_per_tael, twdTable.gold?.previous_close_twd);

                // 銀 Ag
                this.updateElementPrice('silver-twd-buy', twdTable.silver?.buy_twd_per_tael);
                this.updateElementPrice('silver-twd-sell', twdTable.silver?.sell_twd_per_tael);
                this.updateElementPrice('silver-twd-high', twdTable.silver?.daily_high_twd);
                this.updateElementPrice('silver-twd-low', twdTable.silver?.daily_low_twd);
                this.updateElementChange('silver-twd-change', twdTable.silver?.twd_per_tael, twdTable.silver?.previous_close_twd);

                // 鈀 Pd
                this.updateElementPrice('palladium-twd-buy', twdTable.palladium?.buy_twd_per_tael);
                this.updateElementPrice('palladium-twd-sell', twdTable.palladium?.sell_twd_per_tael);
                this.updateElementPrice('palladium-twd-high', twdTable.palladium?.daily_high_twd);
                this.updateElementPrice('palladium-twd-low', twdTable.palladium?.daily_low_twd);
                this.updateElementChange('palladium-twd-change', twdTable.palladium?.twd_per_tael, twdTable.palladium?.previous_close_twd);

                // 鉑 Pt
                this.updateElementPrice('platinum-twd-buy', twdTable.platinum?.buy_twd_per_tael);
                this.updateElementPrice('platinum-twd-sell', twdTable.platinum?.sell_twd_per_tael);
                this.updateElementPrice('platinum-twd-high', twdTable.platinum?.daily_high_twd);
                this.updateElementPrice('platinum-twd-low', twdTable.platinum?.daily_low_twd);
                this.updateElementChange('platinum-twd-change', twdTable.platinum?.twd_per_tael, twdTable.platinum?.previous_close_twd);
            }

            // 實物交易表格（公克報價）
            if (priceData.metals && priceData.metals.physical_table) {
                const physicalTable = priceData.metals.physical_table;

                // 8種金屬
                const metals = ['gold', 'silver', 'palladium', 'platinum', 'rhodium', 'ruthenium', 'iridium', 'osmium'];
                metals.forEach(metal => {
                    // 原有邏輯：更新買入[g]和賣出[g]
                    this.updateElementPrice(`${metal}-gram-buy`, physicalTable[metal]?.buy_twd_per_gram);
                    this.updateElementPrice(`${metal}-gram-sell`, physicalTable[metal]?.sell_twd_per_gram);

                    // 新增邏輯：計算買入(錢) = 買入[g] × 3.75
                    const gramPrice = physicalTable[metal]?.buy_twd_per_gram;
                    if (gramPrice !== undefined && gramPrice !== null) {
                        const taelPrice = gramPrice * 3.75; // 1台錢 = 3.75公克
                        this.updateElementTaelPrice(`${metal}-tael-buy`, taelPrice);
                    } else {
                        this.updateElementTaelPrice(`${metal}-tael-buy`, null);
                    }
                });
            }

            // 其他材料表格（公斤報價）
            if (priceData.other_materials) {
                this.updateElementPrice('cobalt-kg-buy', priceData.other_materials.cobalt?.buy_twd_per_kg);
                this.updateElementPrice('cobalt-kg-sell', priceData.other_materials.cobalt?.sell_twd_per_kg);
                this.updateElementPrice('tin-kg-buy', priceData.other_materials.tin?.buy_twd_per_kg);
                this.updateElementPrice('tin-kg-sell', priceData.other_materials.tin?.sell_twd_per_kg);
            }

            // 貨幣項目表格（買入賣出價格）
            if (priceData.currencies) {
                // 美元
                const usdData = priceData.currencies.usd;
                if (usdData && usdData.buy_rate !== undefined && usdData.sell_rate !== undefined) {
                    this.updateElementPrice('usd-buy', usdData.buy_rate);
                    this.updateElementPrice('usd-sell', usdData.sell_rate);
                }

                // 人民幣
                const cnyData = priceData.currencies.cny;
                if (cnyData && cnyData.buy_rate !== undefined && cnyData.sell_rate !== undefined) {
                    this.updateElementPrice('cny-buy', cnyData.buy_rate);
                    this.updateElementPrice('cny-sell', cnyData.sell_rate);
                }

                // 歐元
                const eurData = priceData.currencies.eur;
                if (eurData && eurData.buy_rate !== undefined && eurData.sell_rate !== undefined) {
                    this.updateElementPrice('eur-buy', eurData.buy_rate);
                    this.updateElementPrice('eur-sell', eurData.sell_rate);
                }
            }

            console.log('✅ 階段四數據結構價格更新完成');

        } catch (error) {
            console.error('❌ 更新階段四價格失敗:', error);
        }
    }

    // 更新舊版數據結構的價格（向後兼容）
    updateLegacyPrices(priceData) {
        try {
            // 舊版格式的基本價格
            this.updateElementPrice('gold-price', priceData.gold);
            this.updateElementPrice('silver-price', priceData.silver);
            this.updateElementPrice('copper-price', priceData.copper);
            this.updateElementPrice('aluminum-price', priceData.aluminum);

            console.log('✅ 舊版數據結構價格更新完成');

        } catch (error) {
            console.error('❌ 更新舊版價格失敗:', error);
        }
    }

    // 輔助方法：更新單個元素的價格
    updateElementPrice(elementId, price) {
        const element = document.getElementById(elementId);
        if (element) {
            if (price !== undefined && price !== null) {
                element.textContent = this.formatPrice(price);
            } else {
                element.textContent = '載入中...';
            }
        }
    }

    // 輔助方法：更新台錢價格（買入(錢)欄位專用）
    updateElementTaelPrice(elementId, price) {
        const element = document.getElementById(elementId);
        if (element) {
            if (price !== undefined && price !== null) {
                element.textContent = this.formatTaelPrice(price);
            } else {
                element.textContent = '載入中...';
            }
        }
    }

    // 輔助方法：更新漲跌數據
    updateElementChange(elementId, currentPrice, previousClose) {
        const element = document.getElementById(elementId);
        if (element) {
            if (currentPrice !== undefined && currentPrice !== null &&
                previousClose !== undefined && previousClose !== null) {

                const change = currentPrice - previousClose;
                const changePercent = (change / previousClose) * 100;

                // 格式化漲跌顯示，取小數1位
                const changeText = `${change >= 0 ? '+' : ''}${this.formatPrice(change)} (${change >= 0 ? '+' : ''}${changePercent.toFixed(1)}%)`;
                element.textContent = changeText;

                // 設置顏色樣式
                element.className = element.className.replace(/\s*(price-up|price-down|price-neutral)\s*/g, '');
                if (change > 0) {
                    element.classList.add('price-up');
                    element.style.color = '#27ae60'; // 綠色上漲
                } else if (change < 0) {
                    element.classList.add('price-down');
                    element.style.color = '#e74c3c'; // 紅色下跌
                } else {
                    element.classList.add('price-neutral');
                    element.style.color = '#7f8c8d'; // 灰色持平
                }
            } else {
                element.textContent = '載入中...';
                element.style.color = ''; // 重置顏色
            }
        }
    }

    // 格式化價格顯示
    formatPrice(price) {
        if (typeof price !== 'number' || isNaN(price)) {
            return '載入中...';
        }

        // 根據價格大小決定小數位數
        let decimals = 3;
        if (price < 10) {
            decimals = 3; // 貨幣匯率顯示3位小數
        } else if (price > 1000) {
            decimals = 0; // 大金額不顯示小數
        }

        // 格式化為千分位顯示
        return new Intl.NumberFormat('zh-TW', {
            minimumFractionDigits: decimals,
            maximumFractionDigits: decimals
        }).format(price);
    }

    // 格式化台錢價格顯示（買入(錢)欄位專用）
    formatTaelPrice(price) {
        if (typeof price !== 'number' || isNaN(price)) {
            return '載入中...';
        }

        // 確保計算精度，避免浮點數誤差
        const roundedPrice = Math.round(price * 100) / 100;

        // 台錢價格通常較大，根據大小決定小數位數
        let decimals = 0;
        if (roundedPrice < 100) {
            decimals = 2; // 小於100顯示2位小數
        } else if (roundedPrice < 1000) {
            decimals = 1; // 100-1000顯示1位小數
        } else {
            decimals = 0; // 大於1000顯示整數
        }

        // 格式化為千分位顯示
        return new Intl.NumberFormat('zh-TW', {
            minimumFractionDigits: decimals,
            maximumFractionDigits: decimals
        }).format(roundedPrice);
    }

    // 顯示更新通知
    showUpdateNotification(priceData) {
        // 如果有更新通知區域，可以在這裡顯示
        if (priceData.source) {
            console.log(`📡 價格來源: ${priceData.source}`);
        }
        
        if (priceData.note) {
            console.log(`📝 備註: ${priceData.note}`);
        }
    }

    // 手動刷新價格（可以綁定到按鈕）
    async refreshPrices() {
        console.log('🔄 手動刷新價格...');
        await this.loadPrices();
    }

    // 獲取當前價格數據
    getCurrentPrices() {
        return this.lastPrices;
    }
}

// 全域變數，方便其他腳本使用
let priceReader;

// 頁面載入完成後初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('🌐 頁面載入完成，初始化價格讀取器...');
    priceReader = new PriceReader();
});

// 提供全域函數供其他腳本調用
window.refreshPrices = function() {
    if (priceReader) {
        priceReader.refreshPrices();
    }
};

window.getCurrentPrices = function() {
    if (priceReader) {
        return priceReader.getCurrentPrices();
    }
    return null;
};
