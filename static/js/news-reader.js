// 新聞讀取器 - 使用Unix時間戳緩存機制
class NewsReader {
    constructor() {
        // Flask API 設定 - 使用相對路徑（同域名）
        this.apiBaseUrl = ''; // 相對路徑，自動使用當前域名
        
        this.defaultNews = {
            news: [
                {
                    id: 1,
                    title: "黃金價格創新高",
                    content: "受全球經濟不確定性影響，黃金價格持續上漲，創下歷史新高。",
                    date: new Date().toISOString().split('T')[0],
                    time: new Date().toTimeString().split(' ')[0].substring(0, 5),
                    category: "市場動態"
                }
            ]
        };
        
        this.lastNews = null;
        this.init();
    }

    // 初始化
    init() {
        console.log('📰 初始化新聞讀取器...');
        this.loadNews();
        
        // 每5分鐘檢查一次新聞更新（可以根據需要調整）
        setInterval(() => {
            this.loadNews();
        }, 5 * 60 * 1000); // 5分鐘
    }

    // 計算15分鐘緩存時間戳
    getCacheTimestamp() {
        // 獲取當前 Unix 時間戳（秒）
        const nowSeconds = Math.floor(Date.now() / 1000);
        
        // 15分鐘 = 900秒
        const cacheInterval = 15 * 60; // 900秒
        
        // 計算當前時間所屬的15分鐘區間起始點
        const cacheTimestamp = Math.floor(nowSeconds / cacheInterval) * cacheInterval;
        
        console.log(`⏰ 新聞緩存 - 當前時間: ${nowSeconds}, 緩存時間戳: ${cacheTimestamp}`);
        return cacheTimestamp;
    }

    // 從 Flask API 載入新聞
    async loadNews() {
        try {
            console.log('📰 正在載入最新新聞...');
            
            // 獲取緩存時間戳
            const cacheTimestamp = this.getCacheTimestamp();
            
            // 構建 API URL，加上緩存時間戳
            const apiUrl = `${this.apiBaseUrl}/api/news?t=${cacheTimestamp}`;
            
            console.log(`🔗 新聞API URL: ${apiUrl}`);
            
            // 發送請求到 Flask API
            const response = await fetch(apiUrl, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'Cache-Control': 'no-cache'
                },
                // 不使用瀏覽器緩存，讓我們的時間戳緩存機制來控制
                cache: 'no-cache'
            });

            if (response.ok) {
                const newsData = await response.json();
                console.log('✅ 從 Flask API 載入新聞成功:', newsData);
                
                this.lastNews = newsData;
                this.updateNewsDisplay(newsData);
                this.showUpdateNotification(newsData);
                
            } else {
                console.warn(`⚠️ 新聞API 響應錯誤: ${response.status}`);
                throw new Error(`新聞API 響應錯誤: ${response.status}`);
            }
            
        } catch (error) {
            console.error('❌ 載入新聞失敗:', error);
            
            // 如果 API 失敗，使用預設新聞
            console.log('🔄 使用預設新聞作為備用');
            const fallbackData = {
                ...this.defaultNews,
                lastUpdate: new Date().toISOString(),
                updateTime: new Date().toLocaleString('zh-TW', { timeZone: 'Asia/Taipei' }),
                updatedBy: '預設新聞',
                source: 'Fallback Data',
                note: 'API 不可用，使用預設新聞'
            };
            
            this.updateNewsDisplay(fallbackData);
        }
    }

    // 更新頁面上的新聞顯示
    updateNewsDisplay(newsData) {
        try {
            const newsContainer = document.getElementById('news-container');
            const updateTimeElement = document.getElementById('news-update-time');

            if (newsContainer && newsData.news) {
                // 清空現有內容
                newsContainer.innerHTML = '';
                
                // 添加新聞項目
                newsData.news.forEach((newsItem, index) => {
                    const newsElement = this.createNewsElement(newsItem, index);
                    newsContainer.appendChild(newsElement);
                });
            }

            // 更新時間顯示
            if (updateTimeElement) {
                const updateTime = newsData.updateTime || newsData.lastUpdate || '未知';
                updateTimeElement.textContent = `新聞更新時間：${updateTime}`;
            }

            console.log('✅ 新聞顯示更新完成');
            
        } catch (error) {
            console.error('❌ 更新新聞顯示失敗:', error);
        }
    }

    // 創建新聞元素
    createNewsElement(newsItem, index) {
        const newsDiv = document.createElement('div');
        newsDiv.className = 'news-item mb-3 p-3 border rounded';
        
        newsDiv.innerHTML = `
            <div class="d-flex justify-content-between align-items-start mb-2">
                <h6 class="news-title mb-0 text-primary">${this.escapeHtml(newsItem.title)}</h6>
                <small class="text-muted">${newsItem.date} ${newsItem.time}</small>
            </div>
            <p class="news-content mb-2 text-muted">${this.escapeHtml(newsItem.content)}</p>
            <span class="badge bg-secondary">${this.escapeHtml(newsItem.category)}</span>
        `;
        
        return newsDiv;
    }

    // HTML 轉義函數
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // 顯示更新通知
    showUpdateNotification(newsData) {
        // 如果有更新通知區域，可以在這裡顯示
        if (newsData.source) {
            console.log(`📡 新聞來源: ${newsData.source}`);
        }
        
        if (newsData.note) {
            console.log(`📝 備註: ${newsData.note}`);
        }
    }

    // 手動刷新新聞（可以綁定到按鈕）
    async refreshNews() {
        console.log('🔄 手動刷新新聞...');
        await this.loadNews();
    }

    // 獲取當前新聞數據
    getCurrentNews() {
        return this.lastNews;
    }
}

// 全域變數，方便其他腳本使用
let newsReader;

// 頁面載入完成後初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('🌐 頁面載入完成，初始化新聞讀取器...');
    newsReader = new NewsReader();
});

// 提供全域函數供其他腳本調用
window.refreshNews = function() {
    if (newsReader) {
        newsReader.refreshNews();
    }
};

window.getCurrentNews = function() {
    if (newsReader) {
        return newsReader.getCurrentNews();
    }
    return null;
};
