// 市場數據圖表功能
class MarketDataChart {
    constructor() {
        this.chart = null;
        this.currentChartType = 'trend';
        this.marketData = this.generateMockData();
        this.init();
    }

    // 生成模擬市場數據
    generateMockData() {
        console.log('生成模擬市場數據...');
        const data = {
            trend: {
                labels: [],
                datasets: [{
                    label: '台股指數',
                    data: [],
                    borderColor: '#D4AF37',
                    backgroundColor: 'rgba(212, 175, 55, 0.1)',
                    borderWidth: 2,
                    fill: true,
                    tension: 0.4
                }]
            },
            comparison: {
                labels: ['黃金', '股票', '債券', '基金', '外匯'],
                datasets: [{
                    label: '年化報酬率 (%)',
                    data: [8.5, 12.3, 4.2, 9.8, 6.1],
                    backgroundColor: [
                        '#D4AF37',
                        '#B8860B',
                        '#DAA520',
                        '#FFD700',
                        '#F0E68C'
                    ],
                    borderColor: '#B8860B',
                    borderWidth: 1
                }]
            },
            performance: {
                labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
                datasets: [
                    {
                        label: '黃金投資',
                        data: [5.2, 6.8, 4.1, 7.3, 8.9, 6.5],
                        borderColor: '#D4AF37',
                        backgroundColor: 'rgba(212, 175, 55, 0.2)',
                        borderWidth: 2,
                        fill: false
                    },
                    {
                        label: '股票基金',
                        data: [3.8, 8.2, 6.7, 9.1, 5.4, 7.8],
                        borderColor: '#B8860B',
                        backgroundColor: 'rgba(184, 134, 11, 0.2)',
                        borderWidth: 2,
                        fill: false
                    }
                ]
            }
        };

        // 生成趨勢數據（過去30天）
        const today = new Date();
        let baseValue = 16800;

        for (let i = 29; i >= 0; i--) {
            const date = new Date(today);
            date.setDate(date.getDate() - i);

            // 模擬市場波動
            const change = (Math.random() - 0.5) * 200;
            baseValue += change;

            data.trend.labels.push(date.toLocaleDateString('zh-TW', {
                month: 'short',
                day: 'numeric'
            }));
            data.trend.datasets[0].data.push(Math.round(baseValue));
        }

        return data;
    }

    // 初始化圖表
    init() {
        this.setupEventListeners();
        this.createChart('trend');
        this.updateMarketStats();
    }

    // 設置事件監聽器
    setupEventListeners() {
        const chartTabs = document.querySelectorAll('.chart-tab');
        chartTabs.forEach(tab => {
            tab.addEventListener('click', (e) => {
                const chartType = e.target.dataset.chart;
                this.switchChart(chartType);

                // 更新活動標籤
                chartTabs.forEach(t => t.classList.remove('active'));
                e.target.classList.add('active');
            });
        });
    }

    // 創建圖表
    createChart(type) {
        console.log('創建圖表，類型:', type);
        const ctx = document.getElementById('market-chart');
        if (!ctx) {
            console.error('找不到圖表 canvas 元素');
            throw new Error('圖表容器不存在');
        }

        try {
            // 顯示載入狀態
            this.showChartLoading(ctx);

            // 銷毀現有圖表
            if (this.chart) {
                this.chart.destroy();
                this.chart = null;
            }

            // 模擬載入延遲
            setTimeout(() => {
                try {
                    const config = this.getChartConfig(type);
                    this.chart = new Chart(ctx, config);
                    this.currentChartType = type;

                    // 隱藏載入狀態
                    this.hideChartLoading();

                    // 無障礙通知
                    if (window.accessibilityEnhancer) {
                        window.accessibilityEnhancer.announceToScreenReader(`${this.getChartTitle(type)} 已載入完成`);
                    }

                    console.log('圖表創建成功:', type);
                } catch (error) {
                    console.error('圖表創建失敗:', error);
                    this.hideChartLoading();
                    throw error;
                }
            }, 500);

        } catch (error) {
            console.error('圖表初始化錯誤:', error);
            this.showChartError(error.message);
            throw error;
        }
    }

    // 顯示圖表載入狀態
    showChartLoading(canvas) {
        const wrapper = canvas.parentElement;
        if (wrapper) {
            // 創建載入覆蓋層
            let loadingOverlay = wrapper.querySelector('.chart-loading-overlay');
            if (!loadingOverlay) {
                loadingOverlay = document.createElement('div');
                loadingOverlay.className = 'chart-loading-overlay';
                loadingOverlay.style.cssText = `
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background-color: rgba(255, 255, 255, 0.9);
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    z-index: 10;
                `;
                loadingOverlay.innerHTML = `
                    <div class="loading-content">
                        <div class="loading-spinner-large" role="status" aria-label="載入圖表中"></div>
                        <div class="loading-text">載入圖表中...</div>
                    </div>
                `;
                wrapper.style.position = 'relative';
                wrapper.appendChild(loadingOverlay);
            }
            loadingOverlay.style.display = 'flex';
        }
    }

    // 隱藏圖表載入狀態
    hideChartLoading() {
        const loadingOverlay = document.querySelector('.chart-loading-overlay');
        if (loadingOverlay) {
            loadingOverlay.style.display = 'none';
        }
    }

    // 顯示圖表錯誤
    showChartError(message) {
        const canvas = document.getElementById('market-chart');
        const wrapper = canvas?.parentElement;
        if (wrapper) {
            wrapper.innerHTML = `
                <div class="data-error">
                    <div class="data-error-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="data-error-title">圖表載入失敗</div>
                    <div class="data-error-message">${message}</div>
                    <button class="retry-btn" onclick="window.marketChart?.retryChart()">
                        <i class="fas fa-redo"></i> 重新載入
                    </button>
                </div>
            `;
        }
    }

    // 重試載入圖表
    retryChart() {
        const wrapper = document.querySelector('.data-error')?.parentElement;
        if (wrapper) {
            wrapper.innerHTML = '<canvas id="market-chart"></canvas>';
            try {
                this.createChart(this.currentChartType || 'trend');
            } catch (error) {
                console.error('重試載入失敗:', error);
                this.showChartError('重新載入失敗，請稍後再試');
            }
        }
    }

    // 獲取圖表配置
    getChartConfig(type) {
        const data = this.marketData[type];
        let chartType = 'line';

        if (type === 'comparison') {
            chartType = 'bar';
        }

        return {
            type: chartType,
            data: data,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: this.getChartTitle(type),
                        font: {
                            size: 16,
                            weight: 'bold'
                        },
                        color: '#333'
                    },
                    legend: {
                        display: true,
                        position: 'top',
                        labels: {
                            usePointStyle: true,
                            padding: 20,
                            color: '#333'
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        titleColor: '#fff',
                        bodyColor: '#fff',
                        borderColor: '#D4AF37',
                        borderWidth: 1,
                        cornerRadius: 6,
                        displayColors: true,
                        callbacks: {
                            label: function (context) {
                                let label = context.dataset.label || '';
                                if (label) {
                                    label += ': ';
                                }

                                if (type === 'comparison') {
                                    label += context.parsed.y + '%';
                                } else if (type === 'trend') {
                                    label += context.parsed.y.toLocaleString();
                                } else {
                                    label += context.parsed.y + '%';
                                }

                                return label;
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        grid: {
                            color: 'rgba(0, 0, 0, 0.1)'
                        },
                        ticks: {
                            color: '#666'
                        }
                    },
                    y: {
                        grid: {
                            color: 'rgba(0, 0, 0, 0.1)'
                        },
                        ticks: {
                            color: '#666',
                            callback: function (value) {
                                if (type === 'trend') {
                                    return value.toLocaleString();
                                } else {
                                    return value + '%';
                                }
                            }
                        }
                    }
                },
                interaction: {
                    intersect: false,
                    mode: 'index'
                },
                hover: {
                    animationDuration: 300
                },
                animation: {
                    duration: 1000,
                    easing: 'easeInOutQuart'
                }
            }
        };
    }

    // 獲取圖表標題
    getChartTitle(type) {
        const titles = {
            trend: '台股指數趨勢 (過去30天)',
            comparison: '投資產品年化報酬率比較',
            performance: '投資組合績效表現'
        };
        return titles[type] || '市場數據';
    }

    // 切換圖表類型
    switchChart(type) {
        if (type !== this.currentChartType) {
            this.createChart(type);
        }
    }

    // 更新市場統計數據
    updateMarketStats() {
        const stats = {
            taiwan: {
                value: 16847,
                change: 125,
                changePercent: 0.74
            },
            gold: {
                value: 2018,
                change: -12,
                changePercent: -0.59
            },
            usd: {
                value: 31.25,
                change: 0.08,
                changePercent: 0.26
            }
        };

        // 更新台股指數
        this.updateStatCard('taiwan-index', stats.taiwan.value.toLocaleString(),
            'taiwan-change', stats.taiwan.change, stats.taiwan.changePercent);

        // 更新黃金價格
        this.updateStatCard('gold-price', `$${stats.gold.value}`,
            'gold-change', stats.gold.change, stats.gold.changePercent);

        // 更新美元匯率
        this.updateStatCard('usd-rate', stats.usd.value.toString(),
            'usd-change', stats.usd.change, stats.usd.changePercent);
    }

    // 更新統計卡片
    updateStatCard(valueId, value, changeId, change, changePercent) {
        const valueElement = document.getElementById(valueId);
        const changeElement = document.getElementById(changeId);

        if (valueElement) {
            valueElement.textContent = value;
        }

        if (changeElement) {
            const isPositive = change >= 0;
            const changeText = `${isPositive ? '+' : ''}${change} (${isPositive ? '+' : ''}${changePercent.toFixed(2)}%)`;

            changeElement.textContent = changeText;
            changeElement.className = `stat-change ${isPositive ? 'positive' : 'negative'}`;
        }
    }

    // 刷新數據
    refreshData() {
        try {
            console.log('刷新市場數據...');

            // 模擬網路請求可能失敗
            if (Math.random() < 0.1) { // 10% 機率失敗
                throw new Error('網路連線不穩定');
            }

            this.marketData = this.generateMockData();

            // 如果圖表存在，更新數據
            if (this.chart) {
                const newData = this.marketData[this.currentChartType];
                this.chart.data = newData;
                this.chart.update('active');
            } else {
                // 重新創建圖表
                this.createChart(this.currentChartType);
            }

            this.updateMarketStats();

            // 無障礙通知
            if (window.accessibilityEnhancer) {
                window.accessibilityEnhancer.announceToScreenReader('市場數據已更新');
            }

            console.log('數據刷新成功');
        } catch (error) {
            console.error('數據刷新失敗:', error);

            // 顯示錯誤通知但不中斷現有圖表
            if (window.errorHandler) {
                window.errorHandler.showErrorNotification('市場數據更新失敗');
            }

            // 如果圖表不存在，顯示錯誤狀態
            if (!this.chart) {
                this.showChartError('無法載入最新市場數據');
            }
        }
    }
}

// 當頁面載入完成時初始化圖表
document.addEventListener('DOMContentLoaded', function () {
    try {
        if (document.getElementById('market-chart')) {
            // 檢查 Chart.js 是否已載入
            if (typeof Chart === 'undefined') {
                console.error('Chart.js 未載入，請檢查 CDN 連結');
                return;
            }

            window.marketChart = new MarketDataChart();
            console.log('市場數據圖表初始化成功');

            // 每30秒更新一次數據（模擬即時更新）
            setInterval(() => {
                if (window.marketChart) {
                    window.marketChart.refreshData();
                }
            }, 30000);
        }
    } catch (error) {
        console.error('圖表初始化失敗:', error);
        handleChartError(error);
    }

    // 圖表錯誤處理
    function handleChartError(error) {
        const chartWrapper = document.querySelector('.chart-wrapper') || document.getElementById('market-chart')?.parentElement;
        if (chartWrapper) {
            chartWrapper.innerHTML = `
                <div class="data-error">
                    <div class="data-error-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="data-error-title">圖表載入失敗</div>
                    <div class="data-error-message">
                        ${error.message || '無法載入市場數據圖表，請檢查網路連線或稍後再試'}
                    </div>
                    <button class="retry-btn" onclick="retryChartLoad()">
                        <i class="fas fa-redo"></i> 重新載入
                    </button>
                </div>
            `;

            // 無障礙支援
            chartWrapper.setAttribute('role', 'alert');
            chartWrapper.setAttribute('aria-live', 'polite');
        }

        // 通知錯誤處理器
        if (window.errorHandler) {
            window.errorHandler.showErrorNotification('市場數據載入失敗');
        }
    }

    // 重新載入圖表
    window.retryChartLoad = function () {
        const chartWrapper = document.querySelector('.chart-wrapper') || document.getElementById('market-chart')?.parentElement;
        if (chartWrapper) {
            // 顯示載入狀態
            chartWrapper.innerHTML = `
                <div class="loading-content">
                    <div class="loading-spinner-large" role="status" aria-label="載入圖表中"></div>
                    <div class="loading-text">載入圖表中...</div>
                </div>
            `;

            // 延遲重新初始化
            setTimeout(() => {
                try {
                    // 重新創建 canvas 元素
                    chartWrapper.innerHTML = '<canvas id="market-chart"></canvas>';

                    // 重新初始化圖表
                    if (typeof Chart !== 'undefined') {
                        window.marketChart = new MarketDataChart();
                        console.log('圖表重新載入成功');
                    } else {
                        throw new Error('Chart.js 庫未載入');
                    }
                } catch (retryError) {
                    console.error('圖表重新載入失敗:', retryError);
                    handleChartError(retryError);
                }
            }, 1000);
        }
    };
});