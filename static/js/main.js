// 金融服務網站 - 主要JavaScript功能

// ===== 跨瀏覽器兼容性檢測 =====
(function () {
    'use strict';

    // 檢測瀏覽器功能支援
    window.browserSupport = {
        es6: (function () {
            try {
                eval('const test = () => {}; let x = `template`; class Test {}');
                return true;
            } catch (e) {
                return false;
            }
        })(),

        fetch: typeof fetch !== 'undefined',
        intersectionObserver: 'IntersectionObserver' in window,
        cssVariables: window.CSS && CSS.supports('color', 'var(--test)'),
        flexbox: CSS.supports('display', 'flex'),
        grid: CSS.supports('display', 'grid'),

        localStorage: (function () {
            try {
                localStorage.setItem('test', 'test');
                localStorage.removeItem('test');
                return true;
            } catch (e) {
                return false;
            }
        })()
    };

    // 為不支援的瀏覽器添加類名
    if (!window.browserSupport.cssVariables) {
        document.documentElement.classList.add('no-css-variables');
    }

    if (!window.browserSupport.flexbox) {
        document.documentElement.classList.add('no-flexbox');
    }

    if (!window.browserSupport.grid) {
        document.documentElement.classList.add('no-grid');
    }
})();

// ===== DOM 載入完成後執行 =====
document.addEventListener('DOMContentLoaded', function () {
    try {
        // 初始化導航功能
        initNavigation();

        // 初始化滾動效果
        initScrollEffects();

        // 初始化首頁功能（僅在首頁）
        if (isHomePage()) {
            initHomepageFeatures();
        }

        console.log('金融服務網站已載入完成');

    } catch (error) {
        console.error('初始化過程中發生錯誤:', error);
    }
});

// ===== 導航功能 =====
function initNavigation() {
    const navToggle = document.querySelector('.nav-toggle');
    const navMenu = document.querySelector('.nav-menu');
    const navLinks = document.querySelectorAll('.nav-link');

    // 漢堡選單點擊事件
    if (navToggle && navMenu) {
        navToggle.addEventListener('click', function () {
            toggleMobileMenu();
        });
    }

    // 導航連結點擊事件（移動端）
    navLinks.forEach(link => {
        link.addEventListener('click', function () {
            if (window.innerWidth <= 767) {
                closeMobileMenu();
            }
        });
    });

    // 視窗大小改變事件
    window.addEventListener('resize', function () {
        if (window.innerWidth > 767) {
            closeMobileMenu();
        }
    });

    // ESC 鍵關閉選單
    document.addEventListener('keydown', function (e) {
        if (e.key === 'Escape') {
            closeMobileMenu();
        }
    });

    // 點擊外部區域關閉選單
    document.addEventListener('click', function (e) {
        if (!navToggle.contains(e.target) && !navMenu.contains(e.target)) {
            closeMobileMenu();
        }
    });
}

// 切換移動端選單
function toggleMobileMenu() {
    const navToggle = document.querySelector('.nav-toggle');
    const navMenu = document.querySelector('.nav-menu');

    if (!navToggle || !navMenu) return;

    const isActive = navMenu.classList.contains('active');

    navToggle.classList.toggle('active');
    navMenu.classList.toggle('active');

    // 更新 ARIA 屬性
    navToggle.setAttribute('aria-expanded', !isActive);

    // 防止背景滾動
    if (!isActive) {
        document.body.style.overflow = 'hidden';
    } else {
        document.body.style.overflow = '';
    }
}

// 關閉移動端選單
function closeMobileMenu() {
    const navToggle = document.querySelector('.nav-toggle');
    const navMenu = document.querySelector('.nav-menu');

    if (navToggle) {
        navToggle.classList.remove('active');
        navToggle.setAttribute('aria-expanded', 'false');
    }
    if (navMenu) {
        navMenu.classList.remove('active');
    }

    // 恢復背景滾動
    document.body.style.overflow = '';
}

// ===== 滾動效果 =====
function initScrollEffects() {
    const navbar = document.querySelector('.navbar');

    if (!navbar) return;

    // 導航欄滾動效果
    window.addEventListener('scroll', function () {
        if (window.scrollY > 50) {
            navbar.classList.add('scrolled');
        } else {
            navbar.classList.remove('scrolled');
        }
    }, { passive: true });

    // 平滑滾動到錨點
    document.querySelectorAll('a[href^="#"]').forEach(function (anchor) {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                const offsetTop = target.offsetTop - 80;
                window.scrollTo({
                    top: offsetTop,
                    behavior: 'smooth'
                });
            }
        });
    });
}

// ===== 首頁專用功能 =====
function isHomePage() {
    return window.location.pathname === '/' ||
        window.location.pathname === '/index.html' ||
        window.location.pathname.endsWith('/index.html');
}

function initHomepageFeatures() {
    initScrollAnimations();
    initCounterAnimation();
    initHeroScrollIndicator();
}

// ===== 滾動動畫 =====
function initScrollAnimations() {
    if (!window.browserSupport.intersectionObserver) return;

    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function (entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate');

                // 如果是統計數字，觸發計數動畫
                if (entry.target.classList.contains('about-stats')) {
                    animateCounters();
                }
            }
        });
    }, observerOptions);

    // 觀察需要動畫的元素
    const animateElements = document.querySelectorAll('.feature-card, .about-content, .about-visual, .testimonial-card, .about-stats');
    animateElements.forEach(el => {
        el.classList.add('scroll-animate');
        observer.observe(el);
    });
}

// ===== 數字計數動畫 =====
function initCounterAnimation() {
    const statNumbers = document.querySelectorAll('.stat-number');
    statNumbers.forEach((stat, index) => {
        if (!stat.hasAttribute('data-target')) {
            const targets = [15, 5000, 98];
            stat.setAttribute('data-target', targets[index] || 0);
        }
    });
}

function animateCounters() {
    const counters = document.querySelectorAll('.stat-number');

    counters.forEach(counter => {
        const target = parseInt(counter.getAttribute('data-target'));
        const duration = 2000;
        const increment = target / (duration / 16);
        let current = 0;

        counter.classList.add('counting');

        const timer = setInterval(() => {
            current += increment;
            if (current >= target) {
                current = target;
                clearInterval(timer);
                counter.classList.remove('counting');
            }

            // 格式化數字顯示
            if (target >= 1000) {
                counter.textContent = Math.floor(current).toLocaleString();
            } else {
                counter.textContent = Math.floor(current);
            }

            // 為百分比添加 % 符號
            const statLabel = counter.parentElement.querySelector('.stat-label');
            if (statLabel && statLabel.textContent.includes('滿意度')) {
                counter.textContent += '%';
            }
        }, 16);
    });
}

// ===== Hero 滾動指示器 =====
function initHeroScrollIndicator() {
    const scrollIndicator = document.querySelector('.hero-scroll-indicator');
    const featuresSection = document.querySelector('.features');

    if (scrollIndicator && featuresSection) {
        scrollIndicator.addEventListener('click', function () {
            const offsetTop = featuresSection.offsetTop - 80;
            window.scrollTo({
                top: offsetTop,
                behavior: 'smooth'
            });
        });

        // 滾動時隱藏指示器
        window.addEventListener('scroll', function () {
            const scrolled = window.scrollY > window.innerHeight * 0.3;
            scrollIndicator.style.opacity = scrolled ? '0' : '1';
            scrollIndicator.style.pointerEvents = scrolled ? 'none' : 'auto';
        });
    }
}

// ===== 產品頁面功能 =====
function isProductsPage() {
    return window.location.pathname.includes('products.html');
}

function initProductsPage() {
    initProductFilters();
    initProductCards();
}

function initProductFilters() {
    const filterButtons = document.querySelectorAll('.filter-btn');
    const productCards = document.querySelectorAll('.product-card');

    filterButtons.forEach(button => {
        button.addEventListener('click', function () {
            const category = this.getAttribute('data-category');

            // 更新按鈕狀態
            filterButtons.forEach(btn => {
                btn.classList.remove('active');
                btn.setAttribute('aria-pressed', 'false');
            });
            this.classList.add('active');
            this.setAttribute('aria-pressed', 'true');

            // 篩選產品
            productCards.forEach(card => {
                if (category === 'all' || card.getAttribute('data-category') === category) {
                    card.style.display = 'block';
                } else {
                    card.style.display = 'none';
                }
            });
        });
    });
}

function initProductCards() {
    const detailButtons = document.querySelectorAll('.btn-details');

    detailButtons.forEach(button => {
        button.addEventListener('click', function () {
            const productCard = this.closest('.product-card');
            const details = productCard.querySelector('.product-details');

            if (details) {
                details.classList.toggle('expanded');
                this.textContent = details.classList.contains('expanded') ? '收起詳情' : '查看詳情';
            }
        });
    });
}

// ===== 聯絡頁面功能 =====
function isContactPage() {
    return window.location.pathname.includes('contact.html');
}

function initContactForm() {
    const contactForm = document.getElementById('contact-form');
    if (!contactForm) return;

    contactForm.addEventListener('submit', function (e) {
        e.preventDefault();

        // 簡單的表單驗證
        const name = document.getElementById('contact-name').value.trim();
        const email = document.getElementById('contact-email').value.trim();
        const subject = document.getElementById('contact-subject').value;
        const message = document.getElementById('contact-message').value.trim();

        if (!name || !email || !subject || !message) {
            alert('請填寫所有必填欄位');
            return;
        }

        // 顯示成功訊息
        const successMessage = document.getElementById('contact-success');
        if (successMessage) {
            successMessage.style.display = 'block';
            contactForm.style.display = 'none';
        }
    });
}

// ===== 工具函數 =====
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

function throttle(func, limit) {
    let inThrottle;
    return function () {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

// ===== 錯誤處理 =====
window.addEventListener('error', function (e) {
    console.error('JavaScript 錯誤:', e.error);
});

window.addEventListener('unhandledrejection', function (e) {
    console.error('未處理的 Promise 拒絕:', e.reason);
});

console.log('main.js 載入完成');