// 聯絡我們表單處理器
class ContactForm {
    constructor() {
        this.apiBaseUrl = ''; // 相對路徑，自動使用當前域名
        this.captchaData = null;
        this.init();
    }

    // 初始化
    init() {
        console.log('📧 初始化聯絡我們表單...');
        this.initToastr();
        this.loadCaptcha();
        this.bindEvents();
    }

    // 初始化 toastr
    initToastr() {
        if (typeof toastr !== 'undefined') {
            toastr.options = {
                "closeButton": true,
                "debug": false,
                "newestOnTop": true,
                "progressBar": true,
                "positionClass": "toast-top-right",
                "preventDuplicates": false,
                "onclick": null,
                "showDuration": "300",
                "hideDuration": "1000",
                "timeOut": "5000",
                "extendedTimeOut": "1000",
                "showEasing": "swing",
                "hideEasing": "linear",
                "showMethod": "fadeIn",
                "hideMethod": "fadeOut"
            };
        }
    }

    // 綁定事件
    bindEvents() {
        const form = document.getElementById('contactForm');
        if (form) {
            form.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleSubmit();
            });
        }

        // 刷新驗證碼按鈕
        const refreshBtn = document.getElementById('refreshCaptcha');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => {
                this.loadCaptcha();
            });
        }
    }

    // 載入驗證碼
    async loadCaptcha() {
        try {
            console.log('🔐 載入驗證碼...');
            
            const response = await fetch(`${this.apiBaseUrl}/api/captcha`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                this.captchaData = await response.json();
                this.updateCaptchaDisplay();
            } else {
                console.error('載入驗證碼失敗');
                this.hideCaptcha();
            }
            
        } catch (error) {
            console.error('載入驗證碼失敗:', error);
            this.hideCaptcha();
        }
    }

    // 更新驗證碼顯示
    updateCaptchaDisplay() {
        const captchaContainer = document.getElementById('captchaContainer');
        const captchaQuestion = document.getElementById('captchaQuestion');
        const captchaInput = document.getElementById('captchaAnswer');

        if (!this.captchaData || !this.captchaData.enabled) {
            this.hideCaptcha();
            return;
        }

        if (captchaContainer) {
            captchaContainer.style.display = 'block';
        }

        if (captchaQuestion) {
            // 如果有圖片數據，顯示圖片驗證碼
            if (this.captchaData.image) {
                captchaQuestion.innerHTML = `
                    <span>請計算圖片中的數學題：</span><br>
                    <img src="${this.captchaData.image}" alt="驗證碼" style="border: 1px solid #ccc; border-radius: 4px; margin-top: 5px;">
                `;
            } else {
                // 備用文字版本
                captchaQuestion.textContent = `請計算：${this.captchaData.question} = ?`;
            }
        }

        if (captchaInput) {
            captchaInput.value = '';
            captchaInput.required = true;
        }

        console.log('✅ 驗證碼顯示更新完成');
    }

    // 隱藏驗證碼
    hideCaptcha() {
        const captchaContainer = document.getElementById('captchaContainer');
        const captchaInput = document.getElementById('captchaAnswer');

        if (captchaContainer) {
            captchaContainer.style.display = 'none';
        }

        if (captchaInput) {
            captchaInput.required = false;
        }
    }

    // 處理表單提交
    async handleSubmit() {
        try {
            this.showLoading(true);
            this.clearMessages();

            // 收集表單數據
            const formData = this.collectFormData();
            
            // 驗證表單數據
            if (!this.validateForm(formData)) {
                this.showLoading(false);
                return;
            }

            console.log('📤 提交聯絡我們...');

            const response = await fetch(`${this.apiBaseUrl}/api/contact`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(formData)
            });

            const result = await response.json();

            if (result.success) {
                this.showMessage('訊息提交成功！我們會盡快回覆您。', 'success');
                this.resetForm();
                this.loadCaptcha(); // 重新載入驗證碼
            } else {
                this.showMessage(result.error || '提交失敗，請重試', 'danger');
                if (result.error && result.error.includes('驗證碼')) {
                    this.loadCaptcha(); // 驗證碼錯誤時重新載入
                }
            }

        } catch (error) {
            console.error('提交聯絡我們失敗:', error);
            this.showMessage('網絡錯誤，請重試', 'danger');
        } finally {
            this.showLoading(false);
        }
    }

    // 收集表單數據
    collectFormData() {
        const formData = {
            name: document.getElementById('name')?.value || '',
            email: document.getElementById('email')?.value || '',
            phone: document.getElementById('phone')?.value || '',
            subject: document.getElementById('subject')?.value || '',
            message: document.getElementById('message')?.value || ''
        };

        // 添加驗證碼答案（如果需要）
        if (this.captchaData && this.captchaData.enabled) {
            const captchaAnswer = document.getElementById('captchaAnswer')?.value || '';
            if (captchaAnswer) {
                formData.captcha_answer = parseInt(captchaAnswer);
            }
        }

        return formData;
    }

    // 驗證表單
    validateForm(formData) {
        const requiredFields = ['name', 'email', 'subject', 'message'];
        
        for (const field of requiredFields) {
            if (!formData[field]) {
                this.showMessage(`請填寫${this.getFieldName(field)}`, 'danger');
                return false;
            }
        }

        // 驗證 email 格式
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(formData.email)) {
            this.showMessage('請輸入有效的電子郵件地址', 'danger');
            return false;
        }

        // 驗證驗證碼
        if (this.captchaData && this.captchaData.enabled) {
            if (!formData.captcha_answer && formData.captcha_answer !== 0) {
                this.showMessage('請輸入驗證碼答案', 'danger');
                return false;
            }
        }

        return true;
    }

    // 獲取字段中文名稱
    getFieldName(field) {
        const fieldNames = {
            name: '姓名',
            email: '電子郵件',
            phone: '電話',
            subject: '主題',
            message: '訊息內容'
        };
        return fieldNames[field] || field;
    }

    // 重置表單
    resetForm() {
        const form = document.getElementById('contactForm');
        if (form) {
            form.reset();
        }
    }

    // 顯示消息 - 使用 toastr
    showMessage(message, type = 'info') {
        if (typeof toastr !== 'undefined') {
            switch(type) {
                case 'success':
                    toastr.success(message);
                    break;
                case 'danger':
                case 'error':
                    toastr.error(message);
                    break;
                case 'warning':
                    toastr.warning(message);
                    break;
                default:
                    toastr.info(message);
                    break;
            }
        } else {
            // 備用方案：使用原有的 alert 方式
            const messageContainer = document.getElementById('messageContainer');
            if (!messageContainer) return;

            const alertClass = type === 'success' ? 'alert-success' :
                              type === 'danger' ? 'alert-danger' : 'alert-info';

            const alertHtml = `
                <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                    <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-triangle'} me-2"></i>
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;

            messageContainer.innerHTML = alertHtml;

            // 自動隱藏成功消息
            if (type === 'success') {
                setTimeout(() => {
                    const alert = messageContainer.querySelector('.alert');
                    if (alert) {
                        alert.remove();
                    }
                }, 5000);
            }
        }
    }

    // 清除消息
    clearMessages() {
        const messageContainer = document.getElementById('messageContainer');
        if (messageContainer) {
            messageContainer.innerHTML = '';
        }
    }

    // 顯示/隱藏載入狀態
    showLoading(show) {
        const submitBtn = document.querySelector('#contactForm button[type="submit"]');
        if (submitBtn) {
            if (show) {
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>提交中...';
            } else {
                submitBtn.disabled = false;
                submitBtn.innerHTML = '<i class="fas fa-paper-plane me-2"></i>發送訊息';
            }
        }
    }
}

// 全域變數
let contactForm;

// 頁面載入完成後初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('🌐 頁面載入完成，初始化聯絡我們表單...');
    contactForm = new ContactForm();
});

// 提供全域函數
window.refreshContactCaptcha = function() {
    if (contactForm) {
        contactForm.loadCaptcha();
    }
};
