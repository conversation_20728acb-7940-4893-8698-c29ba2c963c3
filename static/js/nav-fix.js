// 導航修復腳本 - 確保移動端漢堡選單在所有設備上正常工作
(function() {
    'use strict';
    
    function initMobileNavigation() {
        const navToggle = document.querySelector('.nav-toggle');
        const navMenu = document.querySelector('.nav-menu');
        const navContainer = document.querySelector('.nav-container');

        if (!navToggle || !navMenu) {
            console.warn('導航元素未找到，跳過導航初始化');
            return;
        }

        console.log('初始化移動端導航功能...');

        // 確保按鈕樣式正確
        navToggle.style.pointerEvents = 'auto';
        navToggle.style.userSelect = 'none';
        navToggle.style.webkitUserSelect = 'none';
        navToggle.style.webkitTouchCallout = 'none';
        
        // 清除任何可能干擾的CSS
        navToggle.style.transform = 'none';
        navToggle.style.transition = 'background-color 0.2s ease';

        // 移除舊的事件監聽器
        const newNavToggle = navToggle.cloneNode(true);
        navToggle.parentNode.replaceChild(newNavToggle, navToggle);

        // 添加多種事件類型以確保兼容性
        const events = ['click', 'touchstart'];
        
        events.forEach(eventType => {
            newNavToggle.addEventListener(eventType, function(e) {
                // 防止事件重複觸發
                if (eventType === 'touchstart' && e.touches.length > 1) return;
                
                e.preventDefault();
                e.stopPropagation();
                
                console.log(`導航按鈕 ${eventType} 事件觸發`);
                
                const isCurrentlyActive = navMenu.classList.contains('active');
                
                // 切換選單狀態
                if (isCurrentlyActive) {
                    closeMenu();
                } else {
                    openMenu();
                }
                
            }, { passive: false });
        });

        // 開啟選單
        function openMenu() {
            newNavToggle.classList.add('active');
            navMenu.classList.add('active');
            newNavToggle.setAttribute('aria-expanded', 'true');
            document.body.style.overflow = 'hidden';
            
            // 為iOS Safari添加額外的樣式
            if (/iPad|iPhone|iPod/.test(navigator.userAgent)) {
                document.body.style.position = 'fixed';
                document.body.style.width = '100%';
            }
            
            console.log('選單已開啟');
        }

        // 關閉選單
        function closeMenu() {
            newNavToggle.classList.remove('active');
            navMenu.classList.remove('active');
            newNavToggle.setAttribute('aria-expanded', 'false');
            document.body.style.overflow = '';
            
            // 移除iOS Safari的額外樣式
            if (/iPad|iPhone|iPod/.test(navigator.userAgent)) {
                document.body.style.position = '';
                document.body.style.width = '';
            }
            
            console.log('選單已關閉');
        }

        // 點擊選單連結關閉選單
        navMenu.addEventListener('click', function(e) {
            if (e.target.classList.contains('nav-link')) {
                setTimeout(() => {
                    closeMenu();
                }, 150);
            }
        });

        // 點擊選單外部關閉選單
        document.addEventListener('click', function(e) {
            if (!navContainer.contains(e.target) && navMenu.classList.contains('active')) {
                closeMenu();
            }
        });

        // 觸控滑動關閉選單（可選功能）
        let startX = null;
        navMenu.addEventListener('touchstart', function(e) {
            startX = e.touches[0].clientX;
        }, { passive: true });

        navMenu.addEventListener('touchmove', function(e) {
            if (startX !== null) {
                const currentX = e.touches[0].clientX;
                const diff = startX - currentX;
                
                // 如果向左滑動超過100px，關閉選單
                if (diff > 100) {
                    closeMenu();
                    startX = null;
                }
            }
        }, { passive: true });

        navMenu.addEventListener('touchend', function() {
            startX = null;
        }, { passive: true });

        // ESC鍵關閉選單
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && navMenu.classList.contains('active')) {
                closeMenu();
            }
        });

        // 視窗大小變化時關閉選單
        window.addEventListener('resize', function() {
            if (window.innerWidth > 767 && navMenu.classList.contains('active')) {
                closeMenu();
            }
        });

        // 頁面方向變化時關閉選單
        window.addEventListener('orientationchange', function() {
            setTimeout(() => {
                if (window.innerWidth > 767 && navMenu.classList.contains('active')) {
                    closeMenu();
                }
            }, 100);
        });

        console.log('✅ 導航修復腳本載入成功');
        
        // 測試函數 - 可在開發者工具中調用
        window.testNavigation = function() {
            console.log('測試導航功能...');
            console.log('當前螢幕寬度:', window.innerWidth);
            console.log('導航按鈕元素:', newNavToggle);
            console.log('導航選單元素:', navMenu);
            console.log('選單是否活躍:', navMenu.classList.contains('active'));
        };
    }

    // 確保在DOM載入後執行
    function initWhenReady() {
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', initMobileNavigation);
        } else {
            // DOM已經載入完成
            initMobileNavigation();
        }
    }

    initWhenReady();
})();