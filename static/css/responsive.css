/* 金融服務網站 - 響應式樣式文件 */

/* ===== 響應式斷點定義 ===== */
/* 
  移動端：< 768px
  平板：768px - 1024px  
  桌面：> 1024px
*/

/* ===== 平板樣式 (768px 以上) ===== */
@media (min-width: 768px) {
    .container {
        padding: 0 var(--spacing-lg);
    }

    .container-fluid {
        padding: 0 var(--spacing-lg);
    }

    /* 平板 Grid 系統 */
    .col-md-1 {
        flex: 0 0 8.333333%;
        max-width: 8.333333%;
    }

    .col-md-2 {
        flex: 0 0 16.666667%;
        max-width: 16.666667%;
    }

    .col-md-3 {
        flex: 0 0 25%;
        max-width: 25%;
    }

    .col-md-4 {
        flex: 0 0 33.333333%;
        max-width: 33.333333%;
    }

    .col-md-5 {
        flex: 0 0 41.666667%;
        max-width: 41.666667%;
    }

    .col-md-6 {
        flex: 0 0 50%;
        max-width: 50%;
    }

    /* 聯絡頁面平板樣式 */
    .contact-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-lg);
    }

    .services-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-lg);
    }

    .form-row {
        grid-template-columns: 1fr 1fr;
        gap: var(--spacing-md);
    }

    .col-md-7 {
        flex: 0 0 58.333333%;
        max-width: 58.333333%;
    }

    .col-md-8 {
        flex: 0 0 66.666667%;
        max-width: 66.666667%;
    }

    .col-md-9 {
        flex: 0 0 75%;
        max-width: 75%;
    }

    .col-md-10 {
        flex: 0 0 83.333333%;
        max-width: 83.333333%;
    }

    .col-md-11 {
        flex: 0 0 91.666667%;
        max-width: 91.666667%;
    }

    .col-md-12 {
        flex: 0 0 100%;
        max-width: 100%;
    }

    /* 平板字體大小調整 */
    h1 {
        font-size: 2.5rem;
    }

    h2 {
        font-size: 2rem;
    }

    h3 {
        font-size: 1.75rem;
    }

    /* 平板按鈕樣式 */
    .btn {
        padding: var(--spacing-md) var(--spacing-xl);
        font-size: var(--font-size-lg);
    }

    /* 平板卡片間距 */
    .card-body {
        padding: var(--spacing-xl);
    }

    .card-header,
    .card-footer {
        padding: var(--spacing-xl);
    }
}

/* ===== 桌面樣式 (1024px 以上) ===== */
@media (min-width: 1024px) {
    .container {
        padding: 0 var(--spacing-xl);
    }

    .container-fluid {
        padding: 0 var(--spacing-xl);
    }

    /* 桌面 Grid 系統 */
    .col-lg-1 {
        flex: 0 0 8.333333%;
        max-width: 8.333333%;
    }

    .col-lg-2 {
        flex: 0 0 16.666667%;
        max-width: 16.666667%;
    }

    .col-lg-3 {
        flex: 0 0 25%;
        max-width: 25%;
    }

    .col-lg-4 {
        flex: 0 0 33.333333%;
        max-width: 33.333333%;
    }

    .col-lg-5 {
        flex: 0 0 41.666667%;
        max-width: 41.666667%;
    }

    .col-lg-6 {
        flex: 0 0 50%;
        max-width: 50%;
    }

    .col-lg-7 {
        flex: 0 0 58.333333%;
        max-width: 58.333333%;
    }

    .col-lg-8 {
        flex: 0 0 66.666667%;
        max-width: 66.666667%;
    }

    .col-lg-9 {
        flex: 0 0 75%;
        max-width: 75%;
    }

    .col-lg-10 {
        flex: 0 0 83.333333%;
        max-width: 83.333333%;
    }

    .col-lg-11 {
        flex: 0 0 91.666667%;
        max-width: 91.666667%;
    }

    .col-lg-12 {
        flex: 0 0 100%;
        max-width: 100%;
    }

    /* 桌面字體大小 */
    h1 {
        font-size: var(--font-size-4xl);
    }

    h2 {
        font-size: var(--font-size-3xl);
    }

    h3 {
        font-size: var(--font-size-2xl);
    }

    /* 桌面hover效果增強 */
    .card:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 24px var(--shadow-medium);
    }

    .btn:hover {
        transform: translateY(-3px);
        box-shadow: 0 6px 16px var(--shadow-medium);
    }
}

/* ===== 大螢幕樣式 (1200px 以上) ===== */
@media (min-width: 1200px) {
    .container {
        max-width: 1200px;
    }

    /* 大螢幕 Grid 系統 */
    .col-xl-1 {
        flex: 0 0 8.333333%;
        max-width: 8.333333%;
    }

    .col-xl-2 {
        flex: 0 0 16.666667%;
        max-width: 16.666667%;
    }

    .col-xl-3 {
        flex: 0 0 25%;
        max-width: 25%;
    }

    .col-xl-4 {
        flex: 0 0 33.333333%;
        max-width: 33.333333%;
    }

    .col-xl-5 {
        flex: 0 0 41.666667%;
        max-width: 41.666667%;
    }

    .col-xl-6 {
        flex: 0 0 50%;
        max-width: 50%;
    }

    .col-xl-7 {
        flex: 0 0 58.333333%;
        max-width: 58.333333%;
    }

    .col-xl-8 {
        flex: 0 0 66.666667%;
        max-width: 66.666667%;
    }

    .col-xl-9 {
        flex: 0 0 75%;
        max-width: 75%;
    }

    .col-xl-10 {
        flex: 0 0 83.333333%;
        max-width: 83.333333%;
    }

    .col-xl-11 {
        flex: 0 0 91.666667%;
        max-width: 91.666667%;
    }

    .col-xl-12 {
        flex: 0 0 100%;
        max-width: 100%;
    }
}

/* ===== 移動端專用樣式 (767px 以下) ===== */
@media (max-width: 767px) {

    /* 移動端容器調整 */
    .container,
    .container-fluid {
        padding: 0 var(--spacing-sm);
    }

    /* 移動端導航樣式 */
    .nav-container {
        padding-left: var(--spacing-sm);
        padding-right: var(--spacing-sm);
    }

    .navbar {
        height: 70px;
    }

    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background-color: var(--background-white);
        width: 100%;
        text-align: center;
        transition: left var(--transition-normal);
        box-shadow: 0 4px 16px var(--shadow-medium);
        border-top: 1px solid var(--border-light);
        padding: var(--spacing-lg) 0;
        z-index: 1000;
        height: calc(100vh - 70px);
        overflow-y: auto;
        will-change: transform;
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-item {
        margin: 0;
        margin-bottom: var(--spacing-md);
    }

    .nav-item:last-child {
        margin-bottom: 0;
    }

    .nav-link {
        display: block;
        padding: var(--spacing-md) var(--spacing-lg);
        font-size: var(--font-size-lg);
        border-radius: 0;
        margin: 0 var(--spacing-lg);
        border-radius: var(--border-radius-md);
    }

    .nav-link:hover {
        transform: none;
        background-color: var(--secondary-beige);
    }

    .nav-toggle {
        display: flex !important;
        position: relative;
        z-index: 1001;
        touch-action: manipulation;
        min-width: 44px;
        min-height: 44px;
        justify-content: center;
        align-items: center;
    }

    /* 移動端導航覆蓋層 */
    .nav-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 998;
        opacity: 0;
        visibility: hidden;
        transition: all var(--transition-normal);
    }

    .nav-overlay.active {
        opacity: 1;
        visibility: visible;
    }

    /* 移動端body padding調整 */
    body {
        padding-top: 70px;
    }

    /* 移動端字體大小調整 */
    h1 {
        font-size: var(--font-size-3xl);
        line-height: 1.1;
    }

    h2 {
        font-size: var(--font-size-2xl);
        line-height: 1.2;
    }

    h3 {
        font-size: var(--font-size-xl);
    }

    /* 移動端按鈕調整 */
    .btn {
        width: 100%;
        padding: var(--spacing-md);
        font-size: var(--font-size-base);
    }

    /* 移動端卡片調整 */
    .card {
        margin-bottom: var(--spacing-lg);
    }

    .card-body {
        padding: var(--spacing-md);
    }

    .card-header,
    .card-footer {
        padding: var(--spacing-md);
    }

    /* 移動端表單調整 */
    .form-control {
        font-size: 16px;
        /* 防止iOS縮放 */
    }

    /* 移動端間距調整 */
    .row {
        margin: 0 calc(-1 * var(--spacing-xs));
    }

    .col {
        padding: 0 var(--spacing-xs);
    }

    /* 移動端所有欄位預設為全寬 */
    .col,
    .col-1,
    .col-2,
    .col-3,
    .col-4,
    .col-5,
    .col-6,
    .col-7,
    .col-8,
    .col-9,
    .col-10,
    .col-11,
    .col-12 {
        flex: 0 0 100%;
        max-width: 100%;
        margin-bottom: var(--spacing-md);
    }

    /* 聯絡頁面移動端樣式 */
    .contact-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    .contact-card {
        padding: var(--spacing-lg);
    }

    .contact-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 0;
    }

    .message-form {
        padding: var(--spacing-lg);
    }

    .services-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    .service-item {
        padding: var(--spacing-lg);
    }

    .service-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }
}

/* ===== 響應式工具類別 ===== */
/* 隱藏/顯示工具 */
@media (max-width: 767px) {
    .d-md-none {
        display: none !important;
    }

    .d-lg-none {
        display: none !important;
    }
}

@media (min-width: 768px) {
    .d-sm-none {
        display: none !important;
    }
}

@media (min-width: 1024px) {
    .d-md-none {
        display: none !important;
    }
}

/* 文字對齊響應式 */
@media (max-width: 767px) {
    .text-sm-center {
        text-align: center;
    }

    .text-sm-left {
        text-align: left;
    }

    .text-sm-right {
        text-align: right;
    }
}

@media (min-width: 768px) {
    .text-md-center {
        text-align: center;
    }

    .text-md-left {
        text-align: left;
    }

    .text-md-right {
        text-align: right;
    }
}

@media (min-width: 1024px) {
    .text-lg-center {
        text-align: center;
    }

    .text-lg-left {
        text-align: left;
    }

    .text-lg-right {
        text-align: right;
    }
}

/* ===== 響應式間距工具 ===== */
@media (max-width: 767px) {
    .mb-sm-0 {
        margin-bottom: 0 !important;
    }

    .mb-sm-1 {
        margin-bottom: var(--spacing-xs) !important;
    }

    .mb-sm-2 {
        margin-bottom: var(--spacing-sm) !important;
    }

    .mb-sm-3 {
        margin-bottom: var(--spacing-md) !important;
    }

    .mb-sm-4 {
        margin-bottom: var(--spacing-lg) !important;
    }

    .mb-sm-5 {
        margin-bottom: var(--spacing-xl) !important;
    }

    .mt-sm-0 {
        margin-top: 0 !important;
    }

    .mt-sm-1 {
        margin-top: var(--spacing-xs) !important;
    }

    .mt-sm-2 {
        margin-top: var(--spacing-sm) !important;
    }

    .mt-sm-3 {
        margin-top: var(--spacing-md) !important;
    }

    .mt-sm-4 {
        margin-top: var(--spacing-lg) !important;
    }

    .mt-sm-5 {
        margin-top: var(--spacing-xl) !important;
    }
}

/* ===== 列印樣式 ===== */
@media print {
    * {
        background: transparent !important;
        color: black !important;
        box-shadow: none !important;
        text-shadow: none !important;
    }

    .btn {
        border: 1px solid black;
        background: transparent !important;
    }

    .card {
        border: 1px solid black;
        box-shadow: none;
    }
}

/* ====
= 首頁響應式樣式 ===== */

/* 平板樣式 (768px 以上) */
@media (min-width: 768px) {
    .hero-title {
        font-size: 4rem;
    }

    .hero-buttons {
        justify-content: center;
    }

    .about-stats {
        justify-content: flex-start;
    }

    .about-buttons {
        justify-content: flex-start;
    }
}

/* 桌面樣式 (1024px 以上) */
@media (min-width: 1024px) {
    .hero-title {
        font-size: 4.5rem;
    }

    .feature-card:hover {
        transform: translateY(-12px);
    }

    .about-visual {
        padding-left: var(--spacing-2xl);
    }
}

/* 移動端專用樣式 (767px 以下) */
@media (max-width: 767px) {
    .hero {
        min-height: 80vh;
        text-align: center;
    }

    .hero-title {
        font-size: 2.5rem;
        line-height: 1.2;
    }

    .hero-subtitle {
        font-size: var(--font-size-lg);
    }

    .hero-description {
        font-size: var(--font-size-base);
        margin-bottom: var(--spacing-xl);
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
        gap: var(--spacing-md);
    }

    .btn-lg {
        width: 100%;
        max-width: 280px;
        padding: var(--spacing-md) var(--spacing-lg);
        font-size: var(--font-size-base);
    }

    .hero-scroll-indicator {
        bottom: var(--spacing-lg);
    }

    /* Section 標題調整 */
    .section-title {
        font-size: var(--font-size-2xl);
    }

    .section-title::after {
        left: 50%;
        transform: translateX(-50%);
    }

    .section-subtitle {
        font-size: var(--font-size-base);
    }

    /* 服務特色移動端調整 */
    .features {
        padding: var(--spacing-2xl) 0;
    }

    .feature-card {
        padding: var(--spacing-xl);
        margin-bottom: var(--spacing-lg);
    }

    .feature-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
        margin-bottom: var(--spacing-md);
    }

    .feature-content h4 {
        font-size: var(--font-size-lg);
    }

    .feature-content p {
        font-size: var(--font-size-sm);
    }

    /* 關於我們移動端調整 */
    .about {
        padding: var(--spacing-2xl) 0;
    }

    .about-content {
        padding-right: 0;
        margin-bottom: var(--spacing-xl);
    }

    .about-text {
        font-size: var(--font-size-base);
    }

    .about-stats {
        flex-direction: column;
        gap: var(--spacing-lg);
        text-align: center;
        margin: var(--spacing-xl) 0;
    }

    .stat-number {
        font-size: var(--font-size-2xl);
    }

    .about-buttons {
        flex-direction: column;
        align-items: center;
        gap: var(--spacing-md);
    }

    .about-buttons .btn {
        width: 100%;
        max-width: 280px;
    }

    .about-visual {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
        padding-left: 0;
    }

    .visual-card {
        padding: var(--spacing-lg);
    }

    .visual-card:nth-child(3) {
        grid-column: 1;
        max-width: none;
    }

    .visual-card i {
        font-size: 2rem;
    }

    .visual-card h5 {
        font-size: var(--font-size-base);
    }

    .visual-card p {
        font-size: var(--font-size-xs);
    }

    /* 客戶見證移動端調整 */
    .testimonials {
        padding: var(--spacing-2xl) 0;
    }

    .testimonial-card {
        padding: var(--spacing-lg);
        margin-bottom: var(--spacing-lg);
    }

    .testimonial-card::before {
        font-size: 3rem;
        top: -5px;
        left: var(--spacing-md);
    }

    .testimonial-content {
        margin-bottom: var(--spacing-md);
        padding-top: var(--spacing-sm);
    }

    .testimonial-content p {
        font-size: var(--font-size-base);
    }

    .author-info h5 {
        font-size: var(--font-size-sm);
    }

    .author-info span {
        font-size: var(--font-size-xs);
    }
}

/* 超小螢幕調整 (480px 以下) */
@media (max-width: 480px) {
    .hero-title {
        font-size: 2rem;
    }

    .hero-subtitle {
        font-size: var(--font-size-base);
    }

    .hero-description {
        font-size: var(--font-size-sm);
    }

    .section-title {
        font-size: var(--font-size-xl);
    }

    .feature-card {
        padding: var(--spacing-md);
    }

    .feature-icon {
        width: 50px;
        height: 50px;
        font-size: 1.25rem;
    }

    .about-stats {
        gap: var(--spacing-md);
    }

    .stat-number {
        font-size: var(--font-size-xl);
    }

    .visual-card {
        padding: var(--spacing-md);
    }

    .testimonial-card {
        padding: var(--spacing-md);
    }
}

/* 橫向模式調整 */
@media (max-height: 600px) and (orientation: landscape) {
    .hero {
        min-height: 100vh;
        padding: var(--spacing-xl) 0;
    }

    .hero-content {
        padding: var(--spacing-xl) 0;
    }

    .hero-title {
        font-size: 2.5rem;
        margin-bottom: var(--spacing-md);
    }

    .hero-subtitle {
        margin-bottom: var(--spacing-sm);
    }

    .hero-description {
        margin-bottom: var(--spacing-lg);
    }

    .hero-scroll-indicator {
        display: none;
    }
}

/* ==
=== 產品頁面響應式樣式 ===== */

/* 平板尺寸 (768px - 1024px) */
@media (max-width: 1024px) {
    .products-grid {
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: var(--spacing-lg);
        padding: 0 var(--spacing-md);
    }

    .page-header {
        padding: var(--spacing-2xl) 0 var(--spacing-xl);
    }

    .page-header h1 {
        font-size: var(--font-size-3xl);
    }

    .filter-buttons {
        gap: var(--spacing-sm);
        padding: 0 var(--spacing-md);
    }

    .filter-btn {
        padding: var(--spacing-sm) var(--spacing-md);
        font-size: var(--font-size-sm);
    }
}

/* 移動端尺寸 (< 768px) */
@media (max-width: 767px) {
    .page-header {
        margin-top: 70px;
        /* 調整移動端導航高度 */
        padding: var(--spacing-xl) var(--spacing-md);
    }

    .page-header h1 {
        font-size: var(--font-size-2xl);
    }

    .page-header p {
        font-size: var(--font-size-base);
    }

    .product-filters {
        padding: var(--spacing-lg) var(--spacing-md);
    }

    .filter-buttons {
        justify-content: flex-start;
        overflow-x: auto;
        padding-bottom: var(--spacing-sm);
        gap: var(--spacing-sm);
        flex-wrap: nowrap;
    }

    .filter-btn {
        flex-shrink: 0;
        padding: var(--spacing-sm) var(--spacing-md);
        font-size: var(--font-size-sm);
        border-radius: 20px;
    }

    .products {
        padding: var(--spacing-xl) 0;
    }

    .products-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
        padding: 0 var(--spacing-md);
    }

    .product-card {
        border-radius: 12px;
    }

    .product-card-header {
        padding: var(--spacing-md);
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-sm);
    }

    .product-icon {
        width: 50px;
        height: 50px;
        font-size: var(--font-size-xl);
    }

    .product-category-badge {
        align-self: flex-end;
        margin-top: -40px;
    }

    .product-card-body {
        padding: var(--spacing-md);
    }

    .product-name {
        font-size: var(--font-size-lg);
    }

    .product-description {
        font-size: var(--font-size-sm);
        margin-bottom: var(--spacing-md);
    }

    .product-info {
        gap: var(--spacing-xs);
    }

    .product-risk,
    .product-return {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-xs);
    }

    .risk-label,
    .return-label {
        min-width: auto;
        font-size: var(--font-size-xs);
    }

    .product-card-footer {
        padding: var(--spacing-md);
        flex-direction: column;
        gap: var(--spacing-sm);
    }

    .btn-details,
    .btn-contact {
        width: 100%;
        justify-content: center;
        padding: var(--spacing-sm);
    }

    .details-content {
        padding: var(--spacing-md);
    }

    .details-actions {
        flex-direction: column;
    }

    .details-actions .btn {
        min-width: auto;
        width: 100%;
    }

    /* 產品詳情展開時的最大高度調整 */
    .product-details.expanded {
        max-height: 600px;
    }
}

/* 小型移動端 (< 480px) */
@media (max-width: 479px) {
    .page-header {
        padding: var(--spacing-lg) var(--spacing-sm);
    }

    .page-header h1 {
        font-size: var(--font-size-xl);
    }

    .products-grid {
        padding: 0 var(--spacing-sm);
        gap: var(--spacing-md);
    }

    .product-card-header,
    .product-card-body,
    .product-card-footer,
    .details-content {
        padding: var(--spacing-sm);
    }

    .filter-buttons {
        padding: 0 var(--spacing-sm);
    }

    .filter-btn {
        padding: var(--spacing-xs) var(--spacing-sm);
        font-size: var(--font-size-xs);
    }

    .product-name {
        font-size: var(--font-size-base);
    }

    .product-icon {
        width: 45px;
        height: 45px;
        font-size: var(--font-size-lg);
    }
}

/* 橫向模式優化 */
@media (max-width: 767px) and (orientation: landscape) {
    .page-header {
        padding: var(--spacing-md) var(--spacing-md);
    }

    .page-header h1 {
        font-size: var(--font-size-xl);
        margin-bottom: var(--spacing-xs);
    }

    .page-header p {
        font-size: var(--font-size-sm);
    }

    .products {
        padding: var(--spacing-lg) 0;
    }

    .products-grid {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    }
}

/* 高解析度螢幕優化 */
@media (min-width: 1200px) {
    .products-grid {
        grid-template-columns: repeat(3, 1fr);
        max-width: 1400px;
    }

    .product-card {
        border-radius: 20px;
    }

    .product-icon {
        width: 70px;
        height: 70px;
        font-size: var(--font-size-3xl);
    }
}

/* 觸控設備優化 */
@media (hover: none) and (pointer: coarse) {

    .filter-btn:hover,
    .btn-details:hover,
    .btn-contact:hover,
    .product-card:hover {
        transform: none;
    }

    .filter-btn:active {
        transform: scale(0.95);
    }

    .btn-details:active,
    .btn-contact:active {
        transform: scale(0.98);
    }

    /* 增加觸控目標大小 */
    .filter-btn {
        min-height: 44px;
        padding: var(--spacing-sm) var(--spacing-lg);
    }

    .btn-details,
    .btn-contact {
        min-height: 44px;
    }
}