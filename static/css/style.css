/* 金融服務網站 - 主要樣式文件 */

/* ===== CSS 變數定義 ===== */
:root {
    /* 金色主題配色方案 */
    --primary-gold: #D4AF37;
    --secondary-beige: #F5F5DC;
    --text-dark: #333333;
    --text-light: #666666;
    --background-white: #FFFFFF;
    --background-light: #FAFAFA;
    --accent-gold: #B8860B;
    --border-light: #E0E0E0;
    --shadow-light: rgba(0, 0, 0, 0.1);
    --shadow-medium: rgba(0, 0, 0, 0.15);

    /* 跨瀏覽器兼容性增強 */
    --webkit-primary-gold: #D4AF37;
    /* Safari/Chrome fallback */
    --moz-primary-gold: #D4AF37;
    /* Firefox fallback */
    --ms-primary-gold: #D4AF37;
    /* IE/Edge fallback */

    /* 字體大小 */
    --font-size-xs: 0.75rem;
    /* 12px */
    --font-size-sm: 0.875rem;
    /* 14px */
    --font-size-base: 1rem;
    /* 16px */
    --font-size-lg: 1.125rem;
    /* 18px */
    --font-size-xl: 1.25rem;
    /* 20px */
    --font-size-2xl: 1.5rem;
    /* 24px */
    --font-size-3xl: 1.875rem;
    /* 30px */
    --font-size-4xl: 2.25rem;
    /* 36px */

    /* 間距 */
    --spacing-xs: 0.25rem;
    /* 4px */
    --spacing-sm: 0.5rem;
    /* 8px */
    --spacing-md: 1rem;
    /* 16px */
    --spacing-lg: 1.5rem;
    /* 24px */
    --spacing-xl: 2rem;
    /* 32px */
    --spacing-2xl: 3rem;
    /* 48px */

    /* 動畫時間 */
    --transition-fast: 0.15s;
    --transition-normal: 0.3s;
    --transition-slow: 0.5s;

    /* 陰影層級 */
    --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.15);

    /* 邊框圓角 */
    --border-radius-sm: 0.25rem;
    --border-radius-md: 0.5rem;
    --border-radius-lg: 0.75rem;
    --border-radius-xl: 1rem;
}

/* 24px */
--spacing-xl: 2rem;
/* 32px */
--spacing-2xl: 3rem;
/* 48px */
--spacing-3xl: 4rem;
/* 64px */

/* 邊框圓角 */
--border-radius-sm: 4px;
--border-radius-md: 8px;
--border-radius-lg: 12px;

/* 過渡效果 */
--transition-fast: 0.15s ease;
--transition-normal: 0.3s ease;
--transition-slow: 0.5s ease;
}

/* ===== 基礎重置和設定 ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    /* IE 兼容性 */
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
    /* IE 不支援 scroll-behavior，使用 JavaScript 替代 */
    -ms-overflow-style: scrollbar;
    /* IE 滾動條樣式 */
}

body {
    font-family: 'Noto Sans TC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Microsoft JhengHei', sans-serif;
    font-size: var(--font-size-base, 16px);
    /* IE fallback */
    line-height: 1.6;
    color: var(--text-dark, #333333);
    /* IE fallback */
    background-color: var(--background-white, #FFFFFF);
    /* IE fallback */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    /* IE 文字渲染優化 */
    -ms-text-size-adjust: 100%;
    -webkit-text-size-adjust: 100%;
}

/* ===== Typography 樣式 ===== */
h1,
h2,
h3,
h4,
h5,
h6 {
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: var(--spacing-md);
    color: var(--text-dark);
}

h1 {
    font-size: var(--font-size-4xl);
    margin-bottom: var(--spacing-lg);
}

h2 {
    font-size: var(--font-size-3xl);
}

h3 {
    font-size: var(--font-size-2xl);
}

h4 {
    font-size: var(--font-size-xl);
}

h5 {
    font-size: var(--font-size-lg);
}

h6 {
    font-size: var(--font-size-base);
}

p {
    margin-bottom: var(--spacing-md);
    line-height: 1.7;
}

a {
    color: var(--primary-gold);
    text-decoration: none;
    transition: color var(--transition-fast);
}

a:hover {
    color: var(--accent-gold);
    text-decoration: underline;
}

/* ===== 基礎 Layout 樣式 ===== */
.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-md);
}

.container-fluid {
    width: 100%;
    padding: 0 var(--spacing-md);
}

/* ===== Grid 系統 ===== */
.row {
    display: flex;
    display: -webkit-flex;
    /* Safari */
    display: -ms-flexbox;
    /* IE 10 */
    flex-wrap: wrap;
    -webkit-flex-wrap: wrap;
    -ms-flex-wrap: wrap;
    margin: 0 calc(-1 * var(--spacing-sm, 8px));
    /* IE fallback */
    /* IE 不支援 calc() 在某些情況下 */
    margin: 0 -8px\9;
    /* IE 8-9 fallback */
}

.col {
    flex: 1;
    -webkit-flex: 1;
    -ms-flex: 1;
    padding: 0 var(--spacing-sm, 8px);
    /* IE fallback */
    padding: 0 8px\9;
    /* IE 8-9 fallback */
}

/* Grid 欄位系統 */
.col-1 {
    flex: 0 0 8.333333%;
    max-width: 8.333333%;
}

.col-2 {
    flex: 0 0 16.666667%;
    max-width: 16.666667%;
}

.col-3 {
    flex: 0 0 25%;
    max-width: 25%;
}

.col-4 {
    flex: 0 0 33.333333%;
    max-width: 33.333333%;
}

.col-5 {
    flex: 0 0 41.666667%;
    max-width: 41.666667%;
}

.col-6 {
    flex: 0 0 50%;
    max-width: 50%;
}

.col-7 {
    flex: 0 0 58.333333%;
    max-width: 58.333333%;
}

.col-8 {
    flex: 0 0 66.666667%;
    max-width: 66.666667%;
}

.col-9 {
    flex: 0 0 75%;
    max-width: 75%;
}

.col-10 {
    flex: 0 0 83.333333%;
    max-width: 83.333333%;
}

.col-11 {
    flex: 0 0 91.666667%;
    max-width: 91.666667%;
}

.col-12 {
    flex: 0 0 100%;
    max-width: 100%;
}

/* ===== 通用工具類別 ===== */
.text-center {
    text-align: center;
}

.text-left {
    text-align: left;
}

.text-right {
    text-align: right;
}

.text-primary {
    color: var(--primary-gold);
}

.text-secondary {
    color: var(--text-light);
}

.mb-0 {
    margin-bottom: 0 !important;
}

.mb-1 {
    margin-bottom: var(--spacing-xs) !important;
}

.mb-2 {
    margin-bottom: var(--spacing-sm) !important;
}

.mb-3 {
    margin-bottom: var(--spacing-md) !important;
}

.mb-4 {
    margin-bottom: var(--spacing-lg) !important;
}

.mb-5 {
    margin-bottom: var(--spacing-xl) !important;
}

.mt-0 {
    margin-top: 0 !important;
}

.mt-1 {
    margin-top: var(--spacing-xs) !important;
}

.mt-2 {
    margin-top: var(--spacing-sm) !important;
}

.mt-3 {
    margin-top: var(--spacing-md) !important;
}

.mt-4 {
    margin-top: var(--spacing-lg) !important;
}

.mt-5 {
    margin-top: var(--spacing-xl) !important;
}

/* ===== 導航樣式 ===== */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background-color: var(--background-white);
    box-shadow: var(--shadow-md);
    z-index: 1000;
    transition: all var(--transition-normal);
}

.nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md) var(--spacing-lg);
    max-width: 1200px;
    margin: 0 auto;
}

.nav-logo a {
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--primary-gold);
    text-decoration: none;
}

.nav-menu {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    gap: var(--spacing-lg);
}

.nav-item {
    margin: 0;
}

.nav-link {
    display: block;
    padding: var(--spacing-sm) var(--spacing-md);
    color: var(--text-dark);
    text-decoration: none;
    font-weight: 500;
    border-radius: var(--border-radius-md);
    transition: all var(--transition-normal);
}

.nav-link:hover,
.nav-link.active {
    background-color: var(--secondary-beige);
    color: var(--primary-gold);
    transform: translateY(-2px);
}

.nav-toggle {
    display: none;
    flex-direction: column;
    background: none;
    border: none;
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    transition: all var(--transition-normal);
}

.nav-toggle:hover {
    background-color: var(--background-light);
}

.nav-toggle .bar {
    width: 25px;
    height: 3px;
    background-color: var(--text-dark);
    margin: 3px 0;
    transition: var(--transition-normal);
    border-radius: 2px;
}

.nav-toggle.active .bar:nth-child(1) {
    transform: rotate(-45deg) translate(-5px, 6px);
}

.nav-toggle.active .bar:nth-child(2) {
    opacity: 0;
}

.nav-toggle.active .bar:nth-child(3) {
    transform: rotate(45deg) translate(-5px, -6px);
}

/* ===== 導航樣式 ===== */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background-color: var(--background-white);
    box-shadow: var(--shadow-md);
    z-index: 1000;
    transition: all var(--transition-normal);
}

.nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md) var(--spacing-lg);
    max-width: 1200px;
    margin: 0 auto;
}

.nav-logo a {
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--primary-gold);
    text-decoration: none;
}

.nav-menu {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    gap: var(--spacing-lg);
}

.nav-item {
    margin: 0;
}

.nav-link {
    display: block;
    padding: var(--spacing-sm) var(--spacing-md);
    color: var(--text-dark);
    text-decoration: none;
    font-weight: 500;
    border-radius: var(--border-radius-md);
    transition: all var(--transition-normal);
}

.nav-link:hover,
.nav-link.active {
    background-color: var(--secondary-beige);
    color: var(--primary-gold);
    transform: translateY(-2px);
}

.nav-toggle {
    display: none;
    flex-direction: column;
    background: none;
    border: none;
    cursor: pointer;
    padding: var(--spacing-sm);
}

.nav-toggle .bar {
    width: 25px;
    height: 3px;
    background-color: var(--text-dark);
    margin: 3px 0;
    transition: var(--transition-normal);
    border-radius: 2px;
}

.nav-toggle.active .bar:nth-child(1) {
    transform: rotate(-45deg) translate(-5px, 6px);
}

.nav-toggle.active .bar:nth-child(2) {
    opacity: 0;
}

.nav-toggle.active .bar:nth-child(3) {
    transform: rotate(45deg) translate(-5px, -6px);
}

/* ===== 按鈕樣式 ===== */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-sm) var(--spacing-lg);
    font-size: var(--font-size-base);
    font-weight: 500;
    text-decoration: none;
    border: 2px solid transparent;
    border-radius: var(--border-radius-md);
    cursor: pointer;
    transition: all var(--transition-normal);
    gap: var(--spacing-sm);
}

.btn-primary {
    background-color: var(--primary-gold);
    color: var(--background-white);
    border-color: var(--primary-gold);
}

.btn-primary:hover {
    background-color: var(--accent-gold);
    border-color: var(--accent-gold);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.btn-outline {
    background-color: transparent;
    color: var(--primary-gold);
    border-color: var(--primary-gold);
}

.btn-outline:hover {
    background-color: var(--primary-gold);
    color: var(--background-white);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.btn-lg {
    padding: var(--spacing-md) var(--spacing-xl);
    font-size: var(--font-size-lg);
}

.btn-sm {
    padding: var(--spacing-xs) var(--spacing-md);
    font-size: var(--font-size-sm);
}

/* ===== 卡片樣式 ===== */
.card {
    background-color: var(--background-white);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
    transition: all var(--transition-normal);
}

.card:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-4px);
}

.card-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-light);
}

.card-body {
    padding: var(--spacing-lg);
}

.card-footer {
    padding: var(--spacing-lg);
    border-top: 1px solid var(--border-light);
    background-color: var(--background-light);
}

/* ===== 表單樣式 ===== */
.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-label {
    display: block;
    margin-bottom: var(--spacing-sm);
    font-weight: 500;
    color: var(--text-dark);
}

.form-label.required::after {
    content: ' *';
    color: #dc3545;
}

.form-input,
.form-select,
.form-textarea {
    width: 100%;
    padding: var(--spacing-md);
    border: 2px solid var(--border-light);
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-base);
    font-family: inherit;
    transition: all var(--transition-normal);
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
    outline: none;
    border-color: var(--primary-gold);
    box-shadow: 0 0 0 3px rgba(212, 175, 55, 0.1);
}

.form-textarea {
    resize: vertical;
    min-height: 120px;
}

.error-message {
    color: #dc3545;
    font-size: var(--font-size-sm);
    margin-top: var(--spacing-xs);
    display: none;
}

.form-group.error .error-message {
    display: block;
}

.form-group.error .form-input,
.form-group.error .form-select,
.form-group.error .form-textarea {
    border-color: #dc3545;
}

/* ===== 首頁樣式 ===== */
body {
    padding-top: 80px;
    /* 為固定導航留出空間 */
}

.hero {
    position: relative;
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, var(--secondary-beige) 0%, var(--background-white) 100%);
    overflow: hidden;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="%23D4AF37" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="%23D4AF37" opacity="0.1"/><circle cx="50" cy="10" r="1" fill="%23D4AF37" opacity="0.1"/><circle cx="10" cy="90" r="1" fill="%23D4AF37" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.hero-content {
    position: relative;
    z-index: 2;
    text-align: center;
    padding: var(--spacing-2xl) 0;
}

.hero-title {
    font-size: var(--font-size-4xl);
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: var(--spacing-lg);
    opacity: 0;
    animation: fadeInUp 1s ease-out 0.5s forwards;
}

.hero-subtitle {
    font-size: var(--font-size-xl);
    color: var(--text-light);
    margin-bottom: var(--spacing-md);
    opacity: 0;
    animation: fadeInUp 1s ease-out 0.7s forwards;
}

.hero-description {
    font-size: var(--font-size-lg);
    color: var(--text-light);
    margin-bottom: var(--spacing-2xl);
    opacity: 0;
    animation: fadeInUp 1s ease-out 0.9s forwards;
}

.hero-buttons {
    display: flex;
    gap: var(--spacing-lg);
    justify-content: center;
    flex-wrap: wrap;
    opacity: 0;
    animation: fadeInUp 1s ease-out 1.1s forwards;
}

.hero-scroll-indicator {
    position: absolute;
    bottom: var(--spacing-2xl);
    left: 50%;
    transform: translateX(-50%);
    color: var(--primary-gold);
    font-size: var(--font-size-xl);
    animation: bounce 2s infinite;
    cursor: pointer;
}

/* ===== 動畫定義 ===== */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes bounce {

    0%,
    20%,
    50%,
    80%,
    100% {
        transform: translateX(-50%) translateY(0);
    }

    40% {
        transform: translateX(-50%) translateY(-10px);
    }

    60% {
        transform: translateX(-50%) translateY(-5px);
    }
}

.fade-in-up {
    opacity: 0;
    animation: fadeInUp 0.8s ease-out forwards;
}

.delay-1 {
    animation-delay: 0.2s;
}

.delay-2 {
    animation-delay: 0.4s;
}

.delay-3 {
    animation-delay: 0.6s;
}

/* ===== 區塊樣式 ===== */
.features,
.about,
.testimonials {
    padding: var(--spacing-2xl) 0;
}

.section-header {
    text-align: center;
    margin-bottom: var(--spacing-2xl);
}

.section-title {
    font-size: var(--font-size-3xl);
    color: var(--text-dark);
    margin-bottom: var(--spacing-md);
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background-color: var(--primary-gold);
    border-radius: 2px;
}

.section-subtitle {
    font-size: var(--font-size-lg);
    color: var(--text-light);
}

/* ===== 特色卡片 ===== */
.feature-card {
    background-color: var(--background-white);
    padding: var(--spacing-2xl);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    text-align: center;
    transition: all var(--transition-normal);
    height: 100%;
}

.feature-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-lg);
}

.feature-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-gold), var(--accent-gold));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--spacing-lg);
    font-size: var(--font-size-2xl);
    color: var(--background-white);
}

.feature-content h4 {
    font-size: var(--font-size-xl);
    margin-bottom: var(--spacing-md);
    color: var(--text-dark);
}

.feature-content p {
    color: var(--text-light);
    margin-bottom: var(--spacing-lg);
    line-height: 1.6;
}

.feature-link {
    color: var(--primary-gold);
    font-weight: 500;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    transition: all var(--transition-normal);
}

.feature-link:hover {
    color: var(--accent-gold);
    transform: translateX(5px);
}

/* ===== 關於我們區塊 ===== */
.about {
    background-color: var(--background-light);
}

.about-content {
    padding-right: var(--spacing-xl);
}

.about-text {
    font-size: var(--font-size-lg);
    color: var(--text-light);
    line-height: 1.7;
}

.about-stats {
    display: flex;
    gap: var(--spacing-2xl);
    margin: var(--spacing-2xl) 0;
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: var(--font-size-4xl);
    font-weight: 700;
    color: var(--primary-gold);
    display: block;
}

.stat-label {
    font-size: var(--font-size-sm);
    color: var(--text-light);
    margin-top: var(--spacing-sm);
}

.about-buttons {
    display: flex;
    gap: var(--spacing-lg);
    margin-top: var(--spacing-xl);
}

.about-visual {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-lg);
    align-items: start;
}

.about-visual .visual-card:nth-child(3) {
    grid-column: 1 / -1;
    max-width: 300px;
    margin: 0 auto;
}

.visual-card {
    background-color: var(--background-white);
    padding: var(--spacing-xl);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    text-align: center;
    transition: all var(--transition-normal);
}

.visual-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-md);
}

.visual-card i {
    font-size: var(--font-size-3xl);
    color: var(--primary-gold);
    margin-bottom: var(--spacing-md);
}

.visual-card h5 {
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-sm);
    color: var(--text-dark);
}

.visual-card p {
    color: var(--text-light);
    font-size: var(--font-size-sm);
}

/* ===== 客戶見證 ===== */
.testimonial-card {
    background-color: var(--background-white);
    padding: var(--spacing-2xl);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    position: relative;
    transition: all var(--transition-normal);
}

.testimonial-card::before {
    content: '"';
    position: absolute;
    top: -10px;
    left: var(--spacing-lg);
    font-size: 4rem;
    color: var(--primary-gold);
    font-family: serif;
    line-height: 1;
}

.testimonial-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.testimonial-content {
    margin-bottom: var(--spacing-lg);
    padding-top: var(--spacing-md);
}

.testimonial-content p {
    font-size: var(--font-size-lg);
    color: var(--text-light);
    font-style: italic;
    line-height: 1.6;
}

.testimonial-author {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.author-info h5 {
    font-size: var(--font-size-base);
    color: var(--text-dark);
    margin-bottom: var(--spacing-xs);
}

.author-info span {
    font-size: var(--font-size-sm);
    color: var(--text-light);
}

/* ===== 頁尾樣式 ===== */
.footer {
    background-color: var(--text-dark);
    color: var(--background-white);
    padding: var(--spacing-2xl) 0 var(--spacing-lg);
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-2xl);
    margin-bottom: var(--spacing-xl);
}

.footer-section h3 {
    color: var(--primary-gold);
    margin-bottom: var(--spacing-lg);
    font-size: var(--font-size-lg);
}

.footer-section p,
.footer-section li {
    color: #cccccc;
    line-height: 1.6;
    margin-bottom: var(--spacing-sm);
}

.footer-section ul {
    list-style: none;
    padding: 0;
}

.footer-section a {
    color: #cccccc;
    text-decoration: none;
    transition: color var(--transition-normal);
}

.footer-section a:hover {
    color: var(--primary-gold);
}

.contact-info p {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.contact-info i {
    color: var(--primary-gold);
    width: 20px;
}

.social-links {
    display: flex;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.social-links a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background-color: var(--primary-gold);
    color: var(--background-white);
    border-radius: 50%;
    transition: all var(--transition-normal);
}

.social-links a:hover {
    background-color: var(--accent-gold);
    transform: translateY(-2px);
}

.footer-info {
    margin-top: var(--spacing-lg);
}

.footer-bottom {
    border-top: 1px solid #444;
    padding-top: var(--spacing-lg);
}

.footer-bottom-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: var(--spacing-md);
}

.footer-links {
    display: flex;
    gap: var(--spacing-lg);
}

.footer-links a {
    color: #cccccc;
    font-size: var(--font-size-sm);
}

/* ===== 頁面標題樣式 ===== */
.page-header {
    background: linear-gradient(135deg, var(--secondary-beige) 0%, var(--background-white) 100%);
    padding: var(--spacing-2xl) 0;
    text-align: center;
    margin-top: 80px;
}

.page-header h1 {
    font-size: var(--font-size-4xl);
    color: var(--text-dark);
    margin-bottom: var(--spacing-md);
}

.page-header p {
    font-size: var(--font-size-lg);
    color: var(--text-light);
}

/* ===== 工具類別 ===== */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

.align-items-center {
    align-items: center;
}

.justify-content-center {
    justify-content: center;
}

.justify-content-between {
    justify-content: space-between;
}

color: var(--text-light);
}

.text-dark {
    color: var(--text-dark);
}

.bg-primary {
    background-color: var(--primary-gold);
}

.bg-secondary {
    background-color: var(--secondary-beige);
}

.bg-light {
    background-color: var(--background-light);
}

.bg-white {
    background-color: var(--background-white);
}

.mb-0 {
    margin-bottom: 0;
}

.mb-1 {
    margin-bottom: var(--spacing-xs);
}

.mb-2 {
    margin-bottom: var(--spacing-sm);
}

.mb-3 {
    margin-bottom: var(--spacing-md);
}

.mb-4 {
    margin-bottom: var(--spacing-lg);
}

.mb-5 {
    margin-bottom: var(--spacing-xl);
}

.mt-0 {
    margin-top: 0;
}

.mt-1 {
    margin-top: var(--spacing-xs);
}

.mt-2 {
    margin-top: var(--spacing-sm);
}

/* ===== 表單樣式 ===== */
.form-group {
    margin-bottom: var(--spacing-lg);
    position: relative;
}

.form-label {
    display: block;
    margin-bottom: var(--spacing-sm);
    font-weight: 500;
    color: var(--text-dark);
    font-size: var(--font-size-sm);
}

.form-label.required::after {
    content: ' *';
    color: #e74c3c;
    font-weight: bold;
}

.form-input,
.form-select,
.form-textarea {
    width: 100%;
    padding: var(--spacing-md);
    border: 2px solid var(--border-light);
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-base);
    font-family: inherit;
    background-color: var(--background-white);
    transition: all var(--transition-normal);
    outline: none;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
    border-color: var(--primary-gold);
    box-shadow: 0 0 0 3px rgba(212, 175, 55, 0.1);
}

.form-textarea {
    resize: vertical;
    min-height: 120px;
}

/* ===== 錯誤處理樣式 ===== */
.form-group.error .form-input,
.form-group.error .form-select,
.form-group.error .form-textarea {
    border-color: #e74c3c;
    background-color: #fdf2f2;
}

.form-group.error .form-input:focus,
.form-group.error .form-select:focus,
.form-group.error .form-textarea:focus {
    border-color: #e74c3c;
    box-shadow: 0 0 0 3px rgba(231, 76, 60, 0.1);
}

.error-message {
    display: none;
    margin-top: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    background-color: #fdf2f2;
    border: 1px solid #e74c3c;
    border-radius: var(--border-radius-sm);
    color: #c0392b;
    font-size: var(--font-size-sm);
    font-weight: 500;
    position: relative;
}

.error-message.show {
    display: block;
    animation: slideDown 0.3s ease-out;
}

.error-message::before {
    content: '⚠';
    margin-right: var(--spacing-sm);
    font-weight: bold;
}

/* ===== 成功訊息樣式 ===== */
.success-message {
    display: none;
    margin-top: var(--spacing-md);
    padding: var(--spacing-lg);
    background-color: #d4edda;
    border: 1px solid #27ae60;
    border-radius: var(--border-radius-md);
    color: #155724;
    font-size: var(--font-size-base);
    text-align: center;
}

.success-message.show {
    display: block;
    animation: slideDown 0.3s ease-out;
}

.success-message::before {
    content: '✓';
    margin-right: var(--spacing-sm);
    font-weight: bold;
    color: #27ae60;
}

/* ===== 載入動畫和狀態指示器 ===== */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid var(--primary-gold);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
}

.loading-overlay.show {
    opacity: 1;
    visibility: visible;
}

.loading-content {
    background-color: var(--background-white);
    padding: var(--spacing-2xl);
    border-radius: var(--border-radius-lg);
    text-align: center;
    box-shadow: 0 10px 30px var(--shadow-medium);
}

.loading-spinner-large {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid var(--primary-gold);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto var(--spacing-md);
}

.loading-text {
    color: var(--text-dark);
    font-size: var(--font-size-lg);
    font-weight: 500;
}

/* ===== 按鈕樣式增強 ===== */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-md) var(--spacing-xl);
    border: none;
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-base);
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
    min-height: 48px;
    /* 無障礙觸控目標大小 */
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    pointer-events: none;
}

.btn-primary {
    background-color: var(--primary-gold);
    color: var(--background-white);
}

.btn-primary:hover:not(:disabled) {
    background-color: var(--accent-gold);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(212, 175, 55, 0.3);
}

.btn-secondary {
    background-color: transparent;
    color: var(--primary-gold);
    border: 2px solid var(--primary-gold);
}

.btn-secondary:hover:not(:disabled) {
    background-color: var(--primary-gold);
    color: var(--background-white);
}

.btn i {
    margin-right: var(--spacing-sm);
}

.btn.loading {
    pointer-events: none;
}

.btn.loading i {
    animation: spin 1s linear infinite;
}

/* ===== 數據載入失敗處理 ===== */
.data-error {
    text-align: center;
    padding: var(--spacing-2xl);
    background-color: #fdf2f2;
    border: 2px dashed #e74c3c;
    border-radius: var(--border-radius-lg);
    margin: var(--spacing-lg) 0;
}

.data-error-icon {
    font-size: var(--font-size-4xl);
    color: #e74c3c;
    margin-bottom: var(--spacing-md);
}

.data-error-title {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: var(--spacing-sm);
}

.data-error-message {
    color: var(--text-light);
    margin-bottom: var(--spacing-lg);
}

.retry-btn {
    background-color: #e74c3c;
    color: white;
    border: none;
    padding: var(--spacing-md) var(--spacing-xl);
    border-radius: var(--border-radius-md);
    cursor: pointer;
    font-size: var(--font-size-base);
    font-weight: 500;
    transition: all var(--transition-normal);
}

.retry-btn:hover {
    background-color: #c0392b;
    transform: translateY(-1px);
}

/* ===== 無障礙功能增強 ===== */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

.focus-visible {
    outline: 2px solid var(--primary-gold);
    outline-offset: 2px;
}

/* 高對比度模式支援 */
@media (prefers-contrast: high) {
    :root {
        --primary-gold: #B8860B;
        --text-dark: #000000;
        --border-light: #666666;
    }
}

/* 減少動畫偏好支援 */
@media (prefers-reduced-motion: reduce) {

    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}

/* ===== 動畫定義 ===== */
@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

@keyframes slideDown {
    0% {
        opacity: 0;
        transform: translateY(-10px);
    }

    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeIn {
    0% {
        opacity: 0;
    }

    100% {
        opacity: 1;
    }
}

@keyframes shake {

    0%,
    100% {
        transform: translateX(0);
    }

    25% {
        transform: translateX(-5px);
    }

    75% {
        transform: translateX(5px);
    }
}

.mt-3 {
    margin-top: var(--spacing-md);
}

.mt-4 {
    margin-top: var(--spacing-lg);
}

.mt-5 {
    margin-top: var(--spacing-xl);
}

.p-0 {
    padding: 0;
}

.p-1 {
    padding: var(--spacing-xs);
}

.p-2 {
    padding: var(--spacing-sm);
}

.p-3 {
    padding: var(--spacing-md);
}

.p-4 {
    padding: var(--spacing-lg);
}

.p-5 {
    padding: var(--spacing-xl);
}

/* ===== 按鈕基礎樣式 ===== */
.btn {
    display: inline-block;
    padding: var(--spacing-sm, 8px) var(--spacing-lg, 24px);
    /* IE fallback */
    font-size: var(--font-size-base, 16px);
    /* IE fallback */
    font-weight: 500;
    text-align: center;
    text-decoration: none;
    border: 2px solid transparent;
    border-radius: var(--border-radius-md, 8px);
    /* IE fallback */
    cursor: pointer;
    transition: all var(--transition-normal, 0.3s ease);
    /* IE fallback */
    /* 跨瀏覽器用戶選擇禁用 */
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    /* IE 按鈕樣式重置 */
    -ms-touch-action: manipulation;
    touch-action: manipulation;
}

.btn-primary {
    background-color: var(--primary-gold);
    color: var(--background-white);
    border-color: var(--primary-gold);
}

.btn-primary:hover {
    background-color: var(--accent-gold);
    border-color: var(--accent-gold);
    color: var(--background-white);
    text-decoration: none;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px var(--shadow-medium);
}

.btn-outline {
    background-color: transparent;
    color: var(--primary-gold);
    border-color: var(--primary-gold);
}

.btn-outline:hover {
    background-color: var(--primary-gold);
    color: var(--background-white);
    text-decoration: none;
}

/* ===== 卡片基礎樣式 ===== */
.card {
    background-color: var(--background-white);
    border: 1px solid var(--border-light);
    border-radius: var(--border-radius-lg);
    box-shadow: 0 2px 8px var(--shadow-light);
    transition: box-shadow var(--transition-normal);
    overflow: hidden;
}

.card:hover {
    box-shadow: 0 4px 16px var(--shadow-medium);
}

.card-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-light);
    background-color: var(--background-light);
}

.card-body {
    padding: var(--spacing-lg);
}

.card-footer {
    padding: var(--spacing-lg);
    border-top: 1px solid var(--border-light);
    background-color: var(--background-light);
}

/* ===== 導航選單樣式 ===== */
.navbar {
    background-color: var(--background-white);
    box-shadow: 0 2px 8px var(--shadow-light);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    transition: box-shadow var(--transition-normal);
}

.navbar.scrolled {
    box-shadow: 0 4px 16px var(--shadow-medium);
}

.nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md) 0;
    max-width: 1200px;
    margin: 0 auto;
    padding-left: var(--spacing-md);
    padding-right: var(--spacing-md);
}

.nav-logo a {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--primary-gold);
    text-decoration: none;
    transition: color var(--transition-fast);
}

.nav-logo a:hover {
    color: var(--accent-gold);
    text-decoration: none;
}

.nav-menu {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    align-items: center;
}

.nav-item {
    margin-left: var(--spacing-lg);
}

.nav-link {
    display: block;
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-size-base);
    font-weight: 500;
    color: var(--text-dark);
    text-decoration: none;
    border-radius: var(--border-radius-md);
    transition: all var(--transition-normal);
    position: relative;
}

.nav-link:hover {
    color: var(--primary-gold);
    background-color: var(--secondary-beige);
    text-decoration: none;
    transform: translateY(-2px);
}

.nav-link.active {
    color: var(--primary-gold);
    background-color: var(--secondary-beige);
    font-weight: 600;
}

.nav-link.active::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 50%;
    transform: translateX(-50%);
    width: 20px;
    height: 2px;
    background-color: var(--primary-gold);
    border-radius: 1px;
}

/* 漢堡選單按鈕 */
.nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    transition: background-color var(--transition-fast);
    border: none;
    background: transparent;
    outline: none;
}

.nav-toggle:hover {
    background-color: var(--secondary-beige);
}

.nav-toggle:focus {
    outline: 2px solid var(--primary-gold);
    outline-offset: 2px;
}

.nav-toggle .bar {
    width: 25px;
    height: 3px;
    background-color: var(--text-dark);
    margin: 3px 0;
    transition: all var(--transition-normal);
    border-radius: 2px;
}

/* 漢堡選單動畫 */
.nav-toggle.active .bar:nth-child(1) {
    transform: rotate(-45deg) translate(-5px, 6px);
    background-color: var(--primary-gold);
}

.nav-toggle.active .bar:nth-child(2) {
    opacity: 0;
}

.nav-toggle.active .bar:nth-child(3) {
    transform: rotate(45deg) translate(-5px, -6px);
    background-color: var(--primary-gold);
}

/* 為固定導航留出空間 */
body {
    padding-top: 80px;
}

/* ===== 頁尾樣式 ===== */
.footer {
    background-color: var(--text-dark);
    color: var(--background-white);
    padding: var(--spacing-2xl) 0 var(--spacing-lg);
    margin-top: var(--spacing-3xl);
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
}

.footer-section h3 {
    color: var(--primary-gold);
    margin-bottom: var(--spacing-md);
    font-size: var(--font-size-lg);
}

.footer-section p {
    margin-bottom: var(--spacing-sm);
    color: #cccccc;
}

.footer-section ul {
    list-style: none;
    padding: 0;
}

.footer-section ul li {
    margin-bottom: var(--spacing-sm);
}

.footer-section ul li a {
    color: #cccccc;
    text-decoration: none;
    transition: color var(--transition-fast);
}

.footer-section ul li a:hover {
    color: var(--primary-gold);
    text-decoration: none;
}

.footer-bottom {
    text-align: center;
    padding-top: var(--spacing-lg);
    border-top: 1px solid #555555;
    color: #cccccc;
}

/* ===== 表單基礎樣式 ===== */
.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-label {
    display: block;
    margin-bottom: var(--spacing-sm);
    font-weight: 500;
    color: var(--text-dark);
}

.form-control {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-size-base);
    line-height: 1.5;
    color: var(--text-dark);
    background-color: var(--background-white);
    border: 1px solid var(--border-light);
    border-radius: var(--border-radius-md);
    transition: border-color var(--transition-fast), box-shadow var(--transition-fast);
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-gold);
    box-shadow: 0 0 0 3px rgba(212, 175, 55, 0.1);
}

/* ===== 首頁專用樣式 ===== */

/* Hero Section 樣式 */
.hero {
    position: relative;
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    background: linear-gradient(135deg, var(--secondary-beige) 0%, var(--background-white) 100%);
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 80%, rgba(212, 175, 55, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(212, 175, 55, 0.1) 0%, transparent 50%);
    animation: backgroundFloat 20s ease-in-out infinite;
}

.hero-content {
    position: relative;
    z-index: 2;
    padding: var(--spacing-3xl) 0;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: var(--spacing-lg);
    line-height: 1.1;
}

.hero-subtitle {
    font-size: var(--font-size-xl);
    color: var(--text-light);
    margin-bottom: var(--spacing-md);
    font-weight: 400;
}

.hero-description {
    font-size: var(--font-size-lg);
    color: var(--text-light);
    margin-bottom: var(--spacing-2xl);
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.hero-buttons {
    display: flex;
    gap: var(--spacing-lg);
    justify-content: center;
    flex-wrap: wrap;
}

.btn-lg {
    padding: var(--spacing-md) var(--spacing-2xl);
    font-size: var(--font-size-lg);
    font-weight: 600;
}

.hero-scroll-indicator {
    position: absolute;
    bottom: var(--spacing-xl);
    left: 50%;
    transform: translateX(-50%);
    color: var(--primary-gold);
    font-size: var(--font-size-xl);
    animation: bounce 2s infinite;
    cursor: pointer;
    transition: color var(--transition-fast);
}

.hero-scroll-indicator:hover {
    color: var(--accent-gold);
}

/* 動畫定義 */
@keyframes backgroundFloat {

    0%,
    100% {
        transform: translateY(0px) rotate(0deg);
    }

    33% {
        transform: translateY(-10px) rotate(1deg);
    }

    66% {
        transform: translateY(5px) rotate(-1deg);
    }
}

@keyframes bounce {

    0%,
    20%,
    50%,
    80%,
    100% {
        transform: translateX(-50%) translateY(0);
    }

    40% {
        transform: translateX(-50%) translateY(-10px);
    }

    60% {
        transform: translateX(-50%) translateY(-5px);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }

    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }

    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.8);
    }

    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* 聯絡頁面樣式 */
.contact-info {
    padding: var(--spacing-3xl) 0;
    background-color: var(--background-light);
}

.contact-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-xl);
    margin-top: var(--spacing-2xl);
}

.contact-card {
    background: var(--background-white);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-2xl);
    text-align: center;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.contact-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-gold), var(--accent-gold));
    transform: scaleX(0);
    transition: transform var(--transition-normal);
}

.contact-card:hover::before {
    transform: scaleX(1);
}

.contact-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.contact-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto var(--spacing-lg);
    background: linear-gradient(135deg, var(--primary-gold), var(--accent-gold));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: var(--background-white);
    transition: transform var(--transition-normal);
}

.contact-card:hover .contact-icon {
    transform: scale(1.1) rotate(5deg);
}

.contact-card h3 {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: var(--spacing-sm);
}

.contact-card p {
    color: var(--text-light);
    margin-bottom: var(--spacing-md);
    line-height: 1.6;
}

.contact-link {
    color: var(--primary-gold);
    font-weight: 600;
    font-size: var(--font-size-lg);
    text-decoration: none;
    display: inline-block;
    margin-bottom: var(--spacing-sm);
    transition: all var(--transition-fast);
}

.contact-link:hover {
    color: var(--accent-gold);
    transform: scale(1.05);
    text-decoration: none;
}

.contact-hours {
    font-size: var(--font-size-sm);
    color: var(--text-light);
    font-style: italic;
    margin-bottom: 0;
}

.contact-address {
    font-style: normal;
    color: var(--primary-gold);
    font-weight: 500;
    line-height: 1.5;
}

/* 聯絡表單樣式 */
.contact-form {
    padding: var(--spacing-3xl) 0;
    background-color: var(--background-white);
}

.form-container {
    max-width: 800px;
    margin: 0 auto;
}

.form-header {
    text-align: center;
    margin-bottom: var(--spacing-2xl);
}

.form-header h2 {
    font-size: var(--font-size-2xl);
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: var(--spacing-md);
}

.form-header p {
    color: var(--text-light);
    font-size: var(--font-size-lg);
}

.message-form {
    background: var(--background-light);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-2xl);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-lg);
}

.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-group label {
    display: block;
    font-weight: 500;
    color: var(--text-dark);
    margin-bottom: var(--spacing-sm);
    font-size: var(--font-size-md);
}

.required {
    color: #e74c3c;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: var(--spacing-md);
    border: 2px solid #e0e0e0;
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-md);
    font-family: inherit;
    transition: all var(--transition-fast);
    background-color: var(--background-white);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-gold);
    box-shadow: 0 0 0 3px rgba(212, 175, 55, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

.form-actions {
    text-align: center;
    margin-top: var(--spacing-xl);
}

.submit-btn {
    background: linear-gradient(135deg, var(--primary-gold), var(--accent-gold));
    color: var(--background-white);
    border: none;
    padding: var(--spacing-md) var(--spacing-2xl);
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-lg);
    font-weight: 600;
    cursor: pointer;
    transition: all var(--transition-normal);
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    min-width: 180px;
    justify-content: center;
}

.submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(212, 175, 55, 0.3);
}

.submit-btn:active {
    transform: translateY(0);
}

.submit-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.error-message {
    color: #e74c3c;
    font-size: var(--font-size-sm);
    margin-top: var(--spacing-xs);
    display: none;
}

.error-message.show {
    display: block;
}

.form-group.error input,
.form-group.error select,
.form-group.error textarea {
    border-color: #e74c3c;
    box-shadow: 0 0 0 3px rgba(231, 76, 60, 0.1);
}

.success-message {
    background: var(--background-light);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-2xl);
    text-align: center;
    border: 2px solid #27ae60;
}

.success-content {
    max-width: 400px;
    margin: 0 auto;
}

.success-content i {
    font-size: 3rem;
    color: #27ae60;
    margin-bottom: var(--spacing-lg);
}

.success-content h3 {
    font-size: var(--font-size-xl);
    color: var(--text-dark);
    margin-bottom: var(--spacing-md);
}

.success-content p {
    color: var(--text-light);
    font-size: var(--font-size-lg);
}

/* 服務項目詳細說明樣式 */
.services-detail {
    padding: var(--spacing-3xl) 0;
    background-color: var(--background-light);
}

.services-detail h2 {
    text-align: center;
    font-size: var(--font-size-2xl);
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: var(--spacing-2xl);
    position: relative;
}

.services-detail h2::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-gold), var(--accent-gold));
    border-radius: 2px;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-xl);
    margin-top: var(--spacing-2xl);
}

.service-item {
    background: var(--background-white);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-2xl);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.service-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-gold), var(--accent-gold));
    transform: scaleX(0);
    transition: transform var(--transition-normal);
}

.service-item:hover::before {
    transform: scaleX(1);
}

.service-item:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.service-icon {
    width: 70px;
    height: 70px;
    background: linear-gradient(135deg, var(--primary-gold), var(--accent-gold));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.8rem;
    color: var(--background-white);
    margin-bottom: var(--spacing-lg);
    transition: transform var(--transition-normal);
}

.service-item:hover .service-icon {
    transform: scale(1.1) rotate(5deg);
}

.service-item h3 {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: var(--spacing-md);
}

.service-item p {
    color: var(--text-light);
    margin-bottom: var(--spacing-lg);
    line-height: 1.6;
}

.service-item ul {
    list-style: none;
    padding: 0;
}

.service-item li {
    color: var(--text-light);
    margin-bottom: var(--spacing-sm);
    padding-left: var(--spacing-lg);
    position: relative;
}

.service-item li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: var(--primary-gold);
    font-weight: bold;
}

/* 淡入動畫類別 */
.fade-in-up {
    animation: fadeInUp 0.8s ease-out forwards;
    opacity: 0;
}

.delay-1 {
    animation-delay: 0.2s;
}

.delay-2 {
    animation-delay: 0.4s;
}

.delay-3 {
    animation-delay: 0.6s;
}

.delay-4 {
    animation-delay: 0.8s;
}

/* Section 通用樣式 */
.section-header {
    margin-bottom: var(--spacing-3xl);
}

.section-title {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: var(--spacing-md);
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-gold), var(--accent-gold));
    border-radius: 2px;
}

.section-subtitle {
    font-size: var(--font-size-lg);
    color: var(--text-light);
    font-weight: 400;
}

/* 服務特色樣式 */
.features {
    padding: var(--spacing-3xl) 0;
    background-color: var(--background-light);
}

.feature-card {
    background: var(--background-white);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-2xl);
    text-align: center;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: all var(--transition-normal);
    height: 100%;
    position: relative;
    overflow: hidden;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-gold), var(--accent-gold));
    transform: scaleX(0);
    transition: transform var(--transition-normal);
}

.feature-card:hover::before {
    transform: scaleX(1);
}

.feature-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.feature-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto var(--spacing-lg);
    background: linear-gradient(135deg, var(--primary-gold), var(--accent-gold));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: var(--background-white);
    transition: transform var(--transition-normal);
}

.feature-card:hover .feature-icon {
    transform: scale(1.1) rotate(5deg);
}

.feature-content h4 {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: var(--spacing-md);
}

.feature-content p {
    color: var(--text-light);
    margin-bottom: var(--spacing-lg);
    line-height: 1.6;
}

.feature-link {
    color: var(--primary-gold);
    font-weight: 500;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    transition: all var(--transition-fast);
}

.feature-link:hover {
    color: var(--accent-gold);
    transform: translateX(5px);
    text-decoration: none;
}

.feature-link i {
    transition: transform var(--transition-fast);
}

.feature-link:hover i {
    transform: translateX(3px);
}

/* 關於我們樣式 */
.about {
    padding: var(--spacing-3xl) 0;
    background-color: var(--background-white);
}

.about-content {
    padding-right: var(--spacing-xl);
}

.about-text {
    font-size: var(--font-size-lg);
    line-height: 1.7;
    color: var(--text-light);
}

.about-stats {
    display: flex;
    gap: var(--spacing-2xl);
    margin: var(--spacing-2xl) 0;
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    color: var(--primary-gold);
    display: block;
    margin-bottom: var(--spacing-sm);
}

.stat-label {
    font-size: var(--font-size-sm);
    color: var(--text-light);
    font-weight: 500;
}

.about-buttons {
    display: flex;
    gap: var(--spacing-lg);
    flex-wrap: wrap;
}

.about-visual {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-lg);
    padding-left: var(--spacing-xl);
}

.visual-card {
    background: var(--background-light);
    padding: var(--spacing-xl);
    border-radius: var(--border-radius-lg);
    text-align: center;
    transition: all var(--transition-normal);
    border: 2px solid transparent;
}

.visual-card:hover {
    background: var(--background-white);
    border-color: var(--primary-gold);
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.visual-card i {
    font-size: 2.5rem;
    color: var(--primary-gold);
    margin-bottom: var(--spacing-md);
    display: block;
}

.visual-card h5 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: var(--spacing-sm);
}

.visual-card p {
    font-size: var(--font-size-sm);
    color: var(--text-light);
    margin: 0;
}

.visual-card:nth-child(3) {
    grid-column: 1 / -1;
    max-width: 300px;
    margin: 0 auto;
}

/* 客戶見證樣式 */
.testimonials {
    padding: var(--spacing-3xl) 0;
    background: linear-gradient(135deg, var(--secondary-beige) 0%, var(--background-light) 100%);
}

.testimonial-card {
    background: var(--background-white);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-2xl);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: all var(--transition-normal);
    height: 100%;
    position: relative;
}

.testimonial-card::before {
    content: '"';
    position: absolute;
    top: -10px;
    left: var(--spacing-lg);
    font-size: 4rem;
    color: var(--primary-gold);
    font-family: serif;
    line-height: 1;
}

.testimonial-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.testimonial-content {
    margin-bottom: var(--spacing-lg);
    padding-top: var(--spacing-md);
}

.testimonial-content p {
    font-size: var(--font-size-lg);
    line-height: 1.6;
    color: var(--text-dark);
    font-style: italic;
    margin: 0;
}

.testimonial-author {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.author-info h5 {
    font-size: var(--font-size-base);
    font-weight: 600;
    color: var(--text-dark);
    margin: 0;
}

.author-info span {
    font-size: var(--font-size-sm);
    color: var(--text-light);
}

/* 滾動觸發動畫 */
.scroll-animate {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.8s ease-out;
}

.scroll-animate.animate {
    opacity: 1;
    transform: translateY(0);
}

/* 數字計數動畫 */
.stat-number {
    transition: all 0.3s ease;
}

.stat-number.counting {
    color: var(--accent-gold);
    transform: scale(1.1);
}

/* ===== 
增強頁尾樣式 ===== */
.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-2xl);
    margin-bottom: var(--spacing-2xl);
}

.contact-info p {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-sm);
    color: #cccccc;
}

.contact-info i {
    width: 16px;
    color: var(--primary-gold);
}

.social-links {
    display: flex;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.social-links a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background-color: var(--primary-gold);
    color: var(--background-white);
    border-radius: 50%;
    text-decoration: none;
    transition: all var(--transition-normal);
    font-size: var(--font-size-lg);
}

.social-links a:hover {
    background-color: var(--accent-gold);
    transform: translateY(-2px);
    text-decoration: none;
}

.footer-info p {
    margin-bottom: var(--spacing-xs);
    font-size: var(--font-size-xs);
    color: #999999;
}

.footer-bottom-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: var(--spacing-md);
}

.footer-links {
    display: flex;
    gap: var(--spacing-lg);
}

.footer-links a {
    color: #cccccc;
    text-decoration: none;
    font-size: var(--font-size-sm);
    transition: color var(--transition-fast);
}

.footer-links a:hover {
    color: var(--primary-gold);
    text-decoration: none;
}

/* 響應式頁尾調整 */
@media (max-width: 767px) {
    .footer-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-xl);
    }

    .footer-bottom-content {
        flex-direction: column;
        text-align: center;
    }

    .footer-links {
        justify-content: center;
    }

    .social-links {
        justify-content: center;
    }
}

/*
 ===== 產品頁面樣式 ===== */

/* 頁面標題 */
.page-header {
    background: linear-gradient(135deg, var(--primary-gold), var(--accent-gold));
    color: white;
    padding: var(--spacing-3xl) 0 var(--spacing-2xl);
    text-align: center;
    margin-top: 80px;
    /* 考慮固定導航高度 */
}

.page-header h1 {
    font-size: var(--font-size-4xl);
    font-weight: 700;
    margin-bottom: var(--spacing-md);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.page-header p {
    font-size: var(--font-size-lg);
    opacity: 0.9;
    max-width: 600px;
    margin: 0 auto;
}

/* 產品篩選器 */
.product-filters {
    background: var(--background-light);
    padding: var(--spacing-xl) 0;
    border-bottom: 1px solid var(--border-light);
}

.filter-buttons {
    display: flex;
    justify-content: center;
    gap: var(--spacing-md);
    flex-wrap: wrap;
}

.filter-btn {
    background: white;
    border: 2px solid var(--border-light);
    color: var(--text-dark);
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: 25px;
    font-size: var(--font-size-base);
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.filter-btn:hover {
    border-color: var(--primary-gold);
    color: var(--primary-gold);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px var(--shadow-light);
}

.filter-btn.active {
    background: var(--primary-gold);
    border-color: var(--primary-gold);
    color: white;
    box-shadow: 0 4px 12px rgba(212, 175, 55, 0.3);
}

.filter-btn:focus {
    outline: 2px solid var(--primary-gold);
    outline-offset: 2px;
}

/* 產品展示區 */
.products {
    padding: var(--spacing-3xl) 0;
    background: white;
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--spacing-xl);
    max-width: 1200px;
    margin: 0 auto;
}

/* 產品卡片 */
.product-card {
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px var(--shadow-light);
    overflow: hidden;
    transition: all 0.4s ease;
    opacity: 0;
    transform: translateY(30px);
    border: 1px solid var(--border-light);
}

.product-card.animate {
    opacity: 1;
    transform: translateY(0);
}

.product-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 40px var(--shadow-medium);
}

/* 產品卡片標題 */
.product-card-header {
    background: linear-gradient(135deg, var(--secondary-beige), #f0f0f0);
    padding: var(--spacing-lg);
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.product-icon {
    width: 60px;
    height: 60px;
    background: var(--primary-gold);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: var(--font-size-2xl);
    box-shadow: 0 4px 12px rgba(212, 175, 55, 0.3);
}

.product-category-badge {
    background: var(--primary-gold);
    color: white;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: 12px;
    font-size: var(--font-size-sm);
    font-weight: 500;
}

/* 產品卡片內容 */
.product-card-body {
    padding: var(--spacing-lg);
}

.product-name {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: var(--spacing-sm);
}

.product-description {
    color: var(--text-light);
    line-height: 1.6;
    margin-bottom: var(--spacing-lg);
}

.product-info {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.product-risk,
.product-return {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.risk-label,
.return-label {
    font-size: var(--font-size-sm);
    color: var(--text-light);
    min-width: 80px;
}

.risk-stars {
    color: var(--primary-gold);
    font-size: var(--font-size-lg);
}

.return-value {
    color: var(--primary-gold);
    font-weight: 600;
    font-size: var(--font-size-sm);
}

/* 產品卡片底部 */
.product-card-footer {
    padding: var(--spacing-lg);
    background: var(--background-light);
    display: flex;
    gap: var(--spacing-sm);
}

.btn-details {
    flex: 1;
    background: white;
    border: 1px solid var(--border-light);
    color: var(--text-dark);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: 8px;
    font-size: var(--font-size-sm);
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-xs);
}

.btn-details:hover {
    background: var(--secondary-beige);
    border-color: var(--primary-gold);
}

.btn-contact {
    background: var(--primary-gold);
    border: none;
    color: white;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: 8px;
    font-size: var(--font-size-sm);
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    white-space: nowrap;
}

.btn-contact:hover {
    background: var(--accent-gold);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(212, 175, 55, 0.3);
}

.details-icon {
    transition: transform 0.3s ease;
}

/* 產品詳情 */
.product-details {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.4s ease;
    background: white;
    border-top: 1px solid var(--border-light);
}

.product-details.expanded {
    max-height: 500px;
}

.details-content {
    padding: var(--spacing-lg);
}

.details-content h4 {
    color: var(--text-dark);
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin-bottom: var(--spacing-md);
}

.features-list {
    list-style: none;
    padding: 0;
    margin-bottom: var(--spacing-lg);
}

.features-list li {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-xs) 0;
    color: var(--text-light);
}

.features-list li i {
    color: var(--primary-gold);
    font-size: var(--font-size-sm);
}

.details-actions {
    display: flex;
    gap: var(--spacing-sm);
    flex-wrap: wrap;
}

.details-actions .btn {
    flex: 1;
    min-width: 140px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: 8px;
    font-size: var(--font-size-sm);
    font-weight: 500;
    text-decoration: none;
    transition: all 0.3s ease;
    cursor: pointer;
    border: none;
}

.details-actions .btn-primary {
    background: var(--primary-gold);
    color: white;
}

.details-actions .btn-primary:hover {
    background: var(--accent-gold);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(212, 175, 55, 0.3);
}

.details-actions .btn-secondary {
    background: white;
    color: var(--text-dark);
    border: 1px solid var(--border-light);
}

.details-actions .btn-secondary:hover {
    background: var(--secondary-beige);
    border-color: var(--primary-gold);
}

/* 
===== 市場數據頁面樣式 ===== */

/* 市場概況統計 */
.market-overview {
    background: var(--background-light);
    padding: var(--spacing-2xl) 0;
}

.market-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-xl);
    max-width: 1000px;
    margin: 0 auto;
}

.stat-card {
    background: white;
    padding: var(--spacing-xl);
    border-radius: var(--border-radius-lg);
    text-align: center;
    box-shadow: 0 4px 20px var(--shadow-light);
    transition: all var(--transition-normal);
    border: 1px solid var(--border-light);
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-gold), var(--accent-gold));
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px var(--shadow-medium);
}

.stat-card h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: var(--spacing-md);
}

.stat-value {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    color: var(--primary-gold);
    margin-bottom: var(--spacing-sm);
    transition: all var(--transition-fast);
}

.stat-change {
    font-size: var(--font-size-base);
    font-weight: 500;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    display: inline-block;
    transition: all var(--transition-fast);
}

.stat-change.positive {
    color: #22c55e;
    background-color: rgba(34, 197, 94, 0.1);
}

.stat-change.negative {
    color: #ef4444;
    background-color: rgba(239, 68, 68, 0.1);
}

/* 圖表區域 */
.charts {
    padding: var(--spacing-3xl) 0;
    background: white;
}

.chart-container {
    max-width: 1200px;
    margin: 0 auto;
}

.chart-container h2 {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--text-dark);
    text-align: center;
    margin-bottom: var(--spacing-xl);
}

/* 圖表標籤 */
.chart-tabs {
    display: flex;
    justify-content: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-2xl);
    flex-wrap: wrap;
}

.chart-tab {
    background: white;
    border: 2px solid var(--border-light);
    color: var(--text-dark);
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: 25px;
    font-size: var(--font-size-base);
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.chart-tab:hover {
    border-color: var(--primary-gold);
    color: var(--primary-gold);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px var(--shadow-light);
}

.chart-tab.active {
    background: var(--primary-gold);
    border-color: var(--primary-gold);
    color: white;
    box-shadow: 0 4px 12px rgba(212, 175, 55, 0.3);
}

.chart-tab:focus {
    outline: 2px solid var(--primary-gold);
    outline-offset: 2px;
}

/* 圖表包裝器 */
.chart-wrapper {
    background: white;
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xl);
    box-shadow: 0 4px 20px var(--shadow-light);
    border: 1px solid var(--border-light);
    position: relative;
    height: 500px;
    margin-bottom: var(--spacing-xl);
}

.chart-wrapper canvas {
    max-height: 100%;
    width: 100% !important;
    height: 100% !important;
}

/* 載入動畫 */
.chart-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-md);
    color: var(--text-light);
}

.chart-loading .spinner {
    width: 40px;
    height: 40px;
    border: 3px solid var(--border-light);
    border-top: 3px solid var(--primary-gold);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

/* 市場分析區域 */
.market-analysis {
    background: var(--background-light);
    padding: var(--spacing-3xl) 0;
    text-align: center;
}

.market-analysis h2 {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: var(--spacing-lg);
}

.analysis-content {
    max-width: 600px;
    margin: 0 auto;
}

.analysis-content p {
    font-size: var(--font-size-lg);
    color: var(--text-light);
    line-height: 1.6;
    margin-bottom: var(--spacing-xl);
}

/* 圖表響應式調整 */
@media (max-width: 768px) {
    .market-stats {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    .stat-card {
        padding: var(--spacing-lg);
    }

    .stat-value {
        font-size: var(--font-size-2xl);
    }

    .chart-tabs {
        gap: var(--spacing-xs);
    }

    .chart-tab {
        padding: var(--spacing-xs) var(--spacing-md);
        font-size: var(--font-size-sm);
    }

    .chart-wrapper {
        padding: var(--spacing-md);
        height: 400px;
    }

    .chart-container h2 {
        font-size: var(--font-size-xl);
    }
}

@media (max-width: 480px) {
    .chart-wrapper {
        height: 300px;
        padding: var(--spacing-sm);
    }

    .chart-tabs {
        flex-direction: column;
        align-items: center;
    }

    .chart-tab {
        width: 200px;
        text-align: center;
    }
}

/* 
圖表錯誤狀態 */
.chart-error {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 300px;
    color: var(--text-light);
    text-align: center;
}

.chart-error i {
    font-size: 3rem;
    color: #ef4444;
    margin-bottom: var(--spacing-md);
}

.chart-error p {
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-lg);
}

.chart-error .btn {
    background: var(--primary-gold);
    color: white;
    border: none;
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-base);
    cursor: pointer;
    transition: all var(--transition-normal);
}

.chart-error .btn:hover {
    background: var(--accent-gold);
    transform: translateY(-2px);
}

/* ===== 預約表單樣式 ===== */

/* 預約表單區域 */
.booking-form {
    padding: var(--spacing-3xl) 0;
    background: var(--background-white);
}

.form-container {
    max-width: 600px;
    margin: 0 auto var(--spacing-3xl);
    background: var(--background-white);
    border-radius: var(--border-radius-lg);
    box-shadow: 0 8px 32px var(--shadow-light);
    overflow: hidden;
}

.form-header {
    background: linear-gradient(135deg, var(--primary-gold), var(--accent-gold));
    color: white;
    padding: var(--spacing-2xl);
    text-align: center;
}

.form-header h2 {
    font-size: var(--font-size-2xl);
    font-weight: 600;
    margin-bottom: var(--spacing-sm);
    color: white;
}

.form-header p {
    opacity: 0.9;
    margin: 0;
    font-size: var(--font-size-base);
}

/* 表單樣式 */
.appointment-form {
    padding: var(--spacing-2xl);
}

.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-group label {
    display: block;
    margin-bottom: var(--spacing-sm);
    font-weight: 500;
    color: var(--text-dark);
    font-size: var(--font-size-base);
}

.required {
    color: #e74c3c;
    font-weight: 600;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: var(--spacing-md);
    font-size: var(--font-size-base);
    line-height: 1.5;
    color: var(--text-dark);
    background-color: var(--background-white);
    border: 2px solid var(--border-light);
    border-radius: var(--border-radius-md);
    transition: all var(--transition-normal);
    font-family: inherit;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-gold);
    box-shadow: 0 0 0 3px rgba(212, 175, 55, 0.1);
    background-color: var(--background-white);
}

.form-group input:hover,
.form-group select:hover,
.form-group textarea:hover {
    border-color: var(--primary-gold);
}

.form-group textarea {
    resize: vertical;
    min-height: 100px;
}

.form-group select {
    cursor: pointer;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 12px center;
    background-repeat: no-repeat;
    background-size: 16px;
    padding-right: 40px;
    appearance: none;
}

/* 表單行佈局 */
.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-lg);
}

/* 錯誤訊息樣式 */
.error-message {
    display: none;
    color: #e74c3c;
    font-size: var(--font-size-sm);
    margin-top: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    background-color: #fdf2f2;
    border: 1px solid #fecaca;
    border-radius: var(--border-radius-sm);
    animation: slideDown 0.3s ease;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 表單動作按鈕 */
.form-actions {
    margin-top: var(--spacing-2xl);
    text-align: center;
}

.submit-btn {
    background: linear-gradient(135deg, var(--primary-gold), var(--accent-gold));
    color: white;
    border: none;
    padding: var(--spacing-md) var(--spacing-2xl);
    font-size: var(--font-size-lg);
    font-weight: 600;
    border-radius: var(--border-radius-md);
    cursor: pointer;
    transition: all var(--transition-normal);
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    min-width: 180px;
    justify-content: center;
}

.submit-btn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(212, 175, 55, 0.3);
}

.submit-btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}

.submit-btn i.fa-spinner {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }

    to {
        transform: rotate(360deg);
    }
}

/* 成功訊息樣式 */
.success-message {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
    padding: var(--spacing-2xl);
    border-radius: var(--border-radius-lg);
    text-align: center;
    animation: fadeInScale 0.5s ease;
}

.success-content i {
    font-size: 3rem;
    margin-bottom: var(--spacing-lg);
    display: block;
}

.success-content h3 {
    font-size: var(--font-size-2xl);
    font-weight: 600;
    margin-bottom: var(--spacing-md);
    color: white;
}

.success-content p {
    font-size: var(--font-size-base);
    opacity: 0.9;
    margin: 0;
}

@keyframes fadeInScale {
    from {
        opacity: 0;
        transform: scale(0.8);
    }

    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* 服務資訊區域 */
.service-info {
    max-width: 800px;
    margin: 0 auto;
    text-align: center;
}

.service-info h3 {
    font-size: var(--font-size-2xl);
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: var(--spacing-xl);
}

.info-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-xl);
}

.info-card {
    background: var(--background-light);
    padding: var(--spacing-xl);
    border-radius: var(--border-radius-lg);
    transition: all var(--transition-normal);
    border: 2px solid transparent;
}

.info-card:hover {
    background: var(--background-white);
    border-color: var(--primary-gold);
    transform: translateY(-5px);
    box-shadow: 0 8px 25px var(--shadow-light);
}

.info-card i {
    font-size: 2.5rem;
    color: var(--primary-gold);
    margin-bottom: var(--spacing-md);
    display: block;
}

.info-card h4 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: var(--spacing-sm);
}

.info-card p {
    color: var(--text-light);
    margin: 0;
    font-size: var(--font-size-base);
}

/* 表單驗證狀態 */
.form-group input.valid,
.form-group select.valid,
.form-group textarea.valid {
    border-color: #10b981;
    background-color: #f0fdf4;
}

.form-group input.invalid,
.form-group select.invalid,
.form-group textarea.invalid {
    border-color: #e74c3c;
    background-color: #fef2f2;
}

/* 時間選擇器特殊樣式 */
.form-group select option:disabled {
    color: #999;
    font-style: italic;
}

/* 響應式調整 */
@media (max-width: 768px) {
    .form-row {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    .form-container {
        margin: 0 var(--spacing-md) var(--spacing-2xl);
    }

    .appointment-form {
        padding: var(--spacing-lg);
    }

    .form-header {
        padding: var(--spacing-lg);
    }

    .info-cards {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    .submit-btn {
        width: 100%;
        padding: var(--spacing-md);
    }
}

@media (max-width: 480px) {
    .form-header h2 {
        font-size: var(--font-size-xl);
    }

    .service-info h3 {
        font-size: var(--font-size-xl);
    }

    .success-content h3 {
        font-size: var(--font-size-xl);
    }

    .success-content i {
        font-size: 2.5rem;
    }
}

@ke yframes slideInRight {
    0% {
        opacity: 0;
        transform: translateX(100%);
    }

    100% {
        opacity: 1;
        transform: translateX(0);
    }
}

/* ===== 錯誤通知樣式 ===== */
.error-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
    padding: var(--spacing-md);
    border-radius: var(--border-radius-md);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 10000;
    max-width: 400px;
    animation: slideInRight 0.3s ease-out;
}

.error-notification-content {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.error-notification-close {
    background: none;
    border: none;
    color: #721c24;
    cursor: pointer;
    padding: var(--spacing-xs);
    margin-left: auto;
    border-radius: var(--border-radius-sm);
    transition: background-color var(--transition-fast);
}

.error-notification-close:hover {
    background-color: rgba(114, 28, 36, 0.1);
}

/* ===== 圖表特定樣式 ===== */
.chart-wrapper {
    position: relative;
    min-height: 400px;
}

.chart-loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.9);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10;
}

.chart-error {
    text-align: center;
    padding: var(--spacing-2xl);
    background-color: #fdf2f2;
    border: 2px dashed #e74c3c;
    border-radius: var(--border-radius-lg);
    margin: var(--spacing-lg) 0;
}

/* ===== 響應式錯誤通知 ===== */
@media (max-width: 768px) {
    .error-notification {
        top: 10px;
        right: 10px;
        left: 10px;
        max-width: none;
    }

    .data-error {
        padding: var(--spacing-lg);
    }

    .data-error-icon {
        font-size: var(--font-size-3xl);
    }
}

/ *=====表單輔助文字樣式=====*/ .form-help {
    font-size: var(--font-size-sm);
    color: var(--text-light);
    margin-top: var(--spacing-xs);
    line-height: 1.4;
}

.form-help.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* ===== 表單行樣式 ===== */
.form-row {
    display: flex;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
}

.form-row .form-group {
    flex: 1;
    margin-bottom: 0;
}

@media (max-width: 768px) {
    .form-row {
        flex-direction: column;
        gap: 0;
    }

    .form-row .form-group {
        margin-bottom: var(--spacing-lg);
    }
}

/* ===== 字符計數器 ===== */
.char-counter {
    font-size: var(--font-size-xs);
    color: var(--text-light);
    text-align: right;
    margin-top: var(--spacing-xs);
}

.char-counter.warning {
    color: #f39c12;
}

.char-counter.error {
    color: #e74c3c;
}

/ *=====跨瀏覽器兼容性修復=====*/
/* IE 10-11 Flexbox 修復 */
@media screen and (-ms-high-contrast: active),
(-ms-high-contrast: none) {
    .row {
        display: -ms-flexbox;
    }

    .col {
        -ms-flex: 1 1 auto;
    }

    .nav-menu {
        display: -ms-flexbox;
    }

    .hero-buttons {
        display: -ms-flexbox;
    }
}

/* Safari 特定修復 */
@supports (-webkit-appearance: none) {

    .form-input,
    .form-select,
    .form-textarea {
        -webkit-appearance: none;
        -webkit-border-radius: var(--border-radius-md, 8px);
    }

    .btn {
        -webkit-appearance: none;
        -webkit-border-radius: var(--border-radius-md, 8px);
    }
}

/* Firefox 特定修復 */
@-moz-document url-prefix() {

    .form-input,
    .form-select,
    .form-textarea {
        -moz-appearance: none;
    }

    .btn {
        -moz-appearance: none;
    }
}

/* Edge 特定修復 */
@supports (-ms-ime-align: auto) {
    .grid-container {
        display: -ms-grid;
    }
}

/* ===== 性能優化 ===== */

/* GPU 加速優化 */
.hero-background,
.loading-spinner,
.loading-spinner-large,
.nav-toggle.active .bar {
    transform: translateZ(0);
    -webkit-transform: translateZ(0);
    will-change: transform;
}

/* 動畫性能優化 */
.btn,
.card,
.feature-card,
.nav-link {
    will-change: transform, box-shadow;
}

.btn:hover,
.card:hover,
.feature-card:hover {
    will-change: auto;
}

/* 字體載入優化 */
@font-face {
    font-family: 'Noto Sans TC';
    font-display: swap;
    /* 字體交換策略 */
}

/* 圖片載入優化 */
img {
    max-width: 100%;
    height: auto;
    /* 圖片載入優化 */
    loading: lazy;
    decoding: async;
}

/* 滾動性能優化 */
.navbar,
.hero-scroll-indicator {
    contain: layout style paint;
}

/* ===== 無障礙增強 ===== */

/* 高對比度模式支援 */
@media (prefers-contrast: high) {
    :root {
        --primary-gold: #B8860B;
        --text-dark: #000000;
        --text-light: #333333;
        --border-light: #666666;
        --background-white: #FFFFFF;
        --background-light: #F0F0F0;
    }

    .btn-primary {
        border: 2px solid #000000;
    }

    .card {
        border: 2px solid #666666;
    }
}

/* 減少動畫偏好支援 */
@media (prefers-reduced-motion: reduce) {

    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }

    .hero-background {
        animation: none !important;
    }

    .loading-spinner,
    .loading-spinner-large {
        animation: none !important;
    }
}

/* 焦點指示器增強 */
.keyboard-navigation *:focus {
    outline: 3px solid var(--primary-gold, #D4AF37) !important;
    outline-offset: 2px !important;
    box-shadow: 0 0 0 5px rgba(212, 175, 55, 0.3) !important;
}

/* 觸控設備優化 */
@media (hover: none) and (pointer: coarse) {

    .btn,
    .nav-link,
    .feature-card {
        min-height: 44px;
        /* 觸控目標最小尺寸 */
        min-width: 44px;
    }

    /* 移除 hover 效果 */
    .btn:hover,
    .card:hover,
    .feature-card:hover,
    .nav-link:hover {
        transform: none;
        box-shadow: none;
    }

    /* 添加 active 狀態 */
    .btn:active {
        transform: scale(0.98);
    }
}

/* ===== 列印樣式優化 ===== */
@media print {

    /* 隱藏不必要的元素 */
    .navbar,
    .nav-toggle,
    .hero-scroll-indicator,
    .loading-overlay,
    .error-notification {
        display: none !important;
    }

    /* 優化文字顏色 */
    * {
        color: #000000 !important;
        background: transparent !important;
        box-shadow: none !important;
        text-shadow: none !important;
    }

    /* 優化連結顯示 */
    a[href]:after {
        content: " (" attr(href) ")";
        font-size: 0.8em;
        color: #666666 !important;
    }

    /* 分頁優化 */
    .page-header,
    .section {
        page-break-inside: avoid;
    }

    h1,
    h2,
    h3,
    h4,
    h5,
    h6 {
        page-break-after: avoid;
    }
}

/* ===== 舊版瀏覽器支援 ===== */

/* IE 8-9 支援 */
.ie8 .row,
.ie9 .row {
    display: table;
    width: 100%;
    table-layout: fixed;
}

.ie8 .col,
.ie9 .col {
    display: table-cell;
    vertical-align: top;
}

/* IE 8-9 按鈕樣式 */
.ie8 .btn,
.ie9 .btn {
    display: inline-block;
    zoom: 1;
    /* IE hasLayout 觸發 */
    *display: inline;
    /* IE 6-7 */
}

/* ===== 錯誤處理和降級支援 ===== */

/* CSS Grid 不支援時的降級 */
@supports not (display: grid) {

    .contact-grid,
    .services-grid,
    .products-grid {
        display: flex;
        flex-wrap: wrap;
    }

    .contact-card,
    .service-item,
    .product-card {
        flex: 1 1 300px;
        margin: 0.5rem;
    }
}

/* Flexbox 不支援時的降級 */
@supports not (display: flex) {
    .row {
        display: table;
        width: 100%;
        table-layout: fixed;
    }

    .col {
        display: table-cell;
        vertical-align: top;
    }

    .nav-menu {
        display: table;
    }

    .nav-item {
        display: table-cell;
    }
}

/* CSS 變數不支援時的降級 */
.no-css-variables {
    /* 主要顏色 */
    color: #333333;
    background-color: #FFFFFF;
}

.no-css-variables .btn-primary {
    background-color: #D4AF37;
    border-color: #D4AF37;
}

.no-css-variables .btn-primary:hover {
    background-color: #B8860B;
    border-color: #B8860B;
}

/* ===== 性能監控和調試 ===== */

/* 開發模式樣式 */
.debug-mode * {
    outline: 1px solid red;
}

.debug-mode .container {
    outline: 2px solid blue;
}

.debug-mode .row {
    outline: 2px solid green;
}

.debug-mode .col {
    outline: 1px solid orange;
}

/* 性能警告 */
.performance-warning {
    position: fixed;
    top: 10px;
    right: 10px;
    background: #ff9800;
    color: white;
    padding: 10px;
    border-radius: 4px;
    z-index: 10000;
    font-size: 12px;
    display: none;
}

/* ===== 瀏覽器特定修復 ===== */

/* Chrome 滾動條樣式 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: var(--primary-gold, #D4AF37);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--accent-gold, #B8860B);
}

/* Firefox 滾動條樣式 */
html {
    scrollbar-width: thin;
    scrollbar-color: var(--primary-gold, #D4AF37) #f1f1f1;
}

/* IE 滾動條樣式 */
body {
    -ms-overflow-style: -ms-autohiding-scrollbar;
}

/* 
===== 性能優化樣式 ===== */

/* 預載入關鍵資源 */
.preload-fonts {
    font-display: swap;
}

/* 圖片優化 */
img {
    max-width: 100%;
    height: auto;
    loading: lazy;
    decoding: async;
}

/* 關鍵路徑優化 */
.above-fold {
    contain: layout style paint;
}

/* GPU 加速動畫 */
.gpu-accelerated {
    transform: translateZ(0);
    will-change: transform;
}

/* 減少重繪 */
.no-repaint {
    contain: layout style paint;
}

/* ===== 最終視覺增強 ===== */

/* 平滑滾動 */
html {
    scroll-behavior: smooth;
}

/* 焦點可見性增強 */
*:focus-visible {
    outline: 2px solid var(--primary-gold);
    outline-offset: 2px;
    border-radius: var(--border-radius-sm);
}

/* 選擇文字樣式 */
::selection {
    background-color: var(--primary-gold);
    color: var(--background-white);
}

::-moz-selection {
    background-color: var(--primary-gold);
    color: var(--background-white);
}

/* 滾動條樣式 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--background-light);
}

::-webkit-scrollbar-thumb {
    background: var(--primary-gold);
    border-radius: var(--border-radius-md);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--accent-gold);
}

/* 載入動畫增強 */
@keyframes shimmer {
    0% {
        background-position: -200px 0;
    }

    100% {
        background-position: calc(200px + 100%) 0;
    }
}

.loading-shimmer {
    background: linear-gradient(90deg,
            var(--background-light) 0%,
            rgba(212, 175, 55, 0.1) 50%,
            var(--background-light) 100%);
    background-size: 200px 100%;
    animation: shimmer 1.5s infinite;
}

/* 進入動畫增強 */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translate3d(0, 30px, 0);
    }

    to {
        opacity: 1;
        transform: translate3d(0, 0, 0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translate3d(-30px, 0, 0);
    }

    to {
        opacity: 1;
        transform: translate3d(0, 0, 0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translate3d(30px, 0, 0);
    }

    to {
        opacity: 1;
        transform: translate3d(0, 0, 0);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }

    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* 動畫類別 */
.animate-slide-up {
    animation: slideInUp 0.6s ease-out forwards;
}

.animate-slide-left {
    animation: slideInLeft 0.6s ease-out forwards;
}

.animate-slide-right {
    animation: slideInRight 0.6s ease-out forwards;
}

.animate-scale-in {
    animation: scaleIn 0.4s ease-out forwards;
}

/* 延遲動畫 */
.animate-delay-1 {
    animation-delay: 0.1s;
}

.animate-delay-2 {
    animation-delay: 0.2s;
}

.animate-delay-3 {
    animation-delay: 0.3s;
}

.animate-delay-4 {
    animation-delay: 0.4s;
}

/* 懸停效果增強 */
.hover-lift {
    transition: transform var(--transition-normal) ease,
        box-shadow var(--transition-normal) ease;
}

.hover-lift:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.hover-glow {
    transition: box-shadow var(--transition-normal) ease;
}

.hover-glow:hover {
    box-shadow: 0 0 20px rgba(212, 175, 55, 0.3);
}

/* 按鈕增強效果 */
.btn-enhanced {
    position: relative;
    overflow: hidden;
    transition: all var(--transition-normal) ease;
}

.btn-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
            transparent,
            rgba(255, 255, 255, 0.2),
            transparent);
    transition: left var(--transition-slow) ease;
}

.btn-enhanced:hover::before {
    left: 100%;
}

/* 卡片陰影增強 */
.card-enhanced {
    transition: all var(--transition-normal) ease;
    border-radius: var(--border-radius-lg);
    overflow: hidden;
}

.card-enhanced:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
}

/* 文字動畫 */
.text-gradient {
    background: linear-gradient(135deg, var(--primary-gold), var(--accent-gold));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* 響應式字體大小 */
@media (max-width: 768px) {
    :root {
        --font-size-4xl: 1.875rem;
        /* 30px */
        --font-size-3xl: 1.5rem;
        /* 24px */
        --font-size-2xl: 1.25rem;
        /* 20px */
    }
}

/* 高對比度模式支援 */
@media (prefers-contrast: high) {
    :root {
        --primary-gold: #B8860B;
        --text-dark: #000000;
        --border-light: #999999;
    }
}

/* 減少動畫偏好 */
@media (prefers-reduced-motion: reduce) {

    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}

/* 深色模式支援 */
@media (prefers-color-scheme: dark) {
    :root {
        --background-white: #1a1a1a;
        --background-light: #2d2d2d;
        --text-dark: #ffffff;
        --text-light: #cccccc;
        --border-light: #404040;
    }
}

/* 列印樣式 */
@media print {
    * {
        background: transparent !important;
        color: black !important;
        box-shadow: none !important;
        text-shadow: none !important;
    }

    .navbar,
    .footer,
    .btn,
    .hero-scroll-indicator {
        display: none !important;
    }

    a,
    a:visited {
        text-decoration: underline;
    }

    .page-header {
        page-break-after: avoid;
    }

    h1,
    h2,
    h3 {
        page-break-after: avoid;
    }
}

/*
 ===== 產品頁面樣式 ===== */
.product-filters {
    padding: var(--spacing-xl) 0;
    background-color: var(--background-light);
}

.filter-buttons {
    display: flex;
    justify-content: center;
    gap: var(--spacing-md);
    flex-wrap: wrap;
}

.filter-btn {
    padding: var(--spacing-sm) var(--spacing-lg);
    background-color: var(--background-white);
    color: var(--text-dark);
    border: 2px solid var(--border-light);
    border-radius: 25px;
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-normal);
}

.filter-btn:hover,
.filter-btn.active {
    background-color: var(--primary-gold);
    color: var(--background-white);
    border-color: var(--primary-gold);
    transform: translateY(-2px);
}

.products {
    padding: var(--spacing-2xl) 0;
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--spacing-2xl);
}

.product-card {
    background-color: var(--background-white);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
    transition: all var(--transition-normal);
}

.product-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-lg);
}

.product-card-header {
    padding: var(--spacing-xl);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(135deg, var(--secondary-beige), var(--background-white));
}

.product-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-gold), var(--accent-gold));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-2xl);
    color: var(--background-white);
}

.product-category-badge {
    background-color: var(--primary-gold);
    color: var(--background-white);
    padding: var(--spacing-xs) var(--spacing-md);
    border-radius: 15px;
    font-size: var(--font-size-xs);
    font-weight: 500;
}

.product-card-body {
    padding: var(--spacing-xl);
}

.product-name {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: var(--spacing-md);
}

.product-description {
    color: var(--text-light);
    line-height: 1.6;
    margin-bottom: var(--spacing-lg);
}

.product-info {
    display: flex;
    justify-content: space-between;
    margin-bottom: var(--spacing-lg);
}

.product-risk,
.product-return {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.risk-label,
.return-label {
    font-size: var(--font-size-sm);
    color: var(--text-light);
    min-width: 60px;
}

.risk-value,
.return-value {
    font-weight: 600;
    color: var(--text-dark);
}

.product-card-footer {
    padding: var(--spacing-xl);
    background-color: var(--background-light);
    display: flex;
    gap: var(--spacing-md);
}

.btn-details,
.btn-contact {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
}

.product-details {
    max-height: 0;
    overflow: hidden;
    transition: max-height var(--transition-slow) ease;
    background-color: var(--background-light);
}

.product-details.expanded {
    max-height: 500px;
}

.details-content {
    padding: var(--spacing-xl);
}

.details-actions {
    display: flex;
    gap: var(--spacing-md);
    margin-top: var(--spacing-lg);
}

/* ===== 市場數據頁面樣式 ===== */
.market-overview {
    padding: var(--spacing-2xl) 0;
    background-color: var(--background-light);
}

.market-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-xl);
}

.stat-card {
    background-color: var(--background-white);
    padding: var(--spacing-xl);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    text-align: center;
    transition: all var(--transition-normal);
}

.stat-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-md);
}

.stat-card h3 {
    font-size: var(--font-size-lg);
    color: var(--text-light);
    margin-bottom: var(--spacing-md);
}

.stat-value {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: var(--spacing-sm);
}

.stat-change {
    font-size: var(--font-size-base);
    font-weight: 500;
}

.stat-change.positive {
    color: #28a745;
}

.stat-change.negative {
    color: #dc3545;
}

.charts {
    padding: var(--spacing-2xl) 0;
}

.chart-container {
    background-color: var(--background-white);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    padding: var(--spacing-2xl);
}

.chart-container h2 {
    text-align: center;
    margin-bottom: var(--spacing-xl);
}

.chart-tabs {
    display: flex;
    justify-content: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-xl);
}

.chart-tab {
    padding: var(--spacing-sm) var(--spacing-lg);
    background-color: var(--background-light);
    color: var(--text-dark);
    border: none;
    border-radius: var(--border-radius-md);
    cursor: pointer;
    transition: all var(--transition-normal);
}

.chart-tab:hover,
.chart-tab.active {
    background-color: var(--primary-gold);
    color: var(--background-white);
}

.chart-wrapper {
    position: relative;
    height: 400px;
    margin-bottom: var(--spacing-xl);
}

.market-analysis {
    padding: var(--spacing-2xl) 0;
    background-color: var(--background-light);
    text-align: center;
}

.analysis-content {
    max-width: 600px;
    margin: 0 auto;
}

.cta-button {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-xl);
    background-color: var(--primary-gold);
    color: var(--background-white);
    text-decoration: none;
    border-radius: var(--border-radius-md);
    font-weight: 500;
    margin-top: var(--spacing-lg);
    transition: all var(--transition-normal);
}

.cta-button:hover {
    background-color: var(--accent-gold);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

/* ===== 預約頁面樣式 ===== */
.booking-form {
    padding: var(--spacing-2xl) 0;
}

.form-container {
    max-width: 800px;
    margin: 0 auto;
    background-color: var(--background-white);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    padding: var(--spacing-2xl);
}

.form-header {
    text-align: center;
    margin-bottom: var(--spacing-2xl);
}

.form-header h2 {
    color: var(--text-dark);
    margin-bottom: var(--spacing-md);
}

.form-header p {
    color: var(--text-light);
}

.appointment-form {
    margin-bottom: var(--spacing-2xl);
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-lg);
}

.form-actions {
    text-align: center;
    margin-top: var(--spacing-xl);
}

.submit-btn {
    min-width: 200px;
}

.success-message {
    background-color: #d4edda;
    color: #155724;
    padding: var(--spacing-xl);
    border-radius: var(--border-radius-lg);
    text-align: center;
    border: 1px solid #c3e6cb;
}

.success-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-md);
}

.success-content i {
    font-size: var(--font-size-4xl);
    color: #28a745;
}

.service-info {
    margin-top: var(--spacing-2xl);
}

.service-info h3 {
    text-align: center;
    margin-bottom: var(--spacing-xl);
}

.info-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-lg);
}

.info-card {
    background-color: var(--background-white);
    padding: var(--spacing-xl);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    text-align: center;
    transition: all var(--transition-normal);
}

.info-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-md);
}

.info-card i {
    font-size: var(--font-size-3xl);
    color: var(--primary-gold);
    margin-bottom: var(--spacing-md);
}

.info-card h4 {
    margin-bottom: var(--spacing-sm);
}

/* ===== 聯絡頁面樣式 ===== */
.contact-info {
    padding: var(--spacing-2xl) 0;
}

.contact-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-xl);
}

.contact-card {
    background-color: var(--background-white);
    padding: var(--spacing-2xl);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    text-align: center;
    transition: all var(--transition-normal);
}

.contact-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-md);
}

.contact-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-gold), var(--accent-gold));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--spacing-lg);
    font-size: var(--font-size-2xl);
    color: var(--background-white);
}

.contact-card h3 {
    margin-bottom: var(--spacing-md);
}

.contact-link {
    color: var(--primary-gold);
    font-weight: 600;
    font-size: var(--font-size-lg);
    text-decoration: none;
    display: block;
    margin: var(--spacing-sm) 0;
}

.contact-link:hover {
    color: var(--accent-gold);
}

.contact-hours {
    color: var(--text-light);
    font-size: var(--font-size-sm);
    margin-top: var(--spacing-sm);
}

.contact-address {
    font-style: normal;
    color: var(--text-dark);
    font-weight: 500;
}

.contact-form {
    padding: var(--spacing-2xl) 0;
    background-color: var(--background-light);
}

.message-form {
    background-color: var(--background-white);
    padding: var(--spacing-2xl);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
}

.services-detail {
    padding: var(--spacing-2xl) 0;
}

.services-detail h2 {
    text-align: center;
    margin-bottom: var(--spacing-2xl);
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-2xl);
}

.service-item {
    background-color: var(--background-white);
    padding: var(--spacing-2xl);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-normal);
}

.service-item:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-md);
}

.service-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-gold), var(--accent-gold));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: var(--spacing-lg);
    font-size: var(--font-size-2xl);
    color: var(--background-white);
}

.service-item h3 {
    margin-bottom: var(--spacing-md);
}

.service-item ul {
    list-style: none;
    padding: 0;
    margin-top: var(--spacing-md);
}

.service-item li {
    padding: var(--spacing-xs) 0;
    color: var(--text-light);
    position: relative;
    padding-left: var(--spacing-lg);
}

.service-item li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: var(--primary-gold);
    font-weight: bold;
}