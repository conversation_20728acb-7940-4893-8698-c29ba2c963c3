#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Flask API 應用 - 金屬價格管理系統
提供價格更新和查詢API，支持表單驗證和CORS
整合所有功能到單一文件，支持跨平台部署
"""

from flask import Flask, request, jsonify, render_template, redirect, url_for, flash, session, Response
from flask_cors import CORS
from flask_wtf import FlaskForm
from wtforms import FloatField, SubmitField, StringField
from wtforms.validators import DataRequired, NumberRange, Length
from datetime import datetime, timedelta
import json
import os
import sys
import logging
import random
import string
import stat
import hashlib
import time
import requests
import threading
from PIL import Image, ImageDraw, ImageFont
import io
import base64
import pytz

# 設置時區
tz = pytz.timezone('Asia/Taipei')
# 設置日誌
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 獲取可執行檔的路徑（包括 exe 或 py）
executable_path = sys.executable

# 如果是打包成 exe 後的可執行檔，則取得該檔案所在目錄
if getattr(sys, 'frozen', False):
    executable_dir = os.path.dirname(executable_path)
else:
    # 如果是直接運行 .py 檔案，則取得該檔案所在目錄
    executable_dir = os.path.dirname(os.path.abspath(__file__))

def generate_secure_token(length=32):
    """生成安全的英文+數字 token"""
    characters = string.ascii_letters + string.digits  # a-z, A-Z, 0-9
    return ''.join(random.choice(characters) for _ in range(length))

def init_env_file():
    """初始化 .env 文件"""
    env_path = os.path.join(executable_dir, ".env")

    if not os.path.exists(env_path):
        print("🔐 .env 文件不存在，正在生成...")

        # 生成安全的 tokens
        flask_secret = generate_secure_token(64)  # Flask SECRET_KEY
        admin_token = generate_secure_token(32)   # 管理員 token
        cron_secret = generate_secure_token(32)   # Cron 觸發密鑰

        env_content = f"""# Flask 應用配置 - 自動生成
# 請妥善保管此文件，不要提交到版本控制系統

# Flask 應用密鑰（用於 session 和 CSRF）
FLASK_SECRET_KEY={flask_secret}

# 管理員認證 token
ADMIN_TOKEN={admin_token}

# Cron 定時更新設定
# 用於外部 cron 任務觸發價格更新的密鑰
# 使用方式：curl "http://your-domain/api/cron/update-prices?key={cron_secret}"
# 建議定期更換此密鑰以提高安全性
CRON_SECRET_KEY={cron_secret}

# TwelveData API 金鑰
TWELVEDATA_API_KEY=

# 其他配置
FLASK_ENV=production
FLASK_HOST=0.0.0.0
FLASK_PORT=5000
"""

        try:
            with open(env_path, 'w', encoding='utf-8') as f:
                f.write(env_content)

            # 設置文件權限（僅所有者可讀寫）
            os.chmod(env_path, stat.S_IRUSR | stat.S_IWUSR)

            print(f"✅ .env 文件已生成")
            return admin_token
        except Exception:
            return None
    else:
        return None

def load_env_file():
    """讀取 .env 文件"""
    env_path = os.path.join(executable_dir, ".env")

    if os.path.exists(env_path):
        try:
            with open(env_path, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        os.environ[key.strip()] = value.strip()
            return True
        except Exception:
            return False
    return False

# 初始化 .env 文件
new_admin_token = init_env_file()
load_env_file()

# 創建Flask應用
app = Flask(__name__)
app.config['SECRET_KEY'] = os.environ.get('FLASK_SECRET_KEY', 'fallback-secret-key')

# 配置 session
app.config['PERMANENT_SESSION_LIFETIME'] = timedelta(hours=4)  # session 4小時過期

# 啟用CORS支持
CORS(app, origins=['http://localhost:3000', 'http://localhost:8000', 'http://127.0.0.1:3000', 'http://127.0.0.1:8000'])

# 數據文件路徑 - 使用跨平台路徑
DATA_DIR = os.path.join(executable_dir,"static", "data")
PRICES_FILE = os.path.join(DATA_DIR, "prices.json")
NEWS_FILE = os.path.join(DATA_DIR, "news.json")
BOOKING_FILE = os.path.join(DATA_DIR, "booking.json")
CONTACT_FILE = os.path.join(DATA_DIR, "contact.json")
SETTINGS_FILE = os.path.join(DATA_DIR, "settings.json")

# 確保數據目錄存在
os.makedirs(DATA_DIR, exist_ok=True)

# 移除詳細日誌輸出

# 擴展價格數據結構 - 支援更多金屬和匯率
DEFAULT_PRICES = {
    # 貴金屬 (基礎價格以美元/盎司為準) - 只包含當前 prices.json 中存在的 4 種
    "metals": {
        "gold": {
            "usd_per_oz": 2045.80,
            "auto_update": False,
            "buy_multiplier": 0.98,
            "sell_multiplier": 1.02,
            "enabled": True,
            "update_frequency_seconds": 300,
            "last_update": None,
            "daily_high": None,
            "daily_low": None,
            "previous_close": None
        },
        "silver": {
            "usd_per_oz": 24.50,
            "auto_update": False,
            "buy_multiplier": 0.98,
            "sell_multiplier": 1.02,
            "enabled": True,
            "update_frequency_seconds": 300,
            "last_update": None,
            "daily_high": None,
            "daily_low": None,
            "previous_close": None
        },
        "palladium": {
            "usd_per_oz": 1050.00,
            "auto_update": False,
            "buy_multiplier": 0.95,
            "sell_multiplier": 1.05,
            "enabled": True,
            "update_frequency_seconds": 300,
            "last_update": None,
            "daily_high": None,
            "daily_low": None,
            "previous_close": None
        },
        "platinum": {
            "usd_per_oz": 950.00,
            "auto_update": False,
            "buy_multiplier": 0.96,
            "sell_multiplier": 1.04,
            "enabled": True,
            "update_frequency_seconds": 300,
            "last_update": None,
            "daily_high": None,
            "daily_low": None,
            "previous_close": None
        }
    },

    # 台幣金屬 (台錢報價) - 匹配當前 prices.json 結構
    "metals_twd": {
        "gold_twd": {
            "twd_per_tael": 7521.98,
            "auto_update": False,
            "buy_multiplier": 0.98,
            "sell_multiplier": 1.02,
            "enabled": True,
            "last_update": None
        },
        "silver_twd": {
            "twd_per_tael": 90.08,
            "auto_update": False,
            "buy_multiplier": 0.98,
            "sell_multiplier": 1.02,
            "enabled": True,
            "last_update": None
        },
        "palladium_twd": {
            "twd_per_tael": 3861.00,
            "auto_update": False,
            "buy_multiplier": 0.95,
            "sell_multiplier": 1.05,
            "enabled": True,
            "last_update": None
        },
        "platinum_twd": {
            "twd_per_tael": 3493.46,
            "auto_update": False,
            "buy_multiplier": 0.96,
            "sell_multiplier": 1.04,
            "enabled": True,
            "last_update": None
        }
    },

    # 實物金屬 (公克報價) - 匹配當前 prices.json 結構
    "physical_metals": {
        "gold_gram": {
            "twd_per_gram": 2038.99,
            "auto_update": False,
            "buy_multiplier": 0.98,
            "sell_multiplier": 1.02,
            "enabled": True,
            "last_update": None
        },
        "silver_gram": {
            "twd_per_gram": 24.42,
            "auto_update": False,
            "buy_multiplier": 0.98,
            "sell_multiplier": 1.02,
            "enabled": True,
            "last_update": None
        },
        "palladium_gram": {
            "twd_per_gram": 1046.51,
            "auto_update": False,
            "buy_multiplier": 0.98,
            "sell_multiplier": 1.02,
            "enabled": True,
            "last_update": None
        },
        "platinum_gram": {
            "twd_per_gram": 946.84,
            "auto_update": False,
            "buy_multiplier": 0.98,
            "sell_multiplier": 1.02,
            "enabled": True,
            "last_update": None
        },
        "rhodium_gram": {
            "twd_per_gram": 4485.03,
            "auto_update": False,
            "buy_multiplier": 0.98,
            "sell_multiplier": 1.02,
            "enabled": True,
            "last_update": None
        },
        "ruthenium_gram": {
            "twd_per_gram": 448.50,
            "auto_update": False,
            "buy_multiplier": 0.98,
            "sell_multiplier": 1.02,
            "enabled": True,
            "last_update": None
        },
        "iridium_gram": {
            "twd_per_gram": 1794.01,
            "auto_update": False,
            "buy_multiplier": 0.98,
            "sell_multiplier": 1.02,
            "enabled": True,
            "last_update": None
        },
        "osmium_gram": {
            "twd_per_gram": 398.67,
            "auto_update": False,
            "buy_multiplier": 0.98,
            "sell_multiplier": 1.02,
            "enabled": True,
            "last_update": None
        }
    },

    # 其他材料 (公斤報價) - 匹配當前 prices.json 結構
    "other_materials": {
        "cobalt_kg": {
            "twd_per_kg": 1059.32,
            "auto_update": False,
            "buy_multiplier": 0.90,
            "sell_multiplier": 1.10,
            "enabled": True,
            "last_update": None
        },
        "tin_kg": {
            "twd_per_kg": 970.48,
            "auto_update": False,
            "buy_multiplier": 0.92,
            "sell_multiplier": 1.08,
            "enabled": True,
            "last_update": None
        }
    },

    # 工業金屬 (空的，匹配當前 prices.json 結構)
    "industrial_metals": {},

    # 匯率 (匹配當前 prices.json 結構)
    "currencies": {
        "usd": {
            "twd_rate": 31.00,
            "auto_update": False,
            "buy_multiplier": 0.998,
            "sell_multiplier": 1.002,
            "enabled": True,
            "update_frequency_hours": 1.0,
            "last_update": None
        },
        "cny": {
            "twd_rate": 4.50,
            "auto_update": False,
            "buy_multiplier": 0.998,  # 匹配當前 prices.json
            "sell_multiplier": 1.002,  # 匹配當前 prices.json
            "enabled": True,
            "update_frequency_hours": 1.0,  # 匹配當前 prices.json
            "last_update": None
        },
        "eur": {
            "twd_rate": 35.00,
            "auto_update": False,
            "buy_multiplier": 0.998,  # 匹配當前 prices.json
            "sell_multiplier": 1.002,  # 匹配當前 prices.json
            "enabled": True,
            "update_frequency_hours": 1.0,  # 匹配當前 prices.json
            "last_update": None
        }
    },

    # 更新頻率設定 (匹配當前 prices.json 結構)
    "update_frequencies": {
        "metals_seconds": 300,
        "currencies_base_seconds": 3600
    },

    # 系統資訊
    "lastUpdate": datetime.now(tz).isoformat(),
    "updateTime": datetime.now(tz).strftime('%Y年%m月%d日 %H:%M:%S'),
    "updatedBy": "系統預設",
    "source": "Flask API v4.0",
    "version": "4.0"
}

# 預設新聞數據
DEFAULT_NEWS = {
    "news": [],
    "lastUpdate": datetime.now(tz).isoformat(),
    "updateTime": datetime.now(tz).strftime('%Y年%m月%d日 %H:%M:%S'),
    "updatedBy": "系統預設",
    "source": "Flask API"
}
# DEFAULT_NEWS = {
#     "news": [
#         {
#             "id": 1,
#             "title": "黃金價格創新高",
#             "content": "受全球經濟不確定性影響，黃金價格持續上漲，創下歷史新高。",
#             "date": datetime.now(tz).strftime('%Y-%m-%d'),
#             "time": datetime.now(tz).strftime('%H:%M'),
#             "category": "市場動態"
#         },
#         {
#             "id": 2,
#             "title": "白銀需求增長",
#             "content": "工業需求推動白銀價格穩步上升，預計未來將持續增長。",
#             "date": datetime.now(tz).strftime('%Y-%m-%d'),
#             "time": datetime.now(tz).strftime('%H:%M'),
#             "category": "市場分析"
#         }
#     ],
#     "lastUpdate": datetime.now(tz).isoformat(),
#     "updateTime": datetime.now(tz).strftime('%Y年%m月%d日 %H:%M:%S'),
#     "updatedBy": "系統預設",
#     "source": "Flask API"
# }

# 預設回收預約數據
DEFAULT_BOOKING = {
    "bookings": [],
    "lastUpdate": datetime.now(tz).isoformat(),
    "updateTime": datetime.now(tz).strftime('%Y年%m月%d日 %H:%M:%S'),
    "source": "Flask API"
}

# 預設聯絡我們數據
DEFAULT_CONTACT = {
    "contacts": [],
    "lastUpdate": datetime.now(tz).isoformat(),
    "updateTime": datetime.now(tz).strftime('%Y年%m月%d日 %H:%M:%S'),
    "source": "Flask API"
}

# 預設系統設定
DEFAULT_SETTINGS = {
    "captcha": {
        "enabled": True,
        "difficulty": "easy"  # easy, medium, hard
    },
    "rateLimit": {
        "maxSubmissions": 3,
        "timeWindow": 14400,  # 4 hours in seconds
        "enabled": True
    },
    "twelvedata": {
        "enabled": False,  # 是否啟用 TwelveData API
        "updateInterval": 3600,  # 更新間隔（秒），預設1小時
        "frontendRefreshRate": 300,  # 前端刷新率（秒），預設5分鐘
        "idleTimeout": 300,  # 閒置超時（秒），預設5分鐘
        "pauseOnIdle": True,  # 閒置時是否暫停 TwelveData 請求

        # API 配額管理
        "quota": {
            "daily_limit": 800,  # 每日請求限制 (免費版)
            "minute_limit": 8,   # 每分鐘請求限制 (免費版)
            "current_daily_usage": 0,
            "current_minute_usage": 0,
            "last_reset_date": datetime.now(tz).strftime('%Y-%m-%d'),
            "last_minute_reset": 0
        },

        # 支援的金屬符號映射（包含基礎金屬和可用的稀有金屬）
        "metal_symbols": {
            "gold": "XAU/USD",
            "silver": "XAG/USD",
            "palladium": "XPD/USD",
            "platinum": "XPT/USD",
            # 稀有金屬 - 使用 ETF 或其他可用符號
            "rhodium": "XRH0.LSE"  # Xtrackers Rhodium Exchange Traded Commodity
            # 注意：其他稀有金屬（ruthenium, iridium, osmium）在 TwelveData 中仍不可用
            # 這些金屬的價格需要通過其他方式獲取或手動設定
        },

        # 支援的匯率符號映射
        "currency_symbols": {
            "usd": "USD/TWD",
            "cny": "CNY/TWD",
            "eur": "EUR/TWD"
        },

        # 工業金屬符號 (期貨) - 僅包含確實支援的符號
        "industrial_symbols": {
            "copper": "HG/USD"  # 銅期貨
            # 注意：其他工業金屬（aluminum, cobalt, tin）在 TwelveData 中不可用
            # 這些金屬的價格需要通過其他方式獲取或手動設定
        }
    },
    "lastUpdate": datetime.now(tz).isoformat(),
    "updateTime": datetime.now(tz).strftime('%Y年%m月%d日 %H:%M:%S'),
    "updatedBy": "系統預設"
}

# 階段四：TwelveData 整合類
class TwelveDataIntegration:
    """TwelveData API 整合類 - 階段四實現"""

    def __init__(self, api_key="", config=None):
        self.api_key = api_key
        self.config = config or {}
        self.base_url = "https://api.twelvedata.com"
        self.last_request_time = {}
        self.quota_usage = {
            "daily": 0,
            "minute": 0,
            "last_reset": datetime.now(tz)
        }
        self.is_paused = False
        self.last_activity = datetime.now(tz)

    def set_api_key(self, api_key):
        """設定 API Key"""
        self.api_key = api_key

    def check_quota(self):
        """檢查 API 配額"""
        now = datetime.now(tz)

        # 重置每日配額
        if now.date() > self.quota_usage["last_reset"].date():
            self.quota_usage["daily"] = 0
            self.quota_usage["last_reset"] = now

        # 重置每分鐘配額
        if (now - self.quota_usage["last_reset"]).seconds >= 60:
            self.quota_usage["minute"] = 0
            self.quota_usage["last_reset"] = now

        # 檢查是否超出限制
        daily_limit = self.config.get("quota", {}).get("daily_limit", 800)
        minute_limit = self.config.get("quota", {}).get("minute_limit", 8)

        if self.quota_usage["daily"] >= daily_limit:
            return False, "每日配額已用完"
        if self.quota_usage["minute"] >= minute_limit:
            return False, "每分鐘配額已用完"

        return True, "配額正常"

    def update_quota_usage(self):
        """更新配額使用量"""
        self.quota_usage["daily"] += 1
        self.quota_usage["minute"] += 1

    def get_metal_price(self, symbol):
        """獲取金屬價格"""
        if not self.api_key:
            return None, "API Key 未設定"

        # 檢查配額
        quota_ok, quota_msg = self.check_quota()
        if not quota_ok:
            return None, quota_msg

        try:
            url = f"{self.base_url}/price"
            params = {
                "symbol": symbol,
                "apikey": self.api_key
            }

            response = requests.get(url, params=params, timeout=30)
            self.update_quota_usage()

            if response.status_code == 200:
                data = response.json()
                if "price" in data:
                    return float(data["price"]), "成功"
                else:
                    return None, f"API 回應格式錯誤: {data}"
            else:
                return None, f"API 請求失敗: {response.status_code}"

        except requests.exceptions.Timeout:
            return None, "API 請求超時"
        except requests.exceptions.RequestException as e:
            return None, f"網路錯誤: {str(e)}"
        except Exception as e:
            return None, f"未知錯誤: {str(e)}"


    def update_activity(self):
        """更新活動時間"""
        self.last_activity = datetime.now(tz)
        if self.is_paused:
            self.is_paused = False
            # logger.info("TwelveData API 恢復運行")

    def get_quota_status(self):
        """獲取配額狀態"""
        daily_limit = self.config.get("quota_limit_per_day", 800)
        minute_limit = self.config.get("quota_limit_per_minute", 55)

        return {
            "daily_usage": self.quota_usage["daily"],
            "daily_limit": daily_limit,
            "minute_usage": self.quota_usage["minute"],
            "minute_limit": minute_limit,
            "daily_remaining": daily_limit - self.quota_usage["daily"],
            "minute_remaining": minute_limit - self.quota_usage["minute"],
            "is_paused": self.is_paused,
            "last_activity": self.last_activity.isoformat()
        }

    def test_connection(self):
        """測試 API 連接"""
        if not self.api_key:
            return False, "API Key 未設定"

        # 使用黃金價格測試連接
        price, message = self.get_metal_price("XAU/USD")
        if price is not None:
            return True, f"連接成功，黃金價格: ${price}"
        else:
            return False, f"連接失敗: {message}"

class PriceUpdateForm(FlaskForm):
    """價格更新表單"""
    gold = FloatField('黃金價格 (元/公克)', 
                     validators=[DataRequired(message='請輸入黃金價格'), 
                               NumberRange(min=0.01, max=100000, message='價格必須在0.01-100000之間')])
    
    silver = FloatField('白銀價格 (元/公克)', 
                       validators=[DataRequired(message='請輸入白銀價格'), 
                                 NumberRange(min=0.01, max=10000, message='價格必須在0.01-10000之間')])
    
    copper = FloatField('銅價格 (元/公斤)', 
                       validators=[DataRequired(message='請輸入銅價格'), 
                                 NumberRange(min=0.01, max=10000, message='價格必須在0.01-10000之間')])
    
    aluminum = FloatField('鋁價格 (元/公斤)', 
                         validators=[DataRequired(message='請輸入鋁價格'), 
                                   NumberRange(min=0.01, max=10000, message='價格必須在0.01-10000之間')])
    
    updated_by = StringField('更新者', 
                           validators=[Length(max=50, message='更新者名稱不能超過50個字符')],
                           default='管理員')
    
    submit = SubmitField('更新價格')

# 單位換算常數
UNIT_CONVERSIONS = {
    "oz_to_gram": 31.1035,  # 1盎司 = 31.1035公克
    "lb_to_kg": 0.453592,   # 1磅 = 0.453592公斤
    "tael_to_gram": 3.75,   # 1台錢 = 3.75公克 (台制)
    "gram_to_tael": 1/3.75  # 1公克 = 1/3.75台錢
}

def convert_oz_to_gram(oz_price):
    """將盎司價格轉換為公克價格"""
    return oz_price / UNIT_CONVERSIONS["oz_to_gram"]

def convert_lb_to_kg(lb_price):
    """將磅價格轉換為公斤價格"""
    return lb_price / UNIT_CONVERSIONS["lb_to_kg"]

def convert_gram_to_tael(gram_price):
    """將公克價格轉換為台錢價格"""
    return gram_price * UNIT_CONVERSIONS["tael_to_gram"]

def calculate_twd_high_low(base_data, metal, data_type):
    """計算台幣的最高最低點或前收盤價"""
    if not base_data or "metals" not in base_data:
        return None

    metal_data = base_data["metals"].get(metal, {})
    if not metal_data.get("auto_update", False):
        return None  # 只有啟用自動更新的項目才顯示最高最低點

    usd_twd_rate = base_data.get("currencies", {}).get("usd", {}).get("twd_rate", 31.5)

    if data_type == "high" and metal_data.get("daily_high"):
        # 將美元最高點轉換為台錢
        usd_high = metal_data["daily_high"]
        twd_high_per_oz = usd_high * usd_twd_rate
        return round(convert_gram_to_tael(convert_oz_to_gram(twd_high_per_oz)), 2)
    elif data_type == "low" and metal_data.get("daily_low"):
        # 將美元最低點轉換為台錢
        usd_low = metal_data["daily_low"]
        twd_low_per_oz = usd_low * usd_twd_rate
        return round(convert_gram_to_tael(convert_oz_to_gram(twd_low_per_oz)), 2)
    elif data_type == "previous" and metal_data.get("previous_close"):
        # 將美元前收盤價轉換為台錢
        usd_previous = metal_data["previous_close"]
        twd_previous_per_oz = usd_previous * usd_twd_rate
        return round(convert_gram_to_tael(convert_oz_to_gram(twd_previous_per_oz)), 2)

    return None

def calculate_metal_prices(base_data):
    """計算所有金屬的各種單位價格"""
    if not base_data or "metals" not in base_data:
        return {}

    calculated_prices = {}
    usd_twd_rate = base_data.get("currencies", {}).get("usd", {}).get("twd_rate", 31.5)

    # 處理貴金屬
    for metal, data in base_data["metals"].items():
        if not data.get("enabled", True):
            continue

        usd_per_oz = data["usd_per_oz"]
        buy_mult = data.get("buy_multiplier", 0.98)
        sell_mult = data.get("sell_multiplier", 1.02)
        auto_update = data.get("auto_update", False)

        # 美元/盎司價格（買賣倍數：不論 auto_update 都要進行）
        calculated_prices[f"{metal}_usd_buy"] = round(usd_per_oz * buy_mult, 2)
        calculated_prices[f"{metal}_usd_sell"] = round(usd_per_oz * sell_mult, 2)

        # 台幣/台錢價格
        if auto_update:
            # auto_update: true - 動態計算（跟隨匯率變動）
            twd_per_oz = usd_per_oz * usd_twd_rate
            twd_per_tael = convert_gram_to_tael(convert_oz_to_gram(twd_per_oz))
            calculated_prices[f"{metal}_twd_per_tael"] = round(twd_per_tael, 2)  # 基礎台錢價格
            calculated_prices[f"{metal}_twd_buy"] = round(twd_per_tael * buy_mult, 2)
            calculated_prices[f"{metal}_twd_sell"] = round(twd_per_tael * sell_mult, 2)

            # 台幣/公克價格
            twd_per_gram = convert_oz_to_gram(twd_per_oz)
            calculated_prices[f"{metal}_gram_buy"] = round(twd_per_gram * buy_mult, 2)
            calculated_prices[f"{metal}_gram_sell"] = round(twd_per_gram * sell_mult, 2)
        else:
            # auto_update: false - 使用固定的台幣價格（從 metals_twd 和 physical_metals 中獲取）
            # 這些價格是在 auto_update 關閉前保存的固定值，不會跟隨匯率變動

            # 檢查是否有對應的台錢價格
            metal_twd_key = f"{metal}_twd"
            if metal_twd_key in base_data.get("metals_twd", {}):
                fixed_twd_per_tael = base_data["metals_twd"][metal_twd_key]["twd_per_tael"]
                calculated_prices[f"{metal}_twd_per_tael"] = round(fixed_twd_per_tael, 2)
                calculated_prices[f"{metal}_twd_buy"] = round(fixed_twd_per_tael * buy_mult, 2)
                calculated_prices[f"{metal}_twd_sell"] = round(fixed_twd_per_tael * sell_mult, 2)

            # 檢查是否有對應的公克價格
            metal_gram_key = f"{metal}_gram"
            if metal_gram_key in base_data.get("physical_metals", {}):
                fixed_twd_per_gram = base_data["physical_metals"][metal_gram_key]["twd_per_gram"]
                calculated_prices[f"{metal}_gram_buy"] = round(fixed_twd_per_gram * buy_mult, 2)
                calculated_prices[f"{metal}_gram_sell"] = round(fixed_twd_per_gram * sell_mult, 2)

    # 處理台幣金屬（台錢）
    for metal, data in base_data.get("metals_twd", {}).items():
        if not data.get("enabled", True):
            continue

        twd_per_tael = data["twd_per_tael"]
        buy_mult = data.get("buy_multiplier", 0.98)
        sell_mult = data.get("sell_multiplier", 1.02)

        # 買賣倍數：不論 auto_update 都要進行
        calculated_prices[f"{metal}_buy"] = round(twd_per_tael * buy_mult, 2)
        calculated_prices[f"{metal}_sell"] = round(twd_per_tael * sell_mult, 2)

    # 處理實物交易金屬（公克）
    for metal, data in base_data.get("physical_metals", {}).items():
        if not data.get("enabled", True):
            continue

        twd_per_gram = data["twd_per_gram"]
        buy_mult = data.get("buy_multiplier", 0.98)
        sell_mult = data.get("sell_multiplier", 1.02)

        # 買賣倍數：不論 auto_update 都要進行
        calculated_prices[f"{metal}_buy"] = round(twd_per_gram * buy_mult, 2)
        calculated_prices[f"{metal}_sell"] = round(twd_per_gram * sell_mult, 2)

    # 處理其他材料（公斤）
    for material, data in base_data.get("other_materials", {}).items():
        if not data.get("enabled", True):
            continue

        twd_per_kg = data["twd_per_kg"]
        buy_mult = data.get("buy_multiplier", 0.85)
        sell_mult = data.get("sell_multiplier", 1.15)

        # 買賣倍數：不論 auto_update 都要進行
        calculated_prices[f"{material}_buy"] = round(twd_per_kg * buy_mult, 2)
        calculated_prices[f"{material}_sell"] = round(twd_per_kg * sell_mult, 2)

    # 保持向後兼容：處理舊的 industrial_metals
    for metal, data in base_data.get("industrial_metals", {}).items():
        if not data.get("enabled", True):
            continue

        usd_per_lb = data["usd_per_lb"]
        buy_mult = data.get("buy_multiplier", 0.95)
        sell_mult = data.get("sell_multiplier", 1.05)
        auto_update = data.get("auto_update", False)

        if auto_update:
            # auto_update: true - 動態計算（跟隨匯率變動）
            twd_per_kg = convert_lb_to_kg(usd_per_lb) * usd_twd_rate
            calculated_prices[f"{metal}_kg_buy"] = round(twd_per_kg * buy_mult, 2)
            calculated_prices[f"{metal}_kg_sell"] = round(twd_per_kg * sell_mult, 2)
        else:
            # auto_update: false - 使用固定的台幣價格（從 other_materials 中獲取）
            # 檢查是否有對應的公斤價格
            material_key = f"{metal}_kg"
            if material_key in base_data.get("other_materials", {}):
                fixed_twd_per_kg = base_data["other_materials"][material_key]["twd_per_kg"]
                calculated_prices[f"{metal}_kg_buy"] = round(fixed_twd_per_kg * buy_mult, 2)
                calculated_prices[f"{metal}_kg_sell"] = round(fixed_twd_per_kg * sell_mult, 2)

    # 處理匯率
    for currency, data in base_data.get("currencies", {}).items():
        if not data.get("enabled", True):
            continue

        twd_rate = data["twd_rate"]
        buy_mult = data.get("buy_multiplier", 0.998)
        sell_mult = data.get("sell_multiplier", 1.002)

        # 買賣倍數：不論 auto_update 都要進行
        calculated_prices[f"{currency}_buy"] = round(twd_rate * buy_mult, 4)
        calculated_prices[f"{currency}_sell"] = round(twd_rate * sell_mult, 4)

    return calculated_prices

def load_prices():
    """載入價格數據"""
    try:
        if os.path.exists(PRICES_FILE):
            with open(PRICES_FILE, 'r', encoding='utf-8') as f:
                data = json.load(f)

                # 檢查是否為舊版本格式，如果是則升級到階段四
                if "version" not in data or data.get("version") not in ["4.0"]:
                    logger.info(f"檢測到舊版本價格數據 v{data.get('version', '1.0')}，正在升級到 v4.0...")
                    # 保留舊的基本價格，但使用新結構
                    upgraded_data = DEFAULT_PRICES.copy()

                    # 遷移舊數據
                    if "gold" in data:
                        upgraded_data["metals"]["gold"]["usd_per_oz"] = data["gold"] / 31.5 * 31.1035
                    if "silver" in data:
                        upgraded_data["metals"]["silver"]["usd_per_oz"] = data["silver"] / 31.5 * 31.1035
                    if "copper" in data:
                        upgraded_data["industrial_metals"]["copper"]["usd_per_lb"] = data["copper"] / 31.5 * 0.453592
                    if "aluminum" in data:
                        upgraded_data["industrial_metals"]["aluminum"]["usd_per_lb"] = data["aluminum"] / 31.5 * 0.453592

                    # 保存升級後的數據
                    save_prices(upgraded_data)
                    return upgraded_data

                # logger.info(f"成功載入價格數據 v{data.get('version', '1.0')}")
                return data
        else:
            logger.warning("價格文件不存在，使用預設價格")
            return DEFAULT_PRICES.copy()
    except Exception as e:
        logger.error(f"載入價格數據失敗: {e}")
        return DEFAULT_PRICES.copy()

def save_prices(prices_data):
    """保存價格數據"""
    try:
        with open(PRICES_FILE, 'w', encoding='utf-8') as f:
            json.dump(prices_data, f, ensure_ascii=False, indent=2)
        # logger.info(f"成功保存價格數據: {prices_data}")
        return True
    except Exception as e:
        logger.error(f"保存價格數據失敗: {e}")
        return False

def load_news():
    """載入新聞數據"""
    try:
        if os.path.exists(NEWS_FILE):
            with open(NEWS_FILE, 'r', encoding='utf-8') as f:
                data = json.load(f)
                # logger.info(f"成功載入新聞數據")
                return data
        else:
            logger.warning("新聞文件不存在，使用預設新聞")
            return DEFAULT_NEWS.copy()
    except Exception as e:
        logger.error(f"載入新聞數據失敗: {e}")
        return DEFAULT_NEWS.copy()

def save_news(news_data):
    """保存新聞數據"""
    try:
        with open(NEWS_FILE, 'w', encoding='utf-8') as f:
            json.dump(news_data, f, ensure_ascii=False, indent=2)
        # logger.info(f"成功保存新聞數據")
        return True
    except Exception as e:
        logger.error(f"保存新聞數據失敗: {e}")
        return False

def load_booking():
    """載入回收預約數據"""
    try:
        if os.path.exists(BOOKING_FILE):
            with open(BOOKING_FILE, 'r', encoding='utf-8') as f:
                data = json.load(f)
                return data
        else:
            return DEFAULT_BOOKING.copy()
    except Exception as e:
        logger.error(f"載入回收預約數據失敗: {e}")
        return DEFAULT_BOOKING.copy()

def save_booking(booking_data):
    """保存回收預約數據"""
    try:
        with open(BOOKING_FILE, 'w', encoding='utf-8') as f:
            json.dump(booking_data, f, ensure_ascii=False, indent=2)
        logger.info(f"成功保存回收預約數據")
        return True
    except Exception as e:
        logger.error(f"保存回收預約數據失敗: {e}")
        return False

def load_contact():
    """載入聯絡我們數據"""
    try:
        if os.path.exists(CONTACT_FILE):
            with open(CONTACT_FILE, 'r', encoding='utf-8') as f:
                data = json.load(f)
                return data
        else:
            return DEFAULT_CONTACT.copy()
    except Exception as e:
        logger.error(f"載入聯絡我們數據失敗: {e}")
        return DEFAULT_CONTACT.copy()

def save_contact(contact_data):
    """保存聯絡我們數據"""
    try:
        with open(CONTACT_FILE, 'w', encoding='utf-8') as f:
            json.dump(contact_data, f, ensure_ascii=False, indent=2)
        # logger.info(f"成功保存聯絡我們數據")
        return True
    except Exception as e:
        logger.error(f"保存聯絡我們數據失敗: {e}")
        return False

def load_settings():
    """載入系統設定"""
    try:
        if os.path.exists(SETTINGS_FILE):
            with open(SETTINGS_FILE, 'r', encoding='utf-8') as f:
                data = json.load(f)

                # 向後兼容：如果沒有 TwelveData 設定，則添加預設值
                if 'twelvedata' not in data:
                    data['twelvedata'] = DEFAULT_SETTINGS['twelvedata'].copy()
                    # 自動保存更新後的設定
                    save_settings(data)
                    # logger.info("已添加 TwelveData 預設設定")
                else:
                    # 檢查是否缺少新的設定項目
                    updated = False
                    default_twelvedata = DEFAULT_SETTINGS['twelvedata']

                    for key, value in default_twelvedata.items():
                        if key not in data['twelvedata']:
                            data['twelvedata'][key] = value
                            updated = True

                    if updated:
                        save_settings(data)
                        # logger.info("已更新 TwelveData 設定項目")

                return data
        else:
            return DEFAULT_SETTINGS.copy()
    except Exception as e:
        logger.error(f"載入系統設定失敗: {e}")
        return DEFAULT_SETTINGS.copy()

def save_settings(settings_data):
    """保存系統設定"""
    try:
        with open(SETTINGS_FILE, 'w', encoding='utf-8') as f:
            json.dump(settings_data, f, ensure_ascii=False, indent=2)
        # logger.info(f"成功保存系統設定")
        return True
    except Exception as e:
        logger.error(f"保存系統設定失敗: {e}")
        return False

def get_real_ip():
    """獲取訪客真實 IP（考慮 Docker + Nginx + CDN 環境）"""
    # 按優先級檢查各種 IP 頭
    ip_headers = [
        'X-Forwarded-For',      # 最常用的代理頭
        'X-Real-IP',            # Nginx 常用
        'CF-Connecting-IP',     # Cloudflare
        'X-Client-IP',          # 其他代理
        'X-Forwarded',          # 舊版本
        'Forwarded-For',        # 舊版本
        'Forwarded'             # RFC 7239
    ]

    for header in ip_headers:
        ip = request.headers.get(header)
        if ip:
            # X-Forwarded-For 可能包含多個 IP，取第一個
            if ',' in ip:
                ip = ip.split(',')[0].strip()
            # 驗證 IP 格式（簡單驗證）
            if ip and ip != 'unknown':
                return ip

    # 如果都沒有，使用 Flask 的 remote_addr
    return request.remote_addr or '127.0.0.1'

def generate_captcha(difficulty='easy'):
    """生成數學驗證碼"""
    if difficulty == 'easy':
        # 簡單：十位數加減，結果在個位數
        a = random.randint(1, 9)
        b = random.randint(1, min(9, a))  # 確保減法結果為正
        operation = random.choice(['+', '-'])
        if operation == '+':
            # 確保結果在個位數
            if a + b > 9:
                a, b = b, a  # 交換使結果更小
                if a + b > 9:
                    b = 9 - a
        else:  # 減法
            if a < b:
                a, b = b, a  # 確保 a >= b

        question = f"{a} {operation} {b}"
        answer = a + b if operation == '+' else a - b

    elif difficulty == 'medium':
        # 中等：十位數加減，結果在十位數
        a = random.randint(10, 50)
        b = random.randint(10, 50)
        operation = random.choice(['+', '-'])
        if operation == '-' and a < b:
            a, b = b, a  # 確保減法結果為正

        question = f"{a} {operation} {b}"
        answer = a + b if operation == '+' else a - b

    else:  # hard
        # 困難：乘法，結果可能在十位數或百位數
        a = random.randint(2, 15)
        b = random.randint(2, 15)
        question = f"{a} × {b}"
        answer = a * b

    return question, answer

def generate_captcha_image(question):
    """生成驗證碼圖片"""
    try:
        # 圖片尺寸
        width, height = 200, 80

        # 隨機背景顏色（淺色）
        bg_color = (random.randint(240, 255), random.randint(240, 255), random.randint(240, 255))

        # 創建圖片
        image = Image.new('RGB', (width, height), color=bg_color)
        draw = ImageDraw.Draw(image)

        # 嘗試載入字體，如果失敗則使用預設字體
        font_size = random.randint(28, 36)  # 隨機字體大小
        try:
            # 嘗試使用系統字體
            font = ImageFont.truetype('/usr/share/fonts/truetype/dejavu/DejaVuSans-Bold.ttf', font_size)
        except:
            try:
                # 嘗試其他常見字體路徑
                font = ImageFont.truetype('/System/Library/Fonts/Arial.ttf', font_size)
            except:
                try:
                    # 嘗試 Ubuntu 字體
                    font = ImageFont.truetype('/usr/share/fonts/truetype/liberation/LiberationSans-Bold.ttf', font_size)
                except:
                    # 使用預設字體
                    font = ImageFont.load_default()

        # 添加背景噪點
        for _ in range(random.randint(80, 120)):
            x = random.randint(0, width-1)
            y = random.randint(0, height-1)
            noise_color = (random.randint(180, 220), random.randint(180, 220), random.randint(180, 220))
            draw.point((x, y), fill=noise_color)

        # 添加干擾線
        for _ in range(random.randint(3, 7)):
            start = (random.randint(0, width), random.randint(0, height))
            end = (random.randint(0, width), random.randint(0, height))
            line_color = (random.randint(120, 180), random.randint(120, 180), random.randint(120, 180))
            draw.line([start, end], fill=line_color, width=random.randint(1, 3))

        # 添加一些橢圓干擾
        for _ in range(random.randint(2, 4)):
            x1 = random.randint(0, width//2)
            y1 = random.randint(0, height//2)
            x2 = x1 + random.randint(10, 30)
            y2 = y1 + random.randint(10, 30)
            ellipse_color = (random.randint(200, 230), random.randint(200, 230), random.randint(200, 230))
            draw.ellipse([x1, y1, x2, y2], outline=ellipse_color)

        # 計算文字位置（稍微偏移中心）
        bbox = draw.textbbox((0, 0), question, font=font)
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]
        x = (width - text_width) // 2 + random.randint(-5, 5)
        y = (height - text_height) // 2 + random.randint(-3, 3)

        # 繪製文字（添加陰影效果）
        shadow_offset = random.randint(1, 3)
        shadow_color = (random.randint(100, 150), random.randint(100, 150), random.randint(100, 150))
        text_color = (random.randint(0, 80), random.randint(0, 80), random.randint(0, 80))

        # 陰影
        draw.text((x + shadow_offset, y + shadow_offset), question, font=font, fill=shadow_color)
        # 主文字
        draw.text((x, y), question, font=font, fill=text_color)

        # 轉換為 base64
        buffer = io.BytesIO()
        image.save(buffer, format='PNG')
        buffer.seek(0)
        image_base64 = base64.b64encode(buffer.getvalue()).decode()

        return f"data:image/png;base64,{image_base64}"

    except Exception as e:
        logger.error(f"生成驗證碼圖片失敗: {e}")
        # 如果圖片生成失敗，返回 None，前端會顯示文字版本
        return None

def check_rate_limit(form_type):
    """檢查提交頻率限制"""
    settings = load_settings()
    if not settings['rateLimit']['enabled']:
        return True, ""

    max_submissions = settings['rateLimit']['maxSubmissions']
    time_window = settings['rateLimit']['timeWindow']
    current_time = time.time()

    # 獲取真實 IP
    client_ip = get_real_ip()

    # 初始化 session 數據
    if 'submissions' not in session:
        session['submissions'] = {}
    if 'ip_submissions' not in session:
        session['ip_submissions'] = {}

    # 清理過期記錄
    session_key = f"{form_type}_submissions"
    ip_key = f"{client_ip}_{form_type}"

    # 檢查 session 限制
    if session_key in session['submissions']:
        session['submissions'][session_key] = [
            timestamp for timestamp in session['submissions'][session_key]
            if current_time - timestamp < time_window
        ]
        if len(session['submissions'][session_key]) >= max_submissions:
            return False, f"您在4小時內已提交{max_submissions}次{form_type}表單，請稍後再試"

    # 檢查 IP 限制
    if ip_key in session['ip_submissions']:
        session['ip_submissions'][ip_key] = [
            timestamp for timestamp in session['ip_submissions'][ip_key]
            if current_time - timestamp < time_window
        ]
        if len(session['ip_submissions'][ip_key]) >= max_submissions:
            return False, f"此IP在4小時內已提交{max_submissions}次{form_type}表單，請稍後再試"

    return True, ""

def record_submission(form_type):
    """記錄提交"""
    current_time = time.time()
    client_ip = get_real_ip()

    # 初始化 session 數據
    if 'submissions' not in session:
        session['submissions'] = {}
    if 'ip_submissions' not in session:
        session['ip_submissions'] = {}

    session_key = f"{form_type}_submissions"
    ip_key = f"{client_ip}_{form_type}"

    # 記錄 session 提交
    if session_key not in session['submissions']:
        session['submissions'][session_key] = []
    session['submissions'][session_key].append(current_time)

    # 記錄 IP 提交
    if ip_key not in session['ip_submissions']:
        session['ip_submissions'][ip_key] = []
    session['ip_submissions'][ip_key].append(current_time)

    session.permanent = True

# 階段四：初始化 TwelveData 整合實例
def init_twelvedata():
    """初始化 TwelveData 整合"""
    try:
        prices_data = load_prices()
        config = prices_data.get("twelvedata_config", {})

        # 優先從環境變數讀取 API Key，如果沒有則從配置文件讀取
        api_key = os.environ.get('TWELVEDATA_API_KEY', '').strip()
        if not api_key:
            api_key = config.get("api_key", "")

        twelvedata = TwelveDataIntegration(api_key, config)
        logger.info(f"TwelveData 整合初始化完成，API Key: {'已設定' if api_key else '未設定'}")
        return twelvedata
    except Exception as e:
        logger.error(f"TwelveData 整合初始化失敗: {e}")
        return TwelveDataIntegration()

# 全域 TwelveData 實例
twelvedata_instance = init_twelvedata()

@app.route('/')
def index():
    """首頁 - 靜態網站首頁"""
    return render_template('index.html')

@app.route('/products')
def products():
    """產品介紹頁面"""
    return render_template('products.html')

@app.route('/market-data')
def market_data():
    """金屬價格頁面"""
    return render_template('market-data.html')

@app.route('/booking')
def booking():
    """回收預約頁面"""
    return render_template('booking.html')

@app.route('/contact')
def contact():
    """聯絡我們頁面"""
    return render_template('contact.html')

@app.route('/admin-secure')
def admin_secure():
    """管理員登入頁面"""
    return render_template('admin_secure.html')

@app.route('/admin-secure.html')
def admin_secure_html():
    """兼容原始 admin-secure.html 路徑"""
    return render_template('admin_secure.html')

@app.route('/admin-panel')
def admin_panel():
    """管理員控制台頁面"""
    return render_template('admin_panel.html')

@app.route('/admin-panel.html')
def admin_panel_html():
    """兼容 admin-panel.html 路徑"""
    return render_template('admin_panel.html')

# @app.route('/test-currency-fix')
# def test_currency_fix():
#     """貨幣修正測試頁面"""
#     with open('test_currency_fix.html', 'r', encoding='utf-8') as f:
#         return f.read()

# 兼容 .html 後綴的路由
@app.route('/index.html')
def index_html():
    """兼容原始 index.html 路徑"""
    return render_template('index.html')

@app.route('/products.html')
def products_html():
    """兼容原始 products.html 路徑"""
    return render_template('products.html')

@app.route('/market-data.html')
def market_data_html():
    """兼容原始 market-data.html 路徑"""
    return render_template('market-data.html')

@app.route('/booking.html')
def booking_html():
    """兼容原始 booking.html 路徑"""
    return render_template('booking.html')

@app.route('/contact.html')
def contact_html():
    """兼容原始 contact.html 路徑"""
    return render_template('contact.html')

@app.route('/api/login', methods=['POST'])
def admin_login():
    """管理員登入驗證"""
    data = request.get_json()
    token = data.get('token', '').strip()
    admin_token = os.environ.get('ADMIN_TOKEN', '')

    if token and token == admin_token:
        return jsonify({'success': True, 'message': '驗證成功'})
    else:
        return jsonify({'success': False, 'message': '無效的 Token'}), 401



@app.route('/api/admin/current-prices', methods=['GET'])
def get_admin_prices():
    """獲取當前價格（需要認證）"""
    # 檢查 token
    auth_header = request.headers.get('Authorization')
    if not auth_header or not auth_header.startswith('Bearer '):
        return jsonify({'error': '需要認證'}), 401

    token = auth_header.split(' ')[1]
    admin_token = os.environ.get('ADMIN_TOKEN', '')

    if token != admin_token:
        return jsonify({'error': '無效的 Token'}), 401

    # 返回價格數據
    current_prices = load_prices()
    return jsonify(current_prices)

# @app.route('/api/admin/update-prices', methods=['POST'])
# def update_prices():
#     """更新價格 - API 方式（需要認證）"""
#     # 檢查 token
#     auth_header = request.headers.get('Authorization')
#     if not auth_header or not auth_header.startswith('Bearer '):
#         return jsonify({'error': '需要認證'}), 401

#     token = auth_header.split(' ')[1]
#     admin_token = os.environ.get('ADMIN_TOKEN', '')

#     if token != admin_token:
#         return jsonify({'error': '無效的 Token'}), 401

#     # 獲取數據
#     data = request.get_json()

#     try:
#         # 驗證數據
#         required_fields = ['gold', 'silver', 'copper', 'aluminum']
#         for field in required_fields:
#             if field not in data:
#                 return jsonify({'error': f'缺少必要字段: {field}'}), 400

#             try:
#                 float(data[field])
#             except (ValueError, TypeError):
#                 return jsonify({'error': f'字段 {field} 必須是有效數字'}), 400

#         # 構建新的價格數據
#         new_prices = {
#             "gold": float(data['gold']),
#             "silver": float(data['silver']),
#             "copper": float(data['copper']),
#             "aluminum": float(data['aluminum']),
#             "lastUpdate": datetime.now(tz).isoformat(),
#             "updateTime": datetime.now(tz).strftime('%Y年%m月%d日 %H:%M:%S'),
#             "updatedBy": data.get('updatedBy', '管理員'),
#             "source": "Flask API - 管理員更新"
#         }

#         # 保存價格數據
#         if save_prices(new_prices):
#             logger.info(f"管理員價格更新成功: {new_prices}")
#             return jsonify({
#                 'success': True,
#                 'message': '價格更新成功',
#                 'data': new_prices
#             }), 200
#         else:
#             return jsonify({'error': '價格保存失敗'}), 500

#     except Exception as e:
#         logger.error(f"管理員價格更新失敗: {e}")
#         return jsonify({'error': str(e)}), 500

@app.route('/api/admin/current-news', methods=['GET'])
def get_admin_news():
    """獲取當前新聞（需要認證）"""
    # 檢查 token
    auth_header = request.headers.get('Authorization')
    if not auth_header or not auth_header.startswith('Bearer '):
        return jsonify({'error': '需要認證'}), 401

    token = auth_header.split(' ')[1]
    admin_token = os.environ.get('ADMIN_TOKEN', '')

    if token != admin_token:
        return jsonify({'error': '無效的 Token'}), 401

    # 返回新聞數據
    current_news = load_news()
    return jsonify(current_news)

@app.route('/api/admin/update-news', methods=['POST'])
def update_news():
    """更新新聞 - API 方式（需要認證）"""
    # 檢查 token
    auth_header = request.headers.get('Authorization')
    if not auth_header or not auth_header.startswith('Bearer '):
        return jsonify({'error': '需要認證'}), 401

    token = auth_header.split(' ')[1]
    admin_token = os.environ.get('ADMIN_TOKEN', '')

    if token != admin_token:
        return jsonify({'error': '無效的 Token'}), 401

    # 獲取數據
    data = request.get_json()

    try:
        # 驗證數據
        if 'news' not in data or not isinstance(data['news'], list):
            return jsonify({'error': '新聞數據格式錯誤'}), 400

        # 驗證每條新聞的必要字段
        for i, news_item in enumerate(data['news']):
            required_fields = ['title', 'content', 'date', 'time', 'category']
            for field in required_fields:
                if field not in news_item:
                    return jsonify({'error': f'新聞 {i+1} 缺少必要字段: {field}'}), 400

        # 對新聞按照日期和時間排序（最新的在前面）
        sorted_news = sorted(data['news'], key=lambda x: f"{x['date']} {x['time']}", reverse=True)

        # 構建新的新聞數據
        new_news = {
            "news": sorted_news,
            "lastUpdate": datetime.now(tz).isoformat(),
            "updateTime": datetime.now(tz).strftime('%Y年%m月%d日 %H:%M:%S'),
            "updatedBy": data.get('updatedBy', '管理員'),
            "source": "Flask API - 管理員更新"
        }

        # 保存新聞數據
        if save_news(new_news):
            # logger.info(f"管理員新聞更新成功")
            return jsonify({
                'success': True,
                'message': '新聞更新成功',
                'data': new_news
            }), 200
        else:
            return jsonify({'error': '新聞保存失敗'}), 500

    except Exception as e:
        logger.error(f"管理員新聞更新失敗: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/prices', methods=['GET'])
def api_get_prices():
    """API端點 - 獲取價格數據（階段四：支援完整前端數據結構）"""
    try:
        # 獲取緩存時間戳參數（用於前端緩存控制）
        cache_timestamp = request.args.get('t', '')
        # logger.info(f"API請求 - 緩存時間戳: {cache_timestamp}")

        # 更新 TwelveData 活動時間
        global twelvedata_instance
        twelvedata_instance.update_activity()

        # 載入當前價格數據
        base_prices = load_prices()

        # 計算所有衍生價格
        calculated_prices = calculate_metal_prices(base_prices)

        # 構建階段四的完整回應數據
        response_data = {
            # 保持向後兼容的舊格式
            "gold": calculated_prices.get("gold_gram_buy", 2045.80),
            "silver": calculated_prices.get("silver_gram_buy", 24.50),
            "copper": calculated_prices.get("cobalt_kg_buy", 1059.32),  # 修正：使用鈷代替銅
            "aluminum": calculated_prices.get("tin_kg_buy", 970.48),    # 修正：使用錫代替鋁

            # 階段四：完整的前端數據結構
            "metals": {
                # 商品/美元表格（左側 2/3 區塊）
                "usd_table": {
                    "gold": {
                        "usd_per_oz": base_prices.get("metals", {}).get("gold", {}).get("usd_per_oz", 2045.80),
                        "buy_usd_per_oz": calculated_prices.get("gold_usd_buy", 2045.80),
                        "sell_usd_per_oz": calculated_prices.get("gold_usd_sell", 2045.80),
                        "enabled": base_prices.get("metals", {}).get("gold", {}).get("enabled", True),
                        "auto_update": base_prices.get("metals", {}).get("gold", {}).get("auto_update", False),
                        "daily_high_usd": base_prices.get("metals", {}).get("gold", {}).get("daily_high"),
                        "daily_low_usd": base_prices.get("metals", {}).get("gold", {}).get("daily_low"),
                        "previous_close_usd": base_prices.get("metals", {}).get("gold", {}).get("previous_close")
                    },
                    "silver": {
                        "usd_per_oz": base_prices.get("metals", {}).get("silver", {}).get("usd_per_oz", 24.50),
                        "buy_usd_per_oz": calculated_prices.get("silver_usd_buy", 24.50),
                        "sell_usd_per_oz": calculated_prices.get("silver_usd_sell", 24.50),
                        "enabled": base_prices.get("metals", {}).get("silver", {}).get("enabled", True),
                        "auto_update": base_prices.get("metals", {}).get("silver", {}).get("auto_update", False),
                        "daily_high_usd": base_prices.get("metals", {}).get("silver", {}).get("daily_high"),
                        "daily_low_usd": base_prices.get("metals", {}).get("silver", {}).get("daily_low"),
                        "previous_close_usd": base_prices.get("metals", {}).get("silver", {}).get("previous_close")
                    },
                    "palladium": {
                        "usd_per_oz": base_prices.get("metals", {}).get("palladium", {}).get("usd_per_oz", 1050.0),
                        "buy_usd_per_oz": calculated_prices.get("palladium_usd_buy", 1050.0),
                        "sell_usd_per_oz": calculated_prices.get("palladium_usd_sell", 1050.0),
                        "enabled": base_prices.get("metals", {}).get("palladium", {}).get("enabled", True),
                        "auto_update": base_prices.get("metals", {}).get("palladium", {}).get("auto_update", False),
                        "daily_high_usd": base_prices.get("metals", {}).get("palladium", {}).get("daily_high"),
                        "daily_low_usd": base_prices.get("metals", {}).get("palladium", {}).get("daily_low"),
                        "previous_close_usd": base_prices.get("metals", {}).get("palladium", {}).get("previous_close")
                    },
                    "platinum": {
                        "usd_per_oz": base_prices.get("metals", {}).get("platinum", {}).get("usd_per_oz", 950.0),
                        "buy_usd_per_oz": calculated_prices.get("platinum_usd_buy", 950.0),
                        "sell_usd_per_oz": calculated_prices.get("platinum_usd_sell", 950.0),
                        "enabled": base_prices.get("metals", {}).get("platinum", {}).get("enabled", True),
                        "auto_update": base_prices.get("metals", {}).get("platinum", {}).get("auto_update", False),
                        "daily_high_usd": base_prices.get("metals", {}).get("platinum", {}).get("daily_high"),
                        "daily_low_usd": base_prices.get("metals", {}).get("platinum", {}).get("daily_low"),
                        "previous_close_usd": base_prices.get("metals", {}).get("platinum", {}).get("previous_close")
                    }
                },
                # 商品/台幣表格（左側 2/3 區塊）
                "twd_table": {
                    "gold": {
                        "twd_per_tael": calculated_prices.get("gold_twd_per_tael", 75000),
                        "buy_twd_per_tael": calculated_prices.get("gold_twd_buy", 75000),
                        "sell_twd_per_tael": calculated_prices.get("gold_twd_sell", 75000),
                        "enabled": base_prices.get("metals", {}).get("gold", {}).get("enabled", True),
                        "auto_update": base_prices.get("metals", {}).get("gold", {}).get("auto_update", False),
                        "daily_high_twd": calculate_twd_high_low(base_prices, "gold", "high"),
                        "daily_low_twd": calculate_twd_high_low(base_prices, "gold", "low"),
                        "previous_close_twd": calculate_twd_high_low(base_prices, "gold", "previous")
                    },
                    "silver": {
                        "twd_per_tael": calculated_prices.get("silver_twd_per_tael", 900),
                        "buy_twd_per_tael": calculated_prices.get("silver_twd_buy", 900),
                        "sell_twd_per_tael": calculated_prices.get("silver_twd_sell", 900),
                        "enabled": base_prices.get("metals", {}).get("silver", {}).get("enabled", True),
                        "auto_update": base_prices.get("metals", {}).get("silver", {}).get("auto_update", False),
                        "daily_high_twd": calculate_twd_high_low(base_prices, "silver", "high"),
                        "daily_low_twd": calculate_twd_high_low(base_prices, "silver", "low"),
                        "previous_close_twd": calculate_twd_high_low(base_prices, "silver", "previous")
                    },
                    "palladium": {
                        "twd_per_tael": calculated_prices.get("palladium_twd_per_tael", 38500),
                        "buy_twd_per_tael": calculated_prices.get("palladium_twd_buy", 38500),
                        "sell_twd_per_tael": calculated_prices.get("palladium_twd_sell", 38500),
                        "enabled": base_prices.get("metals", {}).get("palladium", {}).get("enabled", True),
                        "auto_update": base_prices.get("metals", {}).get("palladium", {}).get("auto_update", False),
                        "daily_high_twd": calculate_twd_high_low(base_prices, "palladium", "high"),
                        "daily_low_twd": calculate_twd_high_low(base_prices, "palladium", "low"),
                        "previous_close_twd": calculate_twd_high_low(base_prices, "palladium", "previous")
                    },
                    "platinum": {
                        "twd_per_tael": calculated_prices.get("platinum_twd_per_tael", 34800),
                        "buy_twd_per_tael": calculated_prices.get("platinum_twd_buy", 34800),
                        "sell_twd_per_tael": calculated_prices.get("platinum_twd_sell", 34800),
                        "enabled": base_prices.get("metals", {}).get("platinum", {}).get("enabled", True),
                        "auto_update": base_prices.get("metals", {}).get("platinum", {}).get("auto_update", False),
                        "daily_high_twd": calculate_twd_high_low(base_prices, "platinum", "high"),
                        "daily_low_twd": calculate_twd_high_low(base_prices, "platinum", "low"),
                        "previous_close_twd": calculate_twd_high_low(base_prices, "platinum", "previous")
                    }
                },
                # 實物交易表格（右側 1/3 區塊，8種金屬）
                "physical_table": {
                    "gold": {
                        "buy_twd_per_gram": calculated_prices.get("gold_gram_buy", 2000),
                        "sell_twd_per_gram": calculated_prices.get("gold_gram_sell", 2000),
                        "enabled": base_prices.get("metals", {}).get("gold", {}).get("enabled", True)
                    },
                    "silver": {
                        "buy_twd_per_gram": calculated_prices.get("silver_gram_buy", 24),
                        "sell_twd_per_gram": calculated_prices.get("silver_gram_sell", 24),
                        "enabled": base_prices.get("metals", {}).get("silver", {}).get("enabled", True)
                    },
                    "palladium": {
                        "buy_twd_per_gram": calculated_prices.get("palladium_gram_buy", 1030),
                        "sell_twd_per_gram": calculated_prices.get("palladium_gram_sell", 1030),
                        "enabled": base_prices.get("metals", {}).get("palladium", {}).get("enabled", True)
                    },
                    "platinum": {
                        "buy_twd_per_gram": calculated_prices.get("platinum_gram_buy", 930),
                        "sell_twd_per_gram": calculated_prices.get("platinum_gram_sell", 930),
                        "enabled": base_prices.get("metals", {}).get("platinum", {}).get("enabled", True)
                    },
                    "rhodium": {
                        "buy_twd_per_gram": calculated_prices.get("rhodium_gram_buy", 4400),
                        "sell_twd_per_gram": calculated_prices.get("rhodium_gram_sell", 4400),
                        "enabled": base_prices.get("metals", {}).get("rhodium", {}).get("enabled", True)
                    },
                    "ruthenium": {
                        "buy_twd_per_gram": calculated_prices.get("ruthenium_gram_buy", 440),
                        "sell_twd_per_gram": calculated_prices.get("ruthenium_gram_sell", 440),
                        "enabled": base_prices.get("metals", {}).get("ruthenium", {}).get("enabled", True)
                    },
                    "iridium": {
                        "buy_twd_per_gram": calculated_prices.get("iridium_gram_buy", 1760),
                        "sell_twd_per_gram": calculated_prices.get("iridium_gram_sell", 1760),
                        "enabled": base_prices.get("metals", {}).get("iridium", {}).get("enabled", True)
                    },
                    "osmium": {
                        "buy_twd_per_gram": calculated_prices.get("osmium_gram_buy", 390),
                        "sell_twd_per_gram": calculated_prices.get("osmium_gram_sell", 390),
                        "enabled": base_prices.get("metals", {}).get("osmium", {}).get("enabled", True)
                    }
                }
            },
            # 其他材料表格（右側 1/3 區塊，公斤報價）
            "other_materials": {
                "cobalt": {
                    "buy_twd_per_kg": calculated_prices.get("cobalt_kg_buy", 1050),
                    "sell_twd_per_kg": calculated_prices.get("cobalt_kg_sell", 1050),
                    "enabled": base_prices.get("other_materials", {}).get("cobalt_kg", {}).get("enabled", True)
                },
                "tin": {
                    "buy_twd_per_kg": calculated_prices.get("tin_kg_buy", 960),
                    "sell_twd_per_kg": calculated_prices.get("tin_kg_sell", 960),
                    "enabled": base_prices.get("other_materials", {}).get("tin_kg", {}).get("enabled", True)
                }
            },
            # 貨幣項目表格（右側 1/3 區塊）
            "currencies": {
                "usd": {
                    "twd_rate": base_prices.get("currencies", {}).get("usd", {}).get("twd_rate", 31.0),
                    "buy_multiplier": base_prices.get("currencies", {}).get("usd", {}).get("buy_multiplier", 0.998),
                    "sell_multiplier": base_prices.get("currencies", {}).get("usd", {}).get("sell_multiplier", 1.002),
                    "buy_rate": calculated_prices.get("usd_buy", 31.0),
                    "sell_rate": calculated_prices.get("usd_sell", 31.0),
                    "enabled": base_prices.get("currencies", {}).get("usd", {}).get("enabled", True)
                },
                "cny": {
                    "twd_rate": base_prices.get("currencies", {}).get("cny", {}).get("twd_rate", 4.5),
                    "buy_multiplier": base_prices.get("currencies", {}).get("cny", {}).get("buy_multiplier", 0.995),
                    "sell_multiplier": base_prices.get("currencies", {}).get("cny", {}).get("sell_multiplier", 1.005),
                    "buy_rate": calculated_prices.get("cny_buy", 4.5),
                    "sell_rate": calculated_prices.get("cny_sell", 4.5),
                    "enabled": base_prices.get("currencies", {}).get("cny", {}).get("enabled", True)
                },
                "eur": {
                    "twd_rate": base_prices.get("currencies", {}).get("eur", {}).get("twd_rate", 35.0),
                    "buy_multiplier": base_prices.get("currencies", {}).get("eur", {}).get("buy_multiplier", 0.997),
                    "sell_multiplier": base_prices.get("currencies", {}).get("eur", {}).get("sell_multiplier", 1.003),
                    "buy_rate": calculated_prices.get("eur_buy", 35.0),
                    "sell_rate": calculated_prices.get("eur_sell", 35.0),
                    "enabled": base_prices.get("currencies", {}).get("eur", {}).get("enabled", True)
                }
            },

            # 完整的計算價格數據（供管理員使用）
            "calculated_prices": calculated_prices,
            "base_data": base_prices,

            # API 資訊
            'fetchedAt': datetime.now(tz).isoformat(),
            'fetchedBy': 'Flask API v4.0',
            'cacheTimestamp': cache_timestamp,
            'lastUpdate': base_prices.get('lastUpdate'),
            'updateTime': base_prices.get('updateTime'),
            'updatedBy': base_prices.get('updatedBy'),
            'source': base_prices.get('source'),
            'version': base_prices.get('version', '4.0'),
            'twelvedata_status': twelvedata_instance.get_quota_status() if twelvedata_instance.api_key else None
        }

        return jsonify(response_data), 200

    except Exception as e:
        logger.error(f"API獲取價格失敗: {e}")

        # 返回錯誤時的預設價格
        error_prices = DEFAULT_PRICES.copy()
        error_calculated = calculate_metal_prices(error_prices)

        error_response = {
            "gold": error_calculated.get("gold_gram_buy", 2045.80),
            "silver": error_calculated.get("silver_gram_buy", 24.50),
            "copper": error_calculated.get("cobalt_kg_buy", 1059.32),
            "aluminum": error_calculated.get("tin_kg_buy", 970.48),
            "calculated_prices": error_calculated,
            "base_data": error_prices,
            'fetchedAt': datetime.now(tz).isoformat(),
            'fetchedBy': 'Flask API v4.0 (錯誤狀態)',
            'error': str(e),
            'note': 'API錯誤，返回預設價格',
            'version': '4.0'
        }

        return jsonify(error_response), 200  # 仍返回200，但包含錯誤信息

@app.route('/api/news', methods=['GET'])
def api_get_news():
    """API端點 - 獲取新聞數據"""
    try:
        # 獲取緩存時間戳參數（用於前端緩存控制）
        cache_timestamp = request.args.get('t', '')
        # logger.info(f"新聞API請求 - 緩存時間戳: {cache_timestamp}")

        # 載入當前新聞
        news = load_news()

        # 添加API相關信息
        news['fetchedAt'] = datetime.now(tz).isoformat()
        news['fetchedBy'] = 'Flask API'
        news['cacheTimestamp'] = cache_timestamp

        return jsonify(news), 200

    except Exception as e:
        logger.error(f"API獲取新聞失敗: {e}")

        # 返回錯誤時的預設新聞
        error_news = DEFAULT_NEWS.copy()
        error_news.update({
            'fetchedAt': datetime.now(tz).isoformat(),
            'fetchedBy': 'Flask API',
            'error': str(e),
            'note': 'API錯誤，返回預設新聞'
        })

        return jsonify(error_news), 200  # 仍返回200，但包含錯誤信息

@app.route('/api/captcha', methods=['GET'])
def api_get_captcha():
    """API端點 - 獲取驗證碼"""
    try:
        settings = load_settings()
        if not settings['captcha']['enabled']:
            return jsonify({'enabled': False}), 200

        difficulty = settings['captcha']['difficulty']
        question, answer = generate_captcha(difficulty)

        # 生成驗證碼圖片
        image_data = generate_captcha_image(question)

        # 將答案存儲在 session 中
        session['captcha_answer'] = answer
        session['captcha_time'] = time.time()

        response_data = {
            'enabled': True,
            'question': question,  # 保留文字版本作為備用
            'difficulty': difficulty,
            'image': image_data  # base64 編碼的圖片
        }

        return jsonify(response_data), 200

    except Exception as e:
        logger.error(f"獲取驗證碼失敗: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/booking', methods=['POST'])
def api_submit_booking():
    """API端點 - 提交回收預約"""
    try:
        # 檢查提交頻率限制
        can_submit, error_msg = check_rate_limit('回收預約')
        if not can_submit:
            return jsonify({'error': error_msg}), 429

        data = request.get_json()
        if not data:
            return jsonify({'error': '無效的數據'}), 400

        # 驗證驗證碼
        settings = load_settings()
        if settings['captcha']['enabled']:
            if 'captcha_answer' not in data:
                return jsonify({'error': '請提供驗證碼答案'}), 400

            if 'captcha_answer' not in session:
                return jsonify({'error': '驗證碼已過期，請重新獲取'}), 400

            # 檢查驗證碼是否過期（10分鐘）
            if time.time() - session.get('captcha_time', 0) > 600:
                return jsonify({'error': '驗證碼已過期，請重新獲取'}), 400

            if int(data['captcha_answer']) != session['captcha_answer']:
                return jsonify({'error': '驗證碼錯誤'}), 400

        # 驗證必要字段
        required_fields = ['name', 'phone', 'email', 'address', 'metal_type', 'quantity', 'preferred_date']
        for field in required_fields:
            if field not in data or not data[field]:
                return jsonify({'error': f'缺少必要字段: {field}'}), 400

        # 載入現有預約數據
        booking_data = load_booking()

        # 創建新預約
        new_booking = {
            'id': int(time.time() * 1000),  # 使用時間戳作為 ID
            'name': data['name'],
            'phone': data['phone'],
            'email': data['email'],
            'address': data['address'],
            'metal_type': data['metal_type'],
            'quantity': data['quantity'],
            'preferred_date': data['preferred_date'],
            'preferred_time': data.get('preferred_time', ''),
            'notes': data.get('notes', ''),
            'status': 'pending',  # pending, completed, cancelled
            'submit_time': datetime.now(tz).isoformat(),
            'submit_ip': get_real_ip(),
            'completed_time': None,
            'admin_notes': ''
        }

        # 添加到列表開頭（最新的在前面）
        booking_data['bookings'].insert(0, new_booking)
        booking_data['lastUpdate'] = datetime.now(tz).isoformat()
        booking_data['updateTime'] = datetime.now(tz).strftime('%Y年%m月%d日 %H:%M:%S')

        # 保存數據
        if save_booking(booking_data):
            # 記錄提交
            record_submission('回收預約')
            # 清除驗證碼
            session.pop('captcha_answer', None)
            session.pop('captcha_time', None)

            # logger.info(f"新回收預約提交成功: {new_booking['id']}")
            return jsonify({
                'success': True,
                'message': '預約提交成功，我們會盡快與您聯繫',
                'booking_id': new_booking['id']
            }), 200
        else:
            return jsonify({'error': '保存失敗，請重試'}), 500

    except Exception as e:
        logger.error(f"提交回收預約失敗: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/contact', methods=['POST'])
def api_submit_contact():
    """API端點 - 提交聯絡我們"""
    try:
        # 檢查提交頻率限制
        can_submit, error_msg = check_rate_limit('聯絡我們')
        if not can_submit:
            return jsonify({'error': error_msg}), 429

        data = request.get_json()
        if not data:
            return jsonify({'error': '無效的數據'}), 400

        # 驗證驗證碼
        settings = load_settings()
        if settings['captcha']['enabled']:
            if 'captcha_answer' not in data:
                return jsonify({'error': '請提供驗證碼答案'}), 400

            if 'captcha_answer' not in session:
                return jsonify({'error': '驗證碼已過期，請重新獲取'}), 400

            # 檢查驗證碼是否過期（10分鐘）
            if time.time() - session.get('captcha_time', 0) > 600:
                return jsonify({'error': '驗證碼已過期，請重新獲取'}), 400

            if int(data['captcha_answer']) != session['captcha_answer']:
                return jsonify({'error': '驗證碼錯誤'}), 400

        # 驗證必要字段
        required_fields = ['name', 'email', 'subject', 'message']
        for field in required_fields:
            if field not in data or not data[field]:
                return jsonify({'error': f'缺少必要字段: {field}'}), 400

        # 載入現有聯絡數據
        contact_data = load_contact()

        # 創建新聯絡
        new_contact = {
            'id': int(time.time() * 1000),  # 使用時間戳作為 ID
            'name': data['name'],
            'email': data['email'],
            'phone': data.get('phone', ''),
            'subject': data['subject'],
            'message': data['message'],
            'status': 'pending',  # pending, completed, cancelled
            'submit_time': datetime.now(tz).isoformat(),
            'submit_ip': get_real_ip(),
            'completed_time': None,
            'admin_notes': ''
        }

        # 添加到列表開頭（最新的在前面）
        contact_data['contacts'].insert(0, new_contact)
        contact_data['lastUpdate'] = datetime.now(tz).isoformat()
        contact_data['updateTime'] = datetime.now(tz).strftime('%Y年%m月%d日 %H:%M:%S')

        # 保存數據
        if save_contact(contact_data):
            # 記錄提交
            record_submission('聯絡我們')
            # 清除驗證碼
            session.pop('captcha_answer', None)
            session.pop('captcha_time', None)

            # logger.info(f"新聯絡我們提交成功: {new_contact['id']}")
            return jsonify({
                'success': True,
                'message': '訊息提交成功，我們會盡快回覆您',
                'contact_id': new_contact['id']
            }), 200
        else:
            return jsonify({'error': '保存失敗，請重試'}), 500

    except Exception as e:
        logger.error(f"提交聯絡我們失敗: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/admin/bookings', methods=['GET'])
def get_admin_bookings():
    """獲取回收預約列表（需要認證）"""
    # 檢查 token
    auth_header = request.headers.get('Authorization')
    if not auth_header or not auth_header.startswith('Bearer '):
        return jsonify({'error': '需要認證'}), 401

    token = auth_header.split(' ')[1]
    admin_token = os.environ.get('ADMIN_TOKEN', '')

    if token != admin_token:
        return jsonify({'error': '無效的 Token'}), 401

    # 返回預約數據
    booking_data = load_booking()
    return jsonify(booking_data)

@app.route('/api/admin/contacts', methods=['GET'])
def get_admin_contacts():
    """獲取聯絡我們列表（需要認證）"""
    # 檢查 token
    auth_header = request.headers.get('Authorization')
    if not auth_header or not auth_header.startswith('Bearer '):
        return jsonify({'error': '需要認證'}), 401

    token = auth_header.split(' ')[1]
    admin_token = os.environ.get('ADMIN_TOKEN', '')

    if token != admin_token:
        return jsonify({'error': '無效的 Token'}), 401

    # 返回聯絡數據
    contact_data = load_contact()
    return jsonify(contact_data)

@app.route('/api/admin/settings', methods=['GET'])
def get_admin_settings():
    """獲取系統設定（需要認證）"""
    # 檢查 token
    auth_header = request.headers.get('Authorization')
    if not auth_header or not auth_header.startswith('Bearer '):
        return jsonify({'error': '需要認證'}), 401

    token = auth_header.split(' ')[1]
    admin_token = os.environ.get('ADMIN_TOKEN', '')

    if token != admin_token:
        return jsonify({'error': '無效的 Token'}), 401

    # 返回設定數據
    settings_data = load_settings()
    return jsonify(settings_data)

@app.route('/api/admin/settings', methods=['POST'])
def update_admin_settings():
    """更新系統設定（需要認證）"""
    # 檢查 token
    auth_header = request.headers.get('Authorization')
    if not auth_header or not auth_header.startswith('Bearer '):
        return jsonify({'error': '需要認證'}), 401

    token = auth_header.split(' ')[1]
    admin_token = os.environ.get('ADMIN_TOKEN', '')

    if token != admin_token:
        return jsonify({'error': '無效的 Token'}), 401

    # 獲取數據
    data = request.get_json()

    try:
        # 載入現有設定
        settings = load_settings()

        # 更新設定
        if 'captcha' in data:
            settings['captcha'].update(data['captcha'])
        if 'rateLimit' in data:
            settings['rateLimit'].update(data['rateLimit'])
        if 'twelvedata' in data:
            settings['twelvedata'].update(data['twelvedata'])

        settings['lastUpdate'] = datetime.now(tz).isoformat()
        settings['updateTime'] = datetime.now(tz).strftime('%Y年%m月%d日 %H:%M:%S')
        settings['updatedBy'] = data.get('updatedBy', '管理員')

        # 保存設定
        if save_settings(settings):
            # logger.info(f"管理員設定更新成功")
            return jsonify({
                'success': True,
                'message': '設定更新成功',
                'data': settings
            }), 200
        else:
            return jsonify({'error': '設定保存失敗'}), 500

    except Exception as e:
        logger.error(f"管理員設定更新失敗: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/admin/booking/<int:booking_id>', methods=['PUT'])
def update_booking_status(booking_id):
    """更新回收預約狀態（需要認證）"""
    # 檢查 token
    auth_header = request.headers.get('Authorization')
    if not auth_header or not auth_header.startswith('Bearer '):
        return jsonify({'error': '需要認證'}), 401

    token = auth_header.split(' ')[1]
    admin_token = os.environ.get('ADMIN_TOKEN', '')

    if token != admin_token:
        return jsonify({'error': '無效的 Token'}), 401

    try:
        data = request.get_json()
        booking_data = load_booking()

        # 找到對應的預約
        booking_found = False
        for booking in booking_data['bookings']:
            if booking['id'] == booking_id:
                booking['status'] = data.get('status', booking['status'])
                booking['admin_notes'] = data.get('admin_notes', booking['admin_notes'])
                if data.get('status') == 'completed':
                    booking['completed_time'] = datetime.now(tz).isoformat()
                booking_found = True
                break

        if not booking_found:
            return jsonify({'error': '預約不存在'}), 404

        booking_data['lastUpdate'] = datetime.now(tz).isoformat()
        booking_data['updateTime'] = datetime.now(tz).strftime('%Y年%m月%d日 %H:%M:%S')

        if save_booking(booking_data):
            return jsonify({'success': True, 'message': '預約狀態更新成功'}), 200
        else:
            return jsonify({'error': '保存失敗'}), 500

    except Exception as e:
        logger.error(f"更新預約狀態失敗: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/admin/booking/<int:booking_id>', methods=['DELETE'])
def delete_booking(booking_id):
    """刪除回收預約（需要認證）"""
    # 檢查 token
    auth_header = request.headers.get('Authorization')
    if not auth_header or not auth_header.startswith('Bearer '):
        return jsonify({'error': '需要認證'}), 401

    token = auth_header.split(' ')[1]
    admin_token = os.environ.get('ADMIN_TOKEN', '')

    if token != admin_token:
        return jsonify({'error': '無效的 Token'}), 401

    try:
        booking_data = load_booking()

        # 找到並刪除對應的預約
        original_length = len(booking_data['bookings'])
        booking_data['bookings'] = [b for b in booking_data['bookings'] if b['id'] != booking_id]

        if len(booking_data['bookings']) == original_length:
            return jsonify({'error': '預約不存在'}), 404

        booking_data['lastUpdate'] = datetime.now(tz).isoformat()
        booking_data['updateTime'] = datetime.now(tz).strftime('%Y年%m月%d日 %H:%M:%S')

        if save_booking(booking_data):
            return jsonify({'success': True, 'message': '預約刪除成功'}), 200
        else:
            return jsonify({'error': '保存失敗'}), 500

    except Exception as e:
        logger.error(f"刪除預約失敗: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/admin/contact/<int:contact_id>', methods=['PUT'])
def update_contact_status(contact_id):
    """更新聯絡我們狀態（需要認證）"""
    # 檢查 token
    auth_header = request.headers.get('Authorization')
    if not auth_header or not auth_header.startswith('Bearer '):
        return jsonify({'error': '需要認證'}), 401

    token = auth_header.split(' ')[1]
    admin_token = os.environ.get('ADMIN_TOKEN', '')

    if token != admin_token:
        return jsonify({'error': '無效的 Token'}), 401

    try:
        data = request.get_json()
        contact_data = load_contact()

        # 找到對應的聯絡
        contact_found = False
        for contact in contact_data['contacts']:
            if contact['id'] == contact_id:
                contact['status'] = data.get('status', contact['status'])
                contact['admin_notes'] = data.get('admin_notes', contact['admin_notes'])
                if data.get('status') == 'completed':
                    contact['completed_time'] = datetime.now(tz).isoformat()
                contact_found = True
                break

        if not contact_found:
            return jsonify({'error': '聯絡不存在'}), 404

        contact_data['lastUpdate'] = datetime.now(tz).isoformat()
        contact_data['updateTime'] = datetime.now(tz).strftime('%Y年%m月%d日 %H:%M:%S')

        if save_contact(contact_data):
            return jsonify({'success': True, 'message': '聯絡狀態更新成功'}), 200
        else:
            return jsonify({'error': '保存失敗'}), 500

    except Exception as e:
        logger.error(f"更新聯絡狀態失敗: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/admin/contact/<int:contact_id>', methods=['DELETE'])
def delete_contact(contact_id):
    """刪除聯絡我們（需要認證）"""
    # 檢查 token
    auth_header = request.headers.get('Authorization')
    if not auth_header or not auth_header.startswith('Bearer '):
        return jsonify({'error': '需要認證'}), 401

    token = auth_header.split(' ')[1]
    admin_token = os.environ.get('ADMIN_TOKEN', '')

    if token != admin_token:
        return jsonify({'error': '無效的 Token'}), 401

    try:
        contact_data = load_contact()

        # 找到並刪除對應的聯絡
        original_length = len(contact_data['contacts'])
        contact_data['contacts'] = [c for c in contact_data['contacts'] if c['id'] != contact_id]

        if len(contact_data['contacts']) == original_length:
            return jsonify({'error': '聯絡不存在'}), 404

        contact_data['lastUpdate'] = datetime.now(tz).isoformat()
        contact_data['updateTime'] = datetime.now(tz).strftime('%Y年%m月%d日 %H:%M:%S')

        if save_contact(contact_data):
            return jsonify({'success': True, 'message': '聯絡刪除成功'}), 200
        else:
            return jsonify({'error': '保存失敗'}), 500

    except Exception as e:
        logger.error(f"刪除聯絡失敗: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/prices', methods=['POST'])
def api_update_prices():
    """API端點 - 更新價格數據（新版本支援完整數據結構）"""
    try:
        # 檢查認證（管理員功能需要認證）
        auth_header = request.headers.get('Authorization')
        is_admin_request = auth_header and auth_header.startswith('Bearer ')

        if is_admin_request:
            token = auth_header.split(' ')[1]
            if not verify_token(token):
                return jsonify({'error': '認證失敗'}), 401

        # 獲取JSON數據
        data = request.get_json()

        if not data:
            return jsonify({'error': '無效的JSON數據'}), 400

        # 檢查是否為新版本數據結構
        if 'metals' in data or 'industrial_metals' in data or 'currencies' in data:
            # 新版本數據結構 - 需要管理員認證
            if not is_admin_request:
                return jsonify({'error': '新版本數據結構需要管理員認證'}), 401

            logger.info("收到新版本價格數據結構")

            # 驗證數據結構
            validated_data = validate_new_price_structure(data)
            if not validated_data:
                return jsonify({'error': '數據結構驗證失敗'}), 400

            # 保存新版本數據
            if save_prices(validated_data):
                logger.info(f"管理員更新價格成功 (新版本): 包含 {len(validated_data.get('metals', {}))} 種貴金屬, {len(validated_data.get('industrial_metals', {}))} 種工業金屬, {len(validated_data.get('currencies', {}))} 種匯率")

                return jsonify({
                    'success': True,
                    'message': '價格更新成功 (新版本)',
                    'data': validated_data,
                    'version': '2.0'
                }), 200
            else:
                return jsonify({'error': '價格保存失敗'}), 500

        else:
            # 舊版本數據結構 - 向後兼容
            logger.info("收到舊版本價格數據結構")

            # 驗證必要字段
            required_fields = ['gold', 'silver', 'copper', 'aluminum']
            for field in required_fields:
                if field not in data:
                    return jsonify({'error': f'缺少必要字段: {field}'}), 400

                try:
                    float(data[field])
                except (ValueError, TypeError):
                    return jsonify({'error': f'字段 {field} 必須是有效數字'}), 400

            if is_admin_request:
                # 管理員請求：轉換為新版本格式
                current_prices = load_prices()

                # 更新基礎價格（假設舊版本的價格是台幣/公克或台幣/公斤）
                usd_twd_rate = current_prices.get('currencies', {}).get('usd', {}).get('twd_rate', 31.5)

                # 金和銀：台幣/公克 -> 美元/盎司
                current_prices['metals']['gold']['usd_per_oz'] = float(data['gold']) / usd_twd_rate * 31.1035
                current_prices['metals']['silver']['usd_per_oz'] = float(data['silver']) / usd_twd_rate * 31.1035

                # 銅和鋁：台幣/公斤 -> 美元/磅
                current_prices['industrial_metals']['copper']['usd_per_lb'] = float(data['copper']) / usd_twd_rate * 0.453592
                current_prices['industrial_metals']['aluminum']['usd_per_lb'] = float(data['aluminum']) / usd_twd_rate * 0.453592

                # 更新系統資訊
                current_prices['lastUpdate'] = datetime.now(tz).isoformat()
                current_prices['updateTime'] = datetime.now(tz).strftime('%Y年%m月%d日 %H:%M:%S')
                current_prices['updatedBy'] = data.get('updatedBy', '管理員')
                current_prices['source'] = "管理員更新 (舊版本兼容)"

                # 保存數據
                if save_prices(current_prices):
                    logger.info(f"管理員更新價格成功 (舊版本兼容): {data}")

                    return jsonify({
                        'success': True,
                        'message': '價格更新成功 (舊版本兼容)',
                        'data': current_prices,
                        'legacy_data': data,
                        'version': '2.0'
                    }), 200
                else:
                    return jsonify({'error': '價格保存失敗'}), 500
            else:
                # 非管理員請求：使用舊版本格式
                new_prices = {
                    "gold": float(data['gold']),
                    "silver": float(data['silver']),
                    "copper": float(data['copper']),
                    "aluminum": float(data['aluminum']),
                    "lastUpdate": datetime.now(tz).isoformat(),
                    "updateTime": datetime.now(tz).strftime('%Y年%m月%d日 %H:%M:%S'),
                    "updatedBy": data.get('updatedBy', 'API用戶'),
                    "source": "Flask API - JSON更新"
                }

                # 保存價格數據
                if save_prices(new_prices):
                    logger.info(f"API價格更新成功 (舊版本): {new_prices}")
                    return jsonify({
                        'success': True,
                        'message': '價格更新成功',
                        'data': new_prices
                    }), 200
                else:
                    return jsonify({'error': '價格保存失敗'}), 500

    except Exception as e:
        logger.error(f"API更新價格失敗: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/health')
def health_check():
    """健康檢查端點"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now(tz).isoformat(),
        'service': 'Price API'
    }), 200

@app.route('/robots.txt')
def robots():
    robots_txt = """User-agent: *
Disallow: /admi
Disallow: /marke
Disallow: /booki
Disallow: /conta
"""
    return Response(robots_txt, mimetype="text/plain")

def init_data_structure():
    """初始化數據結構"""
    # 創建數據目錄
    try:
        os.makedirs(DATA_DIR, exist_ok=True)
        print(f"✅ 數據目錄創建成功")
    except Exception:
        return False

    # 檢查價格文件是否存在
    prices_exists = False
    if os.path.exists(PRICES_FILE):
        print(f"✅ 價格文件已存在")
        try:
            with open(PRICES_FILE, 'r', encoding='utf-8') as f:
                json.load(f)
                prices_exists = True
        except Exception:
            pass

    # 創建預設價格文件（如果不存在）
    if not prices_exists:
        if save_prices(DEFAULT_PRICES):
            print(f"✅ 預設價格文件創建成功: {PRICES_FILE}")
        else:
            return False

    # 檢查新聞文件是否存在
    news_exists = False
    if os.path.exists(NEWS_FILE):
        print(f"✅ 新聞文件已存在")
        try:
            with open(NEWS_FILE, 'r', encoding='utf-8') as f:
                json.load(f)
                news_exists = True
        except Exception:
            pass

    # 創建預設新聞文件（如果不存在）
    if not news_exists:
        if save_news(DEFAULT_NEWS):
            print(f"✅ 預設新聞文件創建成功: {NEWS_FILE}")
        else:
            return False

    # 檢查回收預約文件是否存在
    booking_exists = False
    if os.path.exists(BOOKING_FILE):
        print(f"✅ 回收預約文件已存在")
        try:
            with open(BOOKING_FILE, 'r', encoding='utf-8') as f:
                json.load(f)
                booking_exists = True
        except Exception:
            pass

    # 創建預設回收預約文件（如果不存在）
    if not booking_exists:
        if save_booking(DEFAULT_BOOKING):
            print(f"✅ 預設回收預約文件創建成功: {BOOKING_FILE}")
        else:
            return False

    # 檢查聯絡我們文件是否存在
    contact_exists = False
    if os.path.exists(CONTACT_FILE):
        print(f"✅ 聯絡我們文件已存在")
        try:
            with open(CONTACT_FILE, 'r', encoding='utf-8') as f:
                json.load(f)
                contact_exists = True
        except Exception:
            pass

    # 創建預設聯絡我們文件（如果不存在）
    if not contact_exists:
        if save_contact(DEFAULT_CONTACT):
            print(f"✅ 預設聯絡我們文件創建成功: {CONTACT_FILE}")
        else:
            return False

    # 檢查系統設定文件是否存在
    settings_exists = False
    if os.path.exists(SETTINGS_FILE):
        print(f"✅ 系統設定文件已存在")
        try:
            with open(SETTINGS_FILE, 'r', encoding='utf-8') as f:
                json.load(f)
                settings_exists = True
        except Exception:
            pass

    # 創建預設系統設定文件（如果不存在）
    if not settings_exists:
        if save_settings(DEFAULT_SETTINGS):
            print(f"✅ 預設系統設定文件創建成功: {SETTINGS_FILE}")
        else:
            return False

    return True

def check_permissions():
    """檢查文件權限"""
    try:
        test_file = os.path.join(DATA_DIR, "test_write.tmp")
        with open(test_file, 'w') as f:
            f.write("test")
        os.remove(test_file)
        return True
    except Exception:
        return False

# TwelveData API 相關功能
import requests
import time

# TwelveData 價格緩存和請求追蹤
TWELVEDATA_CACHE = {
    "prices": None,
    "last_update": 0,
    "update_interval": 3600,  # 預設1小時
    "last_frontend_request": 0,  # 最後一次前端請求時間
    "is_paused": False,  # 是否因閒置而暫停
    "active_sessions": set(),  # 活躍的 session ID
    "api_key_valid": None,  # API Key 是否有效 (None=未測試, True=有效, False=無效)
    "last_key_test": 0,  # 最後一次 Key 測試時間
    "key_test_interval": 900,  # Key 測試間隔（15分鐘 = 900秒）
    "network_retry_count": 0,  # 網絡重試次數
    "max_network_retries": 3  # 最大網絡重試次數
}

def calculate_free_tier_interval():
    """計算免費版本的合理更新間隔
    免費版本：800 requests/day
    目前只有1個商品（黃金）可用於免費版本
    1個商品 * 31天 = 31個請求/月
    800/30 ≈ 26.67 requests/day，遠超需求
    可以設定為較短間隔，但為了保險起見仍設定為1小時
    """
    return 3600  # 1小時

def is_api_key_error(response_data):
    """判斷是否為 API Key 錯誤"""
    if isinstance(response_data, dict):
        error_message = response_data.get('message', '').lower()
        error_code = response_data.get('code', 0)

        # 常見的 API Key 錯誤指示
        key_error_indicators = [
            'invalid api key',
            'api key',
            'unauthorized',
            'authentication',
            'invalid apikey',
            'apikey'
        ]

        # 檢查錯誤碼（401 通常表示認證錯誤）
        if error_code == 401:
            return True

        # 檢查錯誤信息
        for indicator in key_error_indicators:
            if indicator in error_message:
                return True

    return False

def check_api_quota():
    """檢查 API 配額是否足夠"""
    settings = load_settings()
    quota_config = settings.get('twelvedata', {}).get('quota', {})

    current_date = datetime.now(tz).strftime('%Y-%m-%d')
    current_minute = int(time.time() // 60)

    # 重置每日計數器
    if quota_config.get('last_reset_date') != current_date:
        quota_config['current_daily_usage'] = 0
        quota_config['last_reset_date'] = current_date

    # 重置每分鐘計數器
    if quota_config.get('last_minute_reset', 0) != current_minute:
        quota_config['current_minute_usage'] = 0
        quota_config['last_minute_reset'] = current_minute

    # 檢查配額
    daily_usage = quota_config.get('current_daily_usage', 0)
    minute_usage = quota_config.get('current_minute_usage', 0)
    daily_limit = quota_config.get('daily_limit', 800)
    minute_limit = quota_config.get('minute_limit', 8)

    if daily_usage >= daily_limit:
        logger.warning(f"TwelveData 每日配額已用完: {daily_usage}/{daily_limit}")
        return False, "每日配額已用完"

    if minute_usage >= minute_limit:
        logger.warning(f"TwelveData 每分鐘配額已用完: {minute_usage}/{minute_limit}")
        return False, "每分鐘配額已用完"

    return True, f"配額正常 (日:{daily_usage}/{daily_limit}, 分:{minute_usage}/{minute_limit})"

def update_api_quota():
    """更新 API 配額使用計數"""
    settings = load_settings()
    quota_config = settings.get('twelvedata', {}).get('quota', {})

    quota_config['current_daily_usage'] = quota_config.get('current_daily_usage', 0) + 1
    quota_config['current_minute_usage'] = quota_config.get('current_minute_usage', 0) + 1

    # 保存更新後的設定
    save_settings(settings)

def fetch_twelvedata_prices():
    """從 TwelveData API 獲取價格數據 (新版本)"""
    # 首先檢查 TwelveData 功能是否啟用
    settings = load_settings()
    if not settings.get('twelvedata', {}).get('enabled', False):
        logger.info("TwelveData 功能未啟用，跳過價格更新")
        return None

    api_key = os.environ.get('TWELVEDATA_API_KEY', '').strip()

    if not api_key:
        logger.info("TwelveData API key 未設定，使用模擬數據進行開發")
        # 開發階段：返回模擬的 TwelveData 數據
        return generate_mock_twelvedata_prices()

    # 檢查 API 配額
    quota_ok, quota_msg = check_api_quota()
    if not quota_ok:
        logger.warning(f"TwelveData API 配額不足: {quota_msg}")
        return None

    # 載入設定以獲取符號映射
    twelvedata_config = settings.get('twelvedata', {})

    # 獲取當前價格數據以確定哪些項目啟用了自動更新
    current_prices = load_prices()

    # 第一階段：檢查並禁用沒有符號映射的啟用項目
    items_disabled_due_to_no_mapping = []

    # 檢查基礎金屬
    for metal, data in current_prices.get('metals', {}).items():
        if data.get('auto_update', False) and data.get('enabled', True):
            symbol = twelvedata_config.get('metal_symbols', {}).get(metal)
            if not symbol:
                disable_auto_update_for_item(metal)
                items_disabled_due_to_no_mapping.append(f"基礎金屬 {metal}")

    # 檢查稀有金屬（通過 physical_metals）
    for metal_gram, data in current_prices.get('physical_metals', {}).items():
        if data.get('auto_update', False) and data.get('enabled', True):
            base_metal = metal_gram.replace('_gram', '')
            if base_metal not in ['gold', 'silver', 'palladium', 'platinum']:  # 稀有金屬
                symbol = twelvedata_config.get('metal_symbols', {}).get(base_metal)
                if not symbol:
                    disable_auto_update_for_item(base_metal)
                    items_disabled_due_to_no_mapping.append(f"稀有金屬 {base_metal}")

    # 檢查其他材料
    for material, data in current_prices.get('other_materials', {}).items():
        if data.get('auto_update', False) and data.get('enabled', True):
            base_name = material.replace('_kg', '')
            symbol = twelvedata_config.get('industrial_symbols', {}).get(base_name)
            if not symbol:
                disable_auto_update_for_item(base_name)
                items_disabled_due_to_no_mapping.append(f"其他材料 {material}")

    # 檢查工業金屬
    for metal, data in current_prices.get('industrial_metals', {}).items():
        if data.get('auto_update', False) and data.get('enabled', True):
            symbol = twelvedata_config.get('industrial_symbols', {}).get(metal)
            if not symbol:
                disable_auto_update_for_item(metal)
                items_disabled_due_to_no_mapping.append(f"工業金屬 {metal}")

    # 檢查匯率
    for currency, data in current_prices.get('currencies', {}).items():
        if data.get('auto_update', False) and data.get('enabled', True):
            symbol = twelvedata_config.get('currency_symbols', {}).get(currency)
            if not symbol:
                disable_auto_update_for_item(currency)
                items_disabled_due_to_no_mapping.append(f"匯率 {currency}")

    if items_disabled_due_to_no_mapping:
        logger.info(f"由於沒有符號映射，已自動禁用以下項目的自動更新: {', '.join(items_disabled_due_to_no_mapping)}")
        # 重新載入價格數據，因為可能有項目被禁用了
        current_prices = load_prices()

    # 收集需要更新的符號（第二階段：只處理有符號映射的項目）
    symbols_to_fetch = {}
    required_base_metals = set()

    # 檢查基礎金屬（美元/盎司）
    for metal, data in current_prices.get('metals', {}).items():
        if data.get('auto_update', False) and data.get('enabled', True):
            symbol = twelvedata_config.get('metal_symbols', {}).get(metal)
            if symbol:
                required_base_metals.add(metal)

    # 檢查台幣金屬（依賴基礎金屬）
    for metal_twd, data in current_prices.get('metals_twd', {}).items():
        if data.get('auto_update', False) and data.get('enabled', True):
            base_metal = metal_twd.replace('_twd', '')
            
            # 檢查基礎金屬本身是否仍啟用 auto_update（避免查詢已被404禁用的金屬）
            base_metal_enabled = True
            if base_metal in current_prices.get('metals', {}):
                base_metal_enabled = current_prices['metals'][base_metal].get('auto_update', False)
            
            symbol = twelvedata_config.get('metal_symbols', {}).get(base_metal)
            if symbol and base_metal_enabled:
                required_base_metals.add(base_metal)
            elif not base_metal_enabled:
                logger.info(f"跳過已禁用的基礎金屬 {base_metal}（對應台錢項目 {metal_twd}）")

    # 檢查實物交易金屬（依賴基礎金屬或稀有金屬）
    for physical_metal, data in current_prices.get('physical_metals', {}).items():
        if data.get('auto_update', False) and data.get('enabled', True):
            base_metal = physical_metal.replace('_gram', '')
            
            # 對於基礎金屬，檢查在 metals 區塊中的狀態
            # 對於稀有金屬，直接使用本身的 auto_update 狀態
            should_query = False
            if base_metal in ['gold', 'silver', 'palladium', 'platinum']:
                # 基礎金屬：檢查在 metals 區塊中的 auto_update 狀態
                if base_metal in current_prices.get('metals', {}):
                    should_query = current_prices['metals'][base_metal].get('auto_update', False)
                    if not should_query:
                        logger.info(f"跳過已禁用的基礎金屬 {base_metal}（對應實物交易項目 {physical_metal}）")
            else:
                # 稀有金屬：直接使用已經檢查過的 data.get('auto_update', False)
                # 如果它被 404 禁用，這裡就不會是 True
                should_query = True
            
            symbol = twelvedata_config.get('metal_symbols', {}).get(base_metal)
            if symbol and should_query:
                # 如果是基礎金屬（gold, silver, palladium, platinum），加入 required_base_metals
                if base_metal in ['gold', 'silver', 'palladium', 'platinum']:
                    required_base_metals.add(base_metal)
                else:
                    # 如果是稀有金屬（rhodium, ruthenium, iridium, osmium），直接加入請求列表
                    symbols_to_fetch[base_metal] = symbol
                    logger.info(f"添加稀有金屬到更新列表: {base_metal} -> {symbol}")

    # 將需要的基礎金屬添加到請求列表
    for metal in required_base_metals:
        symbol = twelvedata_config.get('metal_symbols', {}).get(metal)
        if symbol:
            symbols_to_fetch[metal] = symbol
            # logger.info(f"添加基礎金屬到更新列表: {metal} -> {symbol}")

    # 檢查工業金屬
    for metal, data in current_prices.get('industrial_metals', {}).items():
        if data.get('auto_update', False) and data.get('enabled', True):
            symbol = twelvedata_config.get('industrial_symbols', {}).get(metal)
            if symbol:
                symbols_to_fetch[metal] = symbol

    # 檢查其他材料（cobalt_kg, tin_kg）
    for material, data in current_prices.get('other_materials', {}).items():
        if data.get('auto_update', False) and data.get('enabled', True):
            # 將 cobalt_kg -> cobalt, tin_kg -> tin 來查找符號
            base_name = material.replace('_kg', '')
            symbol = twelvedata_config.get('industrial_symbols', {}).get(base_name)
            if symbol:
                symbols_to_fetch[base_name] = symbol  # 使用 base_name 作為 key
                logger.info(f"添加其他材料到更新列表: {material} ({base_name}) -> {symbol}")

    # 檢查匯率
    for currency, data in current_prices.get('currencies', {}).items():
        if data.get('auto_update', False) and data.get('enabled', True):
            symbol = twelvedata_config.get('currency_symbols', {}).get(currency)
            if symbol:
                symbols_to_fetch[currency] = symbol
                # logger.info(f"添加匯率到更新列表: {currency} -> {symbol}")

    if not symbols_to_fetch:
        logger.info("沒有啟用自動更新的項目，跳過 TwelveData 請求")
        return None

    # logger.info(f"準備從 TwelveData 獲取 {len(symbols_to_fetch)} 個項目的價格: {list(symbols_to_fetch.keys())}")

    fetched_data = {}

    try:
        # 分離基礎金屬和其他項目
        base_metals = ['gold', 'silver', 'palladium', 'platinum']
        base_metal_symbols = {}
        other_symbols = {}

        for item, symbol in symbols_to_fetch.items():
            if item in base_metals:
                base_metal_symbols[item] = symbol
            else:
                other_symbols[item] = symbol

        # logger.info(f"基礎金屬 {len(base_metal_symbols)} 個: {list(base_metal_symbols.keys())}")
        # logger.info(f"其他項目 {len(other_symbols)} 個: {list(other_symbols.keys())}")

        # 檢查 API 配額
        total_requests = len(base_metal_symbols) + (1 if other_symbols else 0)
        quota_ok, _ = check_api_quota()
        if not quota_ok:
            logger.warning("API 配額不足，無法進行請求")
            return None

        # 1. 個別請求基礎金屬（使用 quote 端點獲取完整數據）
        for metal, symbol in base_metal_symbols.items():
            try:
                url = f"https://api.twelvedata.com/quote"
                params = {
                    'symbol': symbol,
                    'apikey': api_key
                }

                # logger.info(f"請求基礎金屬完整數據: {metal} ({symbol})")
                response = requests.get(url, params=params, timeout=10)
                update_api_quota()

                if response.status_code == 200:
                    data = response.json()
                    if 'close' in data:
                        price_data = {
                            'price': float(data['close']),
                            'high': float(data.get('high', data['close'])),
                            'low': float(data.get('low', data['close'])),
                            'previous_close': float(data.get('previous_close', data['close']))
                        }
                        fetched_data[metal] = price_data
                        # logger.info(f"基礎金屬 {metal} 完整數據獲取成功: 價格={price_data['price']}, 高={price_data['high']}, 低={price_data['low']}")
                    else:
                        logger.warning(f"基礎金屬 {metal} API 回應格式錯誤: {data}")
                else:
                    logger.warning(f"基礎金屬 {metal} API 請求失敗: {response.status_code}")

            except Exception as e:
                logger.error(f"基礎金屬 {metal} 請求失敗: {e}")

        # 2. 批量請求其他項目（使用 price 端點）
        if other_symbols:
            symbols_list = list(other_symbols.values())
            symbols_str = ','.join(symbols_list)

            # logger.info(f"使用批量請求獲取其他項目 {len(symbols_list)} 個符號: {symbols_str}")

            # 批量請求
            url = f"https://api.twelvedata.com/price"
            params = {
                'symbol': symbols_str,
                'apikey': api_key
            }

            response = requests.get(url, params=params, timeout=30)
            update_api_quota()

        if response.status_code == 200:
            data = response.json()

            # 檢查是否為 API Key 錯誤
            if is_api_key_error(data):
                logger.error(f"TwelveData API Key 驗證失敗: {data}")
                TWELVEDATA_CACHE["api_key_valid"] = False
                TWELVEDATA_CACHE["last_key_test"] = time.time()
                return None

            # 處理批量響應
            if isinstance(data, dict):
                # 批量響應的格式可能是 {symbol: {price: value}} 或直接的錯誤
                items_disabled_due_to_404 = []

                for item_name, symbol in symbols_to_fetch.items():
                    if symbol in data:
                        symbol_data = data[symbol]
                        if isinstance(symbol_data, dict) and 'price' in symbol_data:
                            price = float(symbol_data['price'])
                            fetched_data[item_name] = price
                            # logger.info(f"TwelveData {item_name} 價格獲取成功: {price}")
                        elif isinstance(symbol_data, dict) and symbol_data.get('code') == 404:
                            logger.error(f"TwelveData {item_name} 符號無效: {symbol_data}")
                            disable_auto_update_for_item(item_name)
                            items_disabled_due_to_404.append(f"{item_name} ({symbol})")
                            logger.warning(f"由於 TwelveData 不支援 {item_name}，已自動禁用其自動更新功能")
                        else:
                            logger.error(f"TwelveData {item_name} 價格數據格式錯誤: {symbol_data}")
                    else:
                        # logger.warning(f"批量響應中未找到符號 {symbol} 的數據")
                        pass

                if items_disabled_due_to_404:
                    logger.info(f"由於 API 返回 404 錯誤，已自動禁用以下項目的自動更新: {', '.join(items_disabled_due_to_404)}")
                    # 重新載入價格數據，因為有項目被禁用了
                    current_prices = load_prices()

            # 成功獲取價格，標記 API Key 有效
            if fetched_data:
                TWELVEDATA_CACHE["api_key_valid"] = True
                TWELVEDATA_CACHE["network_retry_count"] = 0
        else:
            logger.error(f"TwelveData 批量 API 請求失敗: {response.status_code}")
            logger.error(f"響應內容: {response.text}")

            # 如果批量請求失敗，回退到單個請求
            logger.info("批量請求失敗，回退到單個請求模式")
            return fetch_prices_individually(symbols_to_fetch, api_key)

    except requests.exceptions.RequestException as e:
        logger.error(f"TwelveData 網絡請求失敗: {e}")
        TWELVEDATA_CACHE["network_retry_count"] += 1
        return None
    except Exception as e:
        logger.error(f"TwelveData 請求過程中發生錯誤: {e}")
        return None

    if fetched_data:
        # 更新基礎價格數據（使用最新的 current_prices，以防止被 404 禁用的項目被復寫）
        updated_prices = update_base_prices_with_twelvedata(current_prices, fetched_data)
        return updated_prices

    return None

def fetch_prices_individually(symbols_to_fetch, api_key):
    """回退函數：當批量請求失敗時，使用單個請求獲取價格"""
    logger.info("使用單個請求模式獲取價格")
    fetched_data = {}
    items_disabled_due_to_404 = []

    try:
        for item_name, symbol in symbols_to_fetch.items():
            # 檢查每次請求前的配額
            quota_ok, _ = check_api_quota()
            if not quota_ok:
                logger.warning("API 配額在請求過程中用完，停止後續請求")
                break

            # 對於基礎金屬，使用 quote 端點獲取完整數據
            if item_name in ['gold', 'silver', 'palladium', 'platinum']:
                url = f"https://api.twelvedata.com/quote"
            else:
                url = f"https://api.twelvedata.com/price"

            params = {
                'symbol': symbol,
                'apikey': api_key
            }

            # logger.info(f"請求 TwelveData: {item_name} ({symbol})")
            response = requests.get(url, params=params, timeout=10)

            # 更新配額計數
            update_api_quota()

            if response.status_code == 200:
                data = response.json()

                # 檢查是否為 API Key 錯誤
                if is_api_key_error(data):
                    logger.error(f"TwelveData API Key 驗證失敗: {data}")
                    TWELVEDATA_CACHE["api_key_valid"] = False
                    TWELVEDATA_CACHE["last_key_test"] = time.time()
                    return None

                # 處理 quote 端點的完整數據
                if 'close' in data:
                    price_data = {
                        'price': float(data['close']),
                        'high': float(data.get('high', data['close'])),
                        'low': float(data.get('low', data['close'])),
                        'previous_close': float(data.get('previous_close', data['close']))
                    }
                    fetched_data[item_name] = price_data
                    # logger.info(f"TwelveData {item_name} 完整數據獲取成功: 價格={price_data['price']}, 高={price_data['high']}, 低={price_data['low']}")
                elif 'price' in data:
                    # 回退到簡單價格數據
                    price = float(data['price'])
                    fetched_data[item_name] = {'price': price}
                    # logger.info(f"TwelveData {item_name} 價格獲取成功: {price}")
                    # logger.info(f"TwelveData {item_name} 價格獲取成功: {price}")

                    # 成功獲取價格，標記 API Key 有效
                    TWELVEDATA_CACHE["api_key_valid"] = True
                    TWELVEDATA_CACHE["network_retry_count"] = 0
                else:
                    logger.error(f"TwelveData {item_name} 價格數據格式錯誤: {data}")

                    # 如果是 404 錯誤（符號無效），自動禁用該項目的自動更新
                    if data.get('code') == 404:
                        disable_auto_update_for_item(item_name)
                        items_disabled_due_to_404.append(f"{item_name} ({symbol})")
                        logger.warning(f"由於 TwelveData 不支援 {item_name}，已自動禁用其自動更新功能")
            else:
                logger.error(f"TwelveData {item_name} API 請求失敗: {response.status_code}")

            # 避免API限制，請求間隔1秒
            time.sleep(1.0)

    except requests.exceptions.RequestException as e:
        logger.error(f"TwelveData 網絡請求失敗: {e}")
        TWELVEDATA_CACHE["network_retry_count"] += 1
        return None
    except Exception as e:
        logger.error(f"TwelveData 請求過程中發生錯誤: {e}")
        return None

    if items_disabled_due_to_404:
        logger.info(f"單個請求模式中，由於 API 返回 404 錯誤，已自動禁用以下項目的自動更新: {', '.join(items_disabled_due_to_404)}")
        # 重新載入價格數據，因為有項目被禁用了
        current_prices = load_prices()

    if fetched_data:
        logger.info(f"單個請求模式成功獲取 {len(fetched_data)} 個價格: {list(fetched_data.keys())}")
        # 使用最新的 current_prices（在 404 處理後可能已更新）
        current_prices = load_prices()
        updated_prices = update_base_prices_with_twelvedata(current_prices, fetched_data)
        return updated_prices
    else:
        logger.warning("單個請求模式沒有獲取到任何價格數據")
        return None

def generate_mock_twelvedata_prices():
    """生成模擬的 TwelveData 價格數據（開發階段使用）"""
    import random

    logger.info("生成模擬 TwelveData 價格數據（開發模式）")

    # 基礎價格（模擬真實市場價格）
    mock_prices = {
        # 貴金屬 (美元/盎司)
        'gold': 2045.80 + random.uniform(-50, 50),
        'silver': 24.50 + random.uniform(-2, 2),
        'palladium': 1050.00 + random.uniform(-100, 100),
        'platinum': 950.00 + random.uniform(-80, 80),
        'rhodium': 4500.00 + random.uniform(-500, 500),
        'ruthenium': 450.00 + random.uniform(-50, 50),
        'iridium': 1800.00 + random.uniform(-200, 200),
        'osmium': 400.00 + random.uniform(-40, 40),

        # 工業金屬 (美元/磅)
        'copper': 4.20 + random.uniform(-0.5, 0.5),
        'aluminum': 0.85 + random.uniform(-0.1, 0.1),
        'cobalt': 15.50 + random.uniform(-2, 2),
        'tin': 14.20 + random.uniform(-1.5, 1.5),

        # 匯率 (外幣對台幣)
        'usd': 31.50 + random.uniform(-0.5, 0.5),
        'cny': 4.35 + random.uniform(-0.2, 0.2),
        'eur': 34.20 + random.uniform(-1, 1)
    }

    # 載入當前價格數據
    current_prices = load_prices()

    # 只返回啟用自動更新的項目
    filtered_prices = {}

    # 檢查金屬
    for metal, data in current_prices.get('metals', {}).items():
        if data.get('auto_update', False) and metal in mock_prices:
            filtered_prices[metal] = mock_prices[metal]

    # 檢查工業金屬
    for metal, data in current_prices.get('industrial_metals', {}).items():
        if data.get('auto_update', False) and metal in mock_prices:
            filtered_prices[metal] = mock_prices[metal]

    # 檢查匯率
    for currency, data in current_prices.get('currencies', {}).items():
        if data.get('auto_update', False) and currency in mock_prices:
            filtered_prices[currency] = mock_prices[currency]

    if filtered_prices:
        logger.info(f"模擬數據包含 {len(filtered_prices)} 個項目: {list(filtered_prices.keys())}")
        return update_base_prices_with_twelvedata(current_prices, filtered_prices)

    return None

def update_base_prices_with_twelvedata(base_prices, fetched_data):
    """使用 TwelveData 獲取的數據更新基礎價格（修正版本 - 支援所有金屬類型）"""
    updated_prices = base_prices.copy()
    current_time = datetime.now(tz).isoformat()
    usd_twd_rate = updated_prices.get('currencies', {}).get('usd', {}).get('twd_rate', 31.0)

    # 更新價格數據
    for metal, price_data in fetched_data.items():
        updated = False

        # 1. 檢查基礎金屬（美元/盎司）
        if metal in updated_prices.get('metals', {}):
            if isinstance(price_data, dict):
                # 完整數據包含最高最低點 - 修正版本：保留先前值而不是設為 null
                updated_prices['metals'][metal]['usd_per_oz'] = round(price_data['price'], 2)
                
                # 只有當 API 有返回且不為 None 時才更新，否則保留原值
                if price_data.get('high') is not None:
                    updated_prices['metals'][metal]['daily_high'] = round(price_data['high'], 2)
                # 如果 API 未返回 daily_high，保留原有值
                
                if price_data.get('low') is not None:
                    updated_prices['metals'][metal]['daily_low'] = round(price_data['low'], 2)
                # 如果 API 未返回 daily_low，保留原有值
                
                if price_data.get('previous_close') is not None:
                    updated_prices['metals'][metal]['previous_close'] = round(price_data['previous_close'], 2)
                # 如果 API 未返回 previous_close，保留原有值
                
                updated_prices['metals'][metal]['last_update'] = current_time
                # logger.info(f"更新基礎金屬完整數據: {metal} = ${price_data['price']}/oz (高:{price_data.get('high')}, 低:{price_data.get('low')})")

                # 同時更新相關的衍生價格
                update_derived_prices_for_metal(updated_prices, metal, price_data['price'], usd_twd_rate, current_time)
            else:
                # 簡單價格數據（向後兼容）
                updated_prices['metals'][metal]['usd_per_oz'] = round(price_data, 2)
                updated_prices['metals'][metal]['last_update'] = current_time
                # logger.info(f"更新基礎金屬價格: {metal} = ${price_data}/oz")

                # 同時更新相關的衍生價格
                update_derived_prices_for_metal(updated_prices, metal, price_data, usd_twd_rate, current_time)
            updated = True

        # 2. 檢查工業金屬（美元/磅）
        elif metal in updated_prices.get('industrial_metals', {}):
            price_value = price_data['price'] if isinstance(price_data, dict) else price_data
            updated_prices['industrial_metals'][metal]['usd_per_lb'] = round(price_value, 2)
            updated_prices['industrial_metals'][metal]['last_update'] = current_time
            # logger.info(f"更新工業金屬價格: {metal} = ${price_value}/lb")
            updated = True

        # 3. 檢查匯率
        elif metal in updated_prices.get('currencies', {}):
            price_value = price_data['price'] if isinstance(price_data, dict) else price_data
            updated_prices['currencies'][metal]['twd_rate'] = round(price_value, 4)
            updated_prices['currencies'][metal]['last_update'] = current_time
            # logger.info(f"更新匯率: {metal} = NT${price_value}")
            updated = True

            # 如果更新的是美元匯率，需要重新計算所有金屬的台幣價格
            if metal == 'usd':
                usd_twd_rate = price_value
                update_all_derived_prices(updated_prices, usd_twd_rate, current_time)

        # 4. 檢查稀有金屬（rhodium, ruthenium, iridium, osmium）
        elif metal in ['rhodium', 'ruthenium', 'iridium', 'osmium']:
            # 這些金屬的價格直接更新到 physical_metals 中的公克價格
            metal_gram_key = f"{metal}_gram"
            if metal_gram_key in updated_prices.get('physical_metals', {}):
                price_value = price_data['price'] if isinstance(price_data, dict) else price_data
                # 將美元/盎司轉換為台幣/公克
                twd_per_gram = (price_value * usd_twd_rate) / UNIT_CONVERSIONS["oz_to_gram"]
                updated_prices['physical_metals'][metal_gram_key]['twd_per_gram'] = round(twd_per_gram, 2)
                updated_prices['physical_metals'][metal_gram_key]['last_update'] = current_time
                # logger.info(f"更新稀有金屬價格: {metal_gram_key} = NT${twd_per_gram}/g (從 ${price_value}/oz 轉換)")
                updated = True

        # 5. 檢查其他材料（cobalt, tin）
        elif metal in ['cobalt', 'tin']:
            material_key = f"{metal}_kg"
            if material_key in updated_prices.get('other_materials', {}):
                price_value = price_data['price'] if isinstance(price_data, dict) else price_data
                # 將美元/磅轉換為台幣/公斤
                twd_per_kg = price_value * 2.20462 * usd_twd_rate  # 1磅 = 2.20462公斤
                updated_prices['other_materials'][material_key]['twd_per_kg'] = round(twd_per_kg, 2)
                updated_prices['other_materials'][material_key]['last_update'] = current_time
                # logger.info(f"更新其他材料價格: {material_key} = NT${twd_per_kg}/kg (從 ${price_value}/lb 轉換)")
                updated = True

        if not updated:
            logger.warning(f"無法找到對應的價格項目來更新: {metal}")

    # 更新系統資訊
    updated_prices['lastUpdate'] = current_time
    updated_prices['updateTime'] = datetime.now(tz).strftime('%Y年%m月%d日 %H:%M:%S')
    updated_prices['updatedBy'] = 'TwelveData API'
    updated_prices['source'] = 'TwelveData API + 管理員設定'

    # 保存更新後的價格
    save_prices(updated_prices)
    # logger.info(f"價格數據已保存，共更新 {len(fetched_data)} 個項目")

    return updated_prices

def update_derived_prices_for_metal(updated_prices, metal, usd_per_oz_price, usd_twd_rate, current_time):
    """為特定金屬更新所有衍生價格（台錢、公克）"""
    # 更新台錢價格 - 只有當該項目的 auto_update 為 true 時才更新
    metal_twd_key = f"{metal}_twd"
    if metal_twd_key in updated_prices.get('metals_twd', {}):
        # 檢查該項目的 auto_update 設定
        if updated_prices['metals_twd'][metal_twd_key].get('auto_update', False):
            twd_per_oz = usd_per_oz_price * usd_twd_rate
            twd_per_tael = convert_gram_to_tael(convert_oz_to_gram(twd_per_oz))
            updated_prices['metals_twd'][metal_twd_key]['twd_per_tael'] = round(twd_per_tael, 2)
            updated_prices['metals_twd'][metal_twd_key]['last_update'] = current_time
            # logger.info(f"更新台錢價格: {metal_twd_key} = NT${twd_per_tael}/tael")
        # else:
            # logger.info(f"跳過台錢價格更新（auto_update=false）: {metal_twd_key}")

    # 更新公克價格 - 只有當該項目的 auto_update 為 true 時才更新
    metal_gram_key = f"{metal}_gram"
    if metal_gram_key in updated_prices.get('physical_metals', {}):
        # 檢查該項目的 auto_update 設定
        if updated_prices['physical_metals'][metal_gram_key].get('auto_update', False):
            twd_per_gram = (usd_per_oz_price * usd_twd_rate) / UNIT_CONVERSIONS["oz_to_gram"]
            updated_prices['physical_metals'][metal_gram_key]['twd_per_gram'] = round(twd_per_gram, 2)
            updated_prices['physical_metals'][metal_gram_key]['last_update'] = current_time
            # logger.info(f"更新公克價格: {metal_gram_key} = NT${twd_per_gram}/g")
        # else:
            # logger.info(f"跳過公克價格更新（auto_update=false）: {metal_gram_key}")

def update_all_derived_prices(updated_prices, new_usd_twd_rate, current_time):
    """當美元匯率更新時，重新計算所有金屬的台幣衍生價格"""
    # logger.info(f"美元匯率更新為 {new_usd_twd_rate}，重新計算所有台幣價格")

    # 重新計算所有基礎金屬的衍生價格
    for metal, data in updated_prices.get('metals', {}).items():
        if data.get('enabled', True):
            usd_per_oz = data['usd_per_oz']
            # 注意：這裡我們不直接檢查基礎金屬的 auto_update，
            # 因為 update_derived_prices_for_metal 函數會在內部檢查每個衍生項目的 auto_update
            update_derived_prices_for_metal(updated_prices, metal, usd_per_oz, new_usd_twd_rate, current_time)

    # 重新計算稀有金屬的台幣價格（如果它們有美元基礎價格）
    # 注意：稀有金屬可能沒有美元基礎價格，這種情況下不需要重新計算

def disable_auto_update_for_item(item_name):
    """當 TwelveData 不支援某個項目時，自動禁用其自動更新功能"""
    try:
        current_prices = load_prices()
        updated = False

        # 檢查基礎金屬
        if item_name in current_prices.get('metals', {}):
            current_prices['metals'][item_name]['auto_update'] = False
            logger.info(f"已禁用基礎金屬 {item_name} 的自動更新")
            updated = True

        # 檢查稀有金屬（通過 physical_metals 中的 _gram 項目）
        if f"{item_name}_gram" in current_prices.get('physical_metals', {}):
            current_prices['physical_metals'][f"{item_name}_gram"]['auto_update'] = False
            logger.info(f"已禁用稀有金屬 {item_name}_gram 的自動更新")
            updated = True

        # 檢查其他材料
        if f"{item_name}_kg" in current_prices.get('other_materials', {}):
            current_prices['other_materials'][f"{item_name}_kg"]['auto_update'] = False
            logger.info(f"已禁用其他材料 {item_name}_kg 的自動更新")
            updated = True

        # 檢查工業金屬
        if item_name in current_prices.get('industrial_metals', {}):
            current_prices['industrial_metals'][item_name]['auto_update'] = False
            logger.info(f"已禁用工業金屬 {item_name} 的自動更新")
            updated = True

        # 檢查匯率
        if item_name in current_prices.get('currencies', {}):
            current_prices['currencies'][item_name]['auto_update'] = False
            logger.info(f"已禁用匯率 {item_name} 的自動更新")
            updated = True

        # 同時禁用相關的衍生項目
        if item_name in ['gold', 'silver', 'palladium', 'platinum']:
            # 禁用對應的台錢和公克項目
            twd_key = f"{item_name}_twd"
            gram_key = f"{item_name}_gram"

            if twd_key in current_prices.get('metals_twd', {}):
                current_prices['metals_twd'][twd_key]['auto_update'] = False
                logger.info(f"已禁用衍生項目 {twd_key} 的自動更新")
                updated = True

            if gram_key in current_prices.get('physical_metals', {}):
                current_prices['physical_metals'][gram_key]['auto_update'] = False
                logger.info(f"已禁用衍生項目 {gram_key} 的自動更新")
                updated = True

        if updated:
            # 更新系統資訊
            current_prices['lastUpdate'] = datetime.now(tz).isoformat()
            current_prices['updateTime'] = datetime.now(tz).strftime('%Y年%m月%d日 %H:%M:%S')
            current_prices['updatedBy'] = 'TwelveData API (自動禁用)'

            # 保存更新
            save_prices(current_prices)
            logger.info(f"已保存 {item_name} 的自動更新禁用設定")
        else:
            logger.warning(f"無法找到項目 {item_name} 來禁用自動更新")

    except Exception as e:
        logger.error(f"禁用 {item_name} 自動更新時發生錯誤: {e}")

def get_cached_twelvedata_prices(from_frontend=False):
    """獲取緩存的 TwelveData 價格，如果需要則更新"""
    # 如果是來自前端的請求，更新活動狀態
    if from_frontend:
        update_frontend_activity()

    # 檢查是否應該更新 TwelveData 數據
    if should_update_twelvedata():
        # logger.info("正在從 TwelveData API 更新價格...")
        new_prices = fetch_twelvedata_prices()

        if new_prices:
            TWELVEDATA_CACHE["prices"] = new_prices
            TWELVEDATA_CACHE["last_update"] = time.time()
            # logger.info("TwelveData 價格緩存更新成功")
        else:
            # 檢查失敗原因
            if TWELVEDATA_CACHE["api_key_valid"] is False:
                logger.warning("TwelveData API Key 無效，請檢查 .env 文件中的 TWELVEDATA_API_KEY")
            elif TWELVEDATA_CACHE["network_retry_count"] >= TWELVEDATA_CACHE["max_network_retries"]:
                logger.warning("TwelveData 網絡重試次數已達上限，請檢查網絡連接")
            else:
                logger.warning("TwelveData 價格更新失敗，使用現有緩存")
    elif TWELVEDATA_CACHE["is_paused"]:
        logger.info("TwelveData 請求已暫停（閒置狀態）")
    elif TWELVEDATA_CACHE["api_key_valid"] is False:
        logger.info("TwelveData API Key 無效，跳過更新")

    return TWELVEDATA_CACHE["prices"]

def is_valid_session_request(request):
    """驗證是否為有效的 session 請求"""
    # 檢查是否有 session
    if 'user_id' not in session:
        # 為新用戶創建 session
        session['user_id'] = os.urandom(16).hex()
        session['created_at'] = time.time()
        session.permanent = True

    # 檢查 Referer 頭部以防止外部網站濫用
    referer = request.headers.get('Referer', '')
    host = request.headers.get('Host', '')

    # 允許的來源：同域名或無 Referer（直接訪問）
    if referer and host:
        if not (referer.startswith(f'http://{host}') or referer.startswith(f'https://{host}')):
            logger.warning(f"拒絕外部請求: Referer={referer}, Host={host}")
            return False

    # 檢查 User-Agent 以過濾明顯的機器人請求
    user_agent = request.headers.get('User-Agent', '').lower()
    bot_indicators = ['bot', 'crawler', 'spider', 'scraper', 'curl', 'wget']
    if any(indicator in user_agent for indicator in bot_indicators):
        logger.warning(f"拒絕機器人請求: User-Agent={user_agent}")
        return False

    return True

def update_frontend_activity():
    """更新前端活動狀態"""
    current_time = time.time()
    TWELVEDATA_CACHE["last_frontend_request"] = current_time

    # 添加當前 session 到活躍列表
    if 'user_id' in session:
        TWELVEDATA_CACHE["active_sessions"].add(session['user_id'])

    # 如果之前因閒置而暫停，現在恢復
    if TWELVEDATA_CACHE["is_paused"]:
        TWELVEDATA_CACHE["is_paused"] = False
        logger.info("檢測到前端活動，恢復 TwelveData 請求")

def check_idle_status():
    """檢查是否應該因閒置而暫停 TwelveData 請求"""
    settings = load_settings()

    if not settings.get('twelvedata', {}).get('pauseOnIdle', True):
        return False  # 如果設定不暫停，直接返回

    current_time = time.time()
    last_request = TWELVEDATA_CACHE["last_frontend_request"]
    idle_timeout = settings.get('twelvedata', {}).get('idleTimeout', 300)

    # 如果超過閒置時間且尚未暫停
    if current_time - last_request > idle_timeout and not TWELVEDATA_CACHE["is_paused"]:
        TWELVEDATA_CACHE["is_paused"] = True
        logger.info(f"前端閒置超過 {idle_timeout} 秒，暫停 TwelveData 請求")
        return True

    return TWELVEDATA_CACHE["is_paused"]

def should_test_api_key():
    """判斷是否應該測試 API Key"""
    api_key = os.environ.get('TWELVEDATA_API_KEY', '').strip()

    # 如果沒有 API Key，不需要測試
    if not api_key:
        return False

    current_time = time.time()
    last_test = TWELVEDATA_CACHE["last_key_test"]
    test_interval = TWELVEDATA_CACHE["key_test_interval"]

    # 如果從未測試過，或者超過測試間隔，或者 Key 狀態未知
    return (last_test == 0 or
            current_time - last_test >= test_interval or
            TWELVEDATA_CACHE["api_key_valid"] is None)

def test_api_key_validity():
    """測試 API Key 是否有效"""
    api_key = os.environ.get('TWELVEDATA_API_KEY', '').strip()

    if not api_key:
        TWELVEDATA_CACHE["api_key_valid"] = False
        return False

    try:
        # 使用簡單的價格查詢測試 API Key
        url = "https://api.twelvedata.com/price"
        params = {
            'symbol': 'XAU/USD',  # 黃金價格
            'apikey': api_key
        }

        response = requests.get(url, params=params, timeout=10)

        if response.status_code == 200:
            data = response.json()

            # 檢查是否為 API Key 錯誤
            if is_api_key_error(data):
                logger.warning("TwelveData API Key 測試失敗：Key 無效")
                TWELVEDATA_CACHE["api_key_valid"] = False
                TWELVEDATA_CACHE["last_key_test"] = time.time()
                return False
            elif 'price' in data:
                logger.info("TwelveData API Key 測試成功")
                TWELVEDATA_CACHE["api_key_valid"] = True
                TWELVEDATA_CACHE["last_key_test"] = time.time()
                TWELVEDATA_CACHE["network_retry_count"] = 0  # 重置網絡重試計數
                return True
            else:
                # 其他錯誤，可能是網絡問題
                logger.warning(f"TwelveData API Key 測試異常: {data}")
                return TWELVEDATA_CACHE["api_key_valid"] != False  # 保持原狀態，除非明確無效
        else:
            logger.warning(f"TwelveData API Key 測試失敗: HTTP {response.status_code}")
            return TWELVEDATA_CACHE["api_key_valid"] != False  # 保持原狀態

    except requests.exceptions.RequestException as e:
        logger.warning(f"TwelveData API Key 測試網絡錯誤: {e}")
        return TWELVEDATA_CACHE["api_key_valid"] != False  # 網絡錯誤不改變 Key 狀態
    except Exception as e:
        logger.error(f"TwelveData API Key 測試異常: {e}")
        return False

def should_update_twelvedata():
    """判斷是否應該更新 TwelveData 數據"""
    settings = load_settings()

    # 檢查是否啟用 TwelveData
    if not settings.get('twelvedata', {}).get('enabled', False):
        logger.info("should_update_twelvedata: TwelveData 功能未啟用")
        return False

    # 檢查 API Key 是否有效
    api_key = os.environ.get('TWELVEDATA_API_KEY', '').strip()
    if not api_key:
        logger.info("should_update_twelvedata: TwelveData API Key 未設定，跳過更新")
        return False

    # 如果 API Key 明確無效，不更新
    if TWELVEDATA_CACHE["api_key_valid"] is False:
        logger.info("should_update_twelvedata: TwelveData API Key 無效，跳過更新")
        return False

    # 定期測試 API Key
    if should_test_api_key():
        logger.info("should_update_twelvedata: 執行 TwelveData API Key 定期測試...")
        if not test_api_key_validity():
            logger.warning("should_update_twelvedata: API Key 測試失敗，跳過更新")
            return False

    # 檢查是否因閒置而暫停
    if check_idle_status():
        logger.info("should_update_twelvedata: 因閒置而暫停，跳過更新")
        return False

    # 檢查網絡重試次數
    if TWELVEDATA_CACHE["network_retry_count"] >= TWELVEDATA_CACHE["max_network_retries"]:
        logger.warning(f"should_update_twelvedata: 網絡重試次數已達上限 ({TWELVEDATA_CACHE['max_network_retries']})，跳過更新")
        return False

    # 檢查是否有任何項目需要更新（基於個別更新頻率）
    result = has_items_to_update()
    logger.info(f"should_update_twelvedata: has_items_to_update 返回 {result}")
    return result

def should_update_twelvedata_for_cron():
    """判斷是否應該更新 TwelveData 數據（Cron 專用版本，跳過閒置檢查）"""
    settings = load_settings()

    # 檢查是否啟用 TwelveData
    if not settings.get('twelvedata', {}).get('enabled', False):
        logger.info("should_update_twelvedata_for_cron: TwelveData 功能未啟用")
        return False

    # 檢查 API Key 是否有效
    api_key = os.environ.get('TWELVEDATA_API_KEY', '').strip()
    if not api_key:
        logger.info("should_update_twelvedata_for_cron: TwelveData API Key 未設定，跳過更新")
        return False

    # 如果 API Key 明確無效，不更新
    if TWELVEDATA_CACHE["api_key_valid"] is False:
        logger.info("should_update_twelvedata_for_cron: TwelveData API Key 無效，跳過更新")
        return False

    # 定期測試 API Key
    if should_test_api_key():
        logger.info("should_update_twelvedata_for_cron: 執行 TwelveData API Key 定期測試...")
        if not test_api_key_validity():
            logger.warning("should_update_twelvedata_for_cron: API Key 測試失敗，跳過更新")
            return False

    # Cron 更新跳過閒置檢查，因為 cron 是外部觸發的

    # 檢查網絡重試次數
    if TWELVEDATA_CACHE["network_retry_count"] >= TWELVEDATA_CACHE["max_network_retries"]:
        logger.warning(f"should_update_twelvedata_for_cron: 網絡重試次數已達上限 ({TWELVEDATA_CACHE['max_network_retries']})，跳過更新")
        return False

    # Cron 更新只檢查是否有啟用 auto_update 的項目，不檢查更新頻率
    # 如果需要頻率檢查，可以將 has_auto_update_items() 替換為 has_items_to_update()
    result = has_auto_update_items()
    logger.info(f"should_update_twelvedata_for_cron: has_auto_update_items 返回 {result}")
    return result

def has_auto_update_items():
    """檢查是否有項目啟用了 auto_update（Cron 專用，不檢查更新頻率）"""
    current_prices = load_prices()
    
    # 檢查各種類別是否有啟用 auto_update 的項目
    categories = [
        ('metals', current_prices.get('metals', {})),
        ('metals_twd', current_prices.get('metals_twd', {})),
        ('physical_metals', current_prices.get('physical_metals', {})),
        ('other_materials', current_prices.get('other_materials', {})),
        ('industrial_metals', current_prices.get('industrial_metals', {})),
        ('currencies', current_prices.get('currencies', {}))
    ]
    
    for category_name, category_data in categories:
        for item_name, item_data in category_data.items():
            if isinstance(item_data, dict) and item_data.get('auto_update', False) and item_data.get('enabled', True):
                logger.info(f"has_auto_update_items: 找到啟用 auto_update 的項目 {category_name}.{item_name}")
                return True
    
    logger.info("has_auto_update_items: 沒有找到任何啟用 auto_update 的項目")
    return False

def has_items_to_update():
    """檢查是否有項目需要更新（基於個別更新頻率）"""
    current_time = time.time()
    current_prices = load_prices()
    settings = load_settings()
    twelvedata_config = settings.get('twelvedata', {})

    # 獲取全域更新頻率設定（從 prices.json 的 update_frequencies）
    update_frequencies = current_prices.get('update_frequencies', {})
    metal_frequency = update_frequencies.get('metals_seconds', 300)  # 金屬更新間隔（秒）
    currency_frequency = update_frequencies.get('currencies_base_seconds', 3600)  # 匯率更新間隔（秒）

    # logger.info(f"Cron 檢查更新頻率 - 金屬: {metal_frequency}秒, 匯率: {currency_frequency}秒")

    # 檢查基礎金屬是否需要更新
    required_base_metals = set()

    # 收集所有需要的基礎金屬
    for metal, data in current_prices.get('metals', {}).items():
        if data.get('auto_update', False) and data.get('enabled', True):
            required_base_metals.add(metal)
            # logger.info(f"添加基礎金屬到檢查列表: {metal}")

    for metal_twd, data in current_prices.get('metals_twd', {}).items():
        if data.get('auto_update', False) and data.get('enabled', True):
            base_metal = metal_twd.replace('_twd', '')
            required_base_metals.add(base_metal)
            # logger.info(f"添加台錢金屬到檢查列表: {metal_twd} -> {base_metal}")

    for physical_metal, data in current_prices.get('physical_metals', {}).items():
        if data.get('auto_update', False) and data.get('enabled', True):
            base_metal = physical_metal.replace('_gram', '')
            required_base_metals.add(base_metal)
            # logger.info(f"添加實物金屬到檢查列表: {physical_metal} -> {base_metal}")

    # logger.info(f"總共收集到 {len(required_base_metals)} 個需要檢查的基礎金屬: {list(required_base_metals)}")

    # 檢查基礎金屬是否需要更新
    for metal in required_base_metals:
        metal_data = current_prices.get('metals', {}).get(metal, {})
        last_update = metal_data.get('last_update')

        if last_update is None:
            logger.info(f"金屬 {metal} 從未更新過，需要更新")
            return True  # 從未更新過

        try:
            last_update_time = datetime.fromisoformat(last_update.replace('Z', '+00:00')).timestamp()
            time_since_update = current_time - last_update_time
            # logger.info(f"金屬 {metal} 上次更新: {time_since_update:.1f}秒前, 更新頻率: {metal_frequency}秒")
            if time_since_update >= metal_frequency:
                # logger.info(f"金屬 {metal} 需要更新 ({time_since_update:.1f}秒 >= {metal_frequency}秒)")
                return True
        except (ValueError, AttributeError):
            logger.warning(f"金屬 {metal} 時間格式錯誤，需要更新")
            return True  # 時間格式錯誤，需要更新

    # 檢查匯率是否需要更新
    for currency, data in current_prices.get('currencies', {}).items():
        if data.get('auto_update', False) and data.get('enabled', True):
            last_update = data.get('last_update')

            if last_update is None:
                return True  # 從未更新過

            try:
                last_update_time = datetime.fromisoformat(last_update.replace('Z', '+00:00')).timestamp()
                if current_time - last_update_time >= currency_frequency:
                    return True
            except (ValueError, AttributeError):
                return True  # 時間格式錯誤，需要更新

    # 檢查其他材料是否需要更新
    for material, data in current_prices.get('other_materials', {}).items():
        if data.get('auto_update', False) and data.get('enabled', True):
            last_update = data.get('last_update')

            if last_update is None:
                return True  # 從未更新過

            try:
                last_update_time = datetime.fromisoformat(last_update.replace('Z', '+00:00')).timestamp()
                # 其他材料使用金屬更新頻率
                if current_time - last_update_time >= metal_frequency:
                    return True
            except (ValueError, AttributeError):
                return True  # 時間格式錯誤，需要更新

    return False

@app.route('/api/prices/twelvedata', methods=['GET'])
def api_get_twelvedata_prices():
    """API端點 - 獲取 TwelveData 價格數據（需要有效 session）"""
    try:
        # 驗證 session 請求
        if not is_valid_session_request(request):
            return jsonify({
                'error': '無效的請求來源',
                'message': '此 API 僅供授權用戶使用'
            }), 403

        # 獲取緩存時間戳參數（用於前端緩存控制）
        cache_timestamp = request.args.get('t', '')
        session_id = session.get('user_id', 'unknown')
        # logger.info(f"TwelveData API請求 - Session: {session_id[:8]}..., 緩存時間戳: {cache_timestamp}")

        # 嘗試獲取 TwelveData 價格（標記為前端請求）
        twelvedata_prices = get_cached_twelvedata_prices(from_frontend=True)

        if twelvedata_prices:
            # 添加API相關信息
            response_data = twelvedata_prices.copy()
            response_data['fetchedAt'] = datetime.now(tz).isoformat()
            response_data['fetchedBy'] = 'TwelveData API'
            response_data['cacheTimestamp'] = cache_timestamp
            response_data['sessionId'] = session_id[:8] + '...'  # 只顯示部分 session ID

            # 添加狀態信息
            if TWELVEDATA_CACHE["is_paused"]:
                response_data['status'] = '閒置暫停中'
            else:
                response_data['status'] = '正常運行'

            return jsonify(response_data), 200
        else:
            # 如果 TwelveData 不可用，返回管理員設定的價格
            admin_prices = load_prices()
            admin_prices['fetchedAt'] = datetime.now(tz).isoformat()
            admin_prices['fetchedBy'] = 'Flask API (TwelveData 備援)'
            admin_prices['cacheTimestamp'] = cache_timestamp
            admin_prices['sessionId'] = session_id[:8] + '...'
            admin_prices['note'] = 'TwelveData 不可用，使用管理員設定價格'

            if TWELVEDATA_CACHE["is_paused"]:
                admin_prices['status'] = '閒置暫停中'
            else:
                admin_prices['status'] = 'TwelveData 未啟用'

            return jsonify(admin_prices), 200

    except Exception as e:
        logger.error(f"TwelveData API獲取價格失敗: {e}")

        # 返回錯誤時的管理員價格
        error_prices = load_prices()
        error_prices.update({
            'fetchedAt': datetime.now(tz).isoformat(),
            'fetchedBy': 'Flask API',
            'error': str(e),
            'note': 'TwelveData API錯誤，返回管理員設定價格',
            'status': '錯誤狀態'
        })

        return jsonify(error_prices), 200  # 仍返回200，但包含錯誤信息

@app.route('/api/settings/public', methods=['GET'])
def get_public_settings():
    """獲取公開的系統設定（不需要認證）"""
    try:
        settings = load_settings()

        # 只返回前端需要的公開設定
        public_settings = {
            'twelvedata': {
                'enabled': settings.get('twelvedata', {}).get('enabled', False),
                'frontendRefreshRate': settings.get('twelvedata', {}).get('frontendRefreshRate', 300)
            }
        }

        return jsonify(public_settings), 200

    except Exception as e:
        logger.error(f"獲取公開設定失敗: {e}")
        # 返回預設設定
        return jsonify({
            'twelvedata': {
                'enabled': False,
                'frontendRefreshRate': 300
            }
        }), 200

@app.route('/api/admin/twelvedata/test', methods=['POST'])
def test_twelvedata_connection():
    """測試 TwelveData API 連接（需要認證）"""
    # 檢查 token
    auth_header = request.headers.get('Authorization')
    if not auth_header or not auth_header.startswith('Bearer '):
        return jsonify({'error': '需要認證'}), 401

    token = auth_header.split(' ')[1]
    admin_token = os.environ.get('ADMIN_TOKEN', '')

    if token != admin_token:
        return jsonify({'error': '無效的 Token'}), 401

    try:
        # 檢查 API Key 是否設定
        api_key = os.environ.get('TWELVEDATA_API_KEY', '').strip()
        if not api_key:
            return jsonify({
                'success': False,
                'error': 'TwelveData API Key 未設定',
                'message': '請在 .env 文件中設定 TWELVEDATA_API_KEY'
            }), 400

        # 測試 API Key 有效性
        if not test_api_key_validity():
            return jsonify({
                'success': False,
                'error': 'TwelveData API Key 無效或網絡連接失敗',
                'message': '請檢查 API Key 是否正確或網絡連接是否正常'
            }), 400

        return jsonify({
            'success': True,
            'message': 'TwelveData API 連接測試成功',
            'api_key_status': 'valid'
        }), 200

    except Exception as e:
        logger.error(f"TwelveData API 測試失敗: {e}")
        return jsonify({
            'success': False,
            'message': f'TwelveData API 測試失敗: {str(e)}'
        }), 500

# 階段四：新增 TwelveData 管理 API
@app.route('/api/admin/twelvedata/update-config', methods=['POST'])
def update_twelvedata_config():
    """更新 TwelveData 配置（需要認證）"""
    # 檢查 token
    auth_header = request.headers.get('Authorization')
    if not auth_header or not auth_header.startswith('Bearer '):
        return jsonify({'error': '需要認證'}), 401

    token = auth_header.split(' ')[1]
    admin_token = os.environ.get('ADMIN_TOKEN', '')

    if not token or token != admin_token:
        return jsonify({'error': '無效的認證'}), 401

    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': '無效的請求數據'}), 400

        # 載入當前價格數據
        prices_data = load_prices()

        # 更新 TwelveData 配置
        if "twelvedata_config" not in prices_data:
            prices_data["twelvedata_config"] = {}

        config = prices_data["twelvedata_config"]

        # 更新配置項目
        if "enabled" in data:
            config["enabled"] = bool(data["enabled"])
        if "api_key" in data:
            config["api_key"] = str(data["api_key"])
        if "update_interval_seconds" in data:
            config["update_interval_seconds"] = int(data["update_interval_seconds"])
        if "pause_on_idle" in data:
            config["pause_on_idle"] = bool(data["pause_on_idle"])
        if "idle_timeout_seconds" in data:
            config["idle_timeout_seconds"] = int(data["idle_timeout_seconds"])

        # 更新系統資訊
        prices_data["lastUpdate"] = datetime.now(tz).isoformat()
        prices_data["updateTime"] = datetime.now(tz).strftime('%Y年%m月%d日 %H:%M:%S')
        prices_data["updatedBy"] = "管理員"

        # 保存數據
        if save_prices(prices_data):
            # 重新初始化 TwelveData 實例
            global twelvedata_instance
            twelvedata_instance = init_twelvedata()

            return jsonify({
                'success': True,
                'message': 'TwelveData 配置更新成功',
                'config': config
            })
        else:
            return jsonify({'error': '保存配置失敗'}), 500

    except Exception as e:
        logger.error(f"更新 TwelveData 配置失敗: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/cron/update-prices', methods=['GET'])
def cron_update_prices():
    """Cron 觸發價格更新檢查（不需要 session 認證）"""
    try:
        # 驗證查詢字串中的密鑰
        provided_key = request.args.get('key', '').strip()
        expected_key = os.environ.get('CRON_SECRET_KEY', '').strip()

        if not expected_key:
            logger.error("CRON_SECRET_KEY 未在環境變數中設定")
            return jsonify({
                'success': False,
                'message': 'Cron 密鑰未設定',
                'error': 'CRON_SECRET_KEY not configured'
            }), 500

        if not provided_key or provided_key != expected_key:
            logger.warning(f"Cron 觸發失敗：密鑰不正確，來源 IP: {get_real_ip()}")
            return jsonify({
                'success': False,
                'message': '無效的密鑰',
                'error': 'Invalid key'
            }), 401

        # 檢查 TwelveData 功能是否啟用
        settings = load_settings()
        if not settings.get('twelvedata', {}).get('enabled', False):
            logger.info("TwelveData 功能未啟用，Cron 觸發跳過")
            return jsonify({
                'success': True,
                'message': 'TwelveData 功能未啟用，跳過更新檢查',
                'twelvedata_enabled': False,
                'timestamp': datetime.now(tz).isoformat()
            })

        # 檢查是否需要更新（cron 專用版本，跳過閒置檢查）
        if should_update_twelvedata_for_cron():
            # logger.info("Cron 觸發：開始檢查並更新價格...")
            updated_prices = fetch_twelvedata_prices()

            if updated_prices:
                # logger.info("Cron 觸發：價格更新成功")
                return jsonify({
                    'success': True,
                    'message': '價格更新成功',
                    'updated': True,
                    'twelvedata_enabled': True,
                    'timestamp': datetime.now(tz).isoformat(),
                    'updated_items': len(updated_prices) if isinstance(updated_prices, dict) else 0
                })
            else:
                logger.warning("Cron 觸發：價格更新失敗")
                return jsonify({
                    'success': False,
                    'message': '價格更新失敗',
                    'updated': False,
                    'twelvedata_enabled': True,
                    'timestamp': datetime.now(tz).isoformat(),
                    'error': 'Failed to fetch prices from TwelveData API'
                }), 500
        else:
            # logger.info("Cron 觸發：當前不需要更新價格")
            return jsonify({
                'success': True,
                'message': '當前不需要更新價格',
                'updated': False,
                'twelvedata_enabled': True,
                'timestamp': datetime.now(tz).isoformat(),
                'reason': 'Update frequency not reached or no items need update'
            })

    except Exception as e:
        logger.error(f"Cron 觸發價格更新失敗: {e}")
        return jsonify({
            'success': False,
            'message': 'Cron 觸發失敗',
            'error': str(e),
            'timestamp': datetime.now(tz).isoformat()
        }), 500

@app.route('/api/admin/twelvedata/force-update', methods=['POST'])
def force_update_prices():
    """強制更新價格（需要認證）- 修正版本：使用新的價格獲取邏輯"""
    # 檢查 token
    auth_header = request.headers.get('Authorization')
    if not auth_header or not auth_header.startswith('Bearer '):
        return jsonify({'error': '需要認證'}), 401

    token = auth_header.split(' ')[1]
    admin_token = os.environ.get('ADMIN_TOKEN', '')

    if not token or token != admin_token:
        return jsonify({'error': '無效的認證'}), 401

    try:
        # 檢查 API Key 是否設定
        api_key = os.environ.get('TWELVEDATA_API_KEY', '').strip()
        if not api_key:
            return jsonify({
                'success': False,
                'message': 'TwelveData API Key 未設定',
                'error': '請在 .env 文件中設定 TWELVEDATA_API_KEY'
            }), 400

        logger.info("管理員強制更新價格開始...")

        # 使用修正後的價格獲取函數
        updated_prices = fetch_twelvedata_prices()

        if updated_prices is None:
            return jsonify({
                'success': False,
                'message': '價格更新失敗',
                'error': '無法從 TwelveData 獲取價格數據，請檢查 API Key 或網絡連接'
            }), 500

        # 計算實際從 TwelveData API 成功獲取的項目數量
        # 這個數量應該與 fetch_twelvedata_prices() 中成功獲取的項目數量一致

        # 從日誌中我們可以看到實際成功獲取的項目
        # 但更準確的方法是檢查 fetched_data 的內容

        # 重新載入價格以獲取最新狀態
        final_prices = load_prices()
        updated_count = 0
        errors = []

        # 計算在這次更新中實際被修改的項目
        current_time = datetime.now(tz)
        time_threshold = 60  # 60秒內的更新被認為是這次更新的結果

        for section_name, section_data in final_prices.items():
            if isinstance(section_data, dict):
                for item_name, item_data in section_data.items():
                    if isinstance(item_data, dict) and item_data.get('auto_update', False):
                        last_update_str = item_data.get('last_update')
                        if last_update_str:
                            try:
                                last_update_time = datetime.fromisoformat(last_update_str.replace('Z', '+00:00'))
                                time_diff = (current_time - last_update_time).total_seconds()
                                if time_diff <= time_threshold:
                                    updated_count += 1
                                    # logger.info(f"計入更新項目: {section_name}.{item_name}")
                            except (ValueError, AttributeError):
                                pass

        # 獲取配額狀態
        settings = load_settings()
        quota_config = settings.get('twelvedata', {}).get('quota', {})
        quota_status = {
            'daily_usage': quota_config.get('current_daily_usage', 0),
            'daily_limit': quota_config.get('daily_limit', 800),
            'daily_remaining': quota_config.get('daily_limit', 800) - quota_config.get('current_daily_usage', 0),
            'minute_usage': quota_config.get('current_minute_usage', 0),
            'minute_limit': quota_config.get('minute_limit', 8),
            'minute_remaining': quota_config.get('minute_limit', 8) - quota_config.get('current_minute_usage', 0),
            'is_paused': TWELVEDATA_CACHE.get("is_paused", False),
            'last_activity': datetime.now(tz).isoformat()
        }

        logger.info(f"管理員強制更新完成，成功更新 {updated_count} 個價格")

        return jsonify({
            'success': True,
            'message': f'成功更新 {updated_count} 個價格',
            'updated_count': updated_count,
            'errors': errors,
            'quota_status': quota_status
        })

    except Exception as e:
        logger.error(f"強制更新價格失敗: {e}")
        return jsonify({
            'success': False,
            'error': f'強制更新失敗: {str(e)}',
            'message': '請檢查 TwelveData API Key 設定或網絡連接'
        }), 500

@app.route('/api/admin/twelvedata/status', methods=['GET'])
def get_twelvedata_status():
    """獲取 TwelveData 狀態信息（需要認證）"""
    # 檢查 token
    auth_header = request.headers.get('Authorization')
    if not auth_header or not auth_header.startswith('Bearer '):
        return jsonify({'error': '需要認證'}), 401

    token = auth_header.split(' ')[1]
    admin_token = os.environ.get('ADMIN_TOKEN', '')

    if token != admin_token:
        return jsonify({'error': '無效的 Token'}), 401

    try:
        current_time = time.time()
        settings = load_settings()

        api_key = os.environ.get('TWELVEDATA_API_KEY', '').strip()

        status_info = {
            'enabled': settings.get('twelvedata', {}).get('enabled', False),
            'isPaused': TWELVEDATA_CACHE["is_paused"],
            'lastUpdate': TWELVEDATA_CACHE["last_update"],
            'lastFrontendRequest': TWELVEDATA_CACHE["last_frontend_request"],
            'activeSessions': len(TWELVEDATA_CACHE["active_sessions"]),
            'updateInterval': settings.get('twelvedata', {}).get('updateInterval', 3600),
            'idleTimeout': settings.get('twelvedata', {}).get('idleTimeout', 300),
            'pauseOnIdle': settings.get('twelvedata', {}).get('pauseOnIdle', True),
            'currentTime': current_time,
            'timeSinceLastUpdate': current_time - TWELVEDATA_CACHE["last_update"] if TWELVEDATA_CACHE["last_update"] > 0 else 0,
            'timeSinceLastRequest': current_time - TWELVEDATA_CACHE["last_frontend_request"] if TWELVEDATA_CACHE["last_frontend_request"] > 0 else 0,
            'hasValidCache': TWELVEDATA_CACHE["prices"] is not None,
            # API Key 相關狀態
            'hasApiKey': bool(api_key),
            'apiKeyValid': TWELVEDATA_CACHE["api_key_valid"],
            'lastKeyTest': TWELVEDATA_CACHE["last_key_test"],
            'timeSinceLastKeyTest': current_time - TWELVEDATA_CACHE["last_key_test"] if TWELVEDATA_CACHE["last_key_test"] > 0 else 0,
            'keyTestInterval': TWELVEDATA_CACHE["key_test_interval"],
            'networkRetryCount': TWELVEDATA_CACHE["network_retry_count"],
            'maxNetworkRetries': TWELVEDATA_CACHE["max_network_retries"]
        }

        return jsonify({
            'success': True,
            'data': status_info
        }), 200

    except Exception as e:
        logger.error(f"獲取 TwelveData 狀態失敗: {e}")
        return jsonify({
            'success': False,
            'message': f'獲取狀態失敗: {str(e)}'
        }), 500

def validate_new_price_structure(data):
    """驗證新版本價格數據結構"""
    try:
        validated = {
            'metals': {},
            'metals_twd': {},
            'physical_metals': {},
            'other_materials': {},
            'industrial_metals': {},
            'currencies': {},
            'update_frequencies': data.get('update_frequencies', {
                'metals_seconds': 300,
                'currencies_base_seconds': 3600
            }),
            'lastUpdate': data.get('lastUpdate', datetime.now(tz).isoformat()),
            'updateTime': data.get('updateTime', datetime.now(tz).strftime('%Y年%m月%d日 %H:%M:%S')),
            'updatedBy': data.get('updatedBy', '管理員'),
            'source': data.get('source', '管理員設定'),
            'version': '4.0'
        }

        # 驗證貴金屬數據（美元/盎司）
        if 'metals' in data:
            for metal, metal_data in data['metals'].items():
                validated['metals'][metal] = {
                    'usd_per_oz': float(metal_data.get('usd_per_oz', 0)),
                    'auto_update': bool(metal_data.get('auto_update', False)),
                    'buy_multiplier': float(metal_data.get('buy_multiplier', 0.98)),
                    'sell_multiplier': float(metal_data.get('sell_multiplier', 1.02)),
                    'enabled': bool(metal_data.get('enabled', True)),
                    'update_frequency_seconds': int(metal_data.get('update_frequency_seconds', 300)),
                    'last_update': metal_data.get('last_update', None)
                }

        # 驗證台幣金屬數據（台幣/台錢）
        if 'metals_twd' in data:
            for metal, metal_data in data['metals_twd'].items():
                validated['metals_twd'][metal] = {
                    'twd_per_tael': float(metal_data.get('twd_per_tael', 0)),
                    'auto_update': bool(metal_data.get('auto_update', False)),
                    'buy_multiplier': float(metal_data.get('buy_multiplier', 0.98)),
                    'sell_multiplier': float(metal_data.get('sell_multiplier', 1.02)),
                    'enabled': bool(metal_data.get('enabled', True)),
                    'last_update': metal_data.get('last_update', None)
                }

        # 驗證實物交易數據（台幣/公克）
        if 'physical_metals' in data:
            for metal, metal_data in data['physical_metals'].items():
                validated['physical_metals'][metal] = {
                    'twd_per_gram': float(metal_data.get('twd_per_gram', 0)),
                    'auto_update': bool(metal_data.get('auto_update', False)),
                    'buy_multiplier': float(metal_data.get('buy_multiplier', 0.98)),
                    'sell_multiplier': float(metal_data.get('sell_multiplier', 1.02)),
                    'enabled': bool(metal_data.get('enabled', True)),
                    'last_update': metal_data.get('last_update', None)
                }

        # 驗證其他材料數據（台幣/公斤）
        if 'other_materials' in data:
            for material, material_data in data['other_materials'].items():
                validated['other_materials'][material] = {
                    'twd_per_kg': float(material_data.get('twd_per_kg', 0)),
                    'auto_update': bool(material_data.get('auto_update', False)),
                    'buy_multiplier': float(material_data.get('buy_multiplier', 0.85)),
                    'sell_multiplier': float(material_data.get('sell_multiplier', 1.15)),
                    'enabled': bool(material_data.get('enabled', True)),
                    'last_update': material_data.get('last_update', None)
                }

        # 驗證工業金屬數據（向後兼容）
        if 'industrial_metals' in data:
            for metal, metal_data in data['industrial_metals'].items():
                validated['industrial_metals'][metal] = {
                    'usd_per_lb': float(metal_data.get('usd_per_lb', 0)),
                    'auto_update': bool(metal_data.get('auto_update', False)),
                    'buy_multiplier': float(metal_data.get('buy_multiplier', 0.95)),
                    'sell_multiplier': float(metal_data.get('sell_multiplier', 1.05)),
                    'enabled': bool(metal_data.get('enabled', True))
                }

        # 驗證匯率數據
        if 'currencies' in data:
            for currency, currency_data in data['currencies'].items():
                validated['currencies'][currency] = {
                    'twd_rate': float(currency_data.get('twd_rate', 0)),
                    'auto_update': bool(currency_data.get('auto_update', False)),
                    'buy_multiplier': float(currency_data.get('buy_multiplier', 0.998)),
                    'sell_multiplier': float(currency_data.get('sell_multiplier', 1.002)),
                    'enabled': bool(currency_data.get('enabled', True)),
                    'update_frequency_hours': float(currency_data.get('update_frequency_hours', 1.0)),
                    'last_update': currency_data.get('last_update', None)
                }

        return validated

    except Exception as e:
        logger.error(f"價格數據結構驗證失敗: {e}")
        return None

def verify_token(token):
    """驗證管理員 Token"""
    try:
        # 這裡應該實現真正的 Token 驗證邏輯
        # 目前使用簡單的比較，實際應用中應該使用 JWT 或其他安全方法
        valid_token = os.environ.get('ADMIN_TOKEN', 'your-secret-admin-token-here')
        return token == valid_token
    except Exception as e:
        logger.error(f"Token 驗證失敗: {e}")
        return False

@app.route('/api/validate-token', methods=['POST'])
def api_validate_token():
    """API端點 - 驗證管理員 Token"""
    try:
        data = request.get_json()
        if not data or 'token' not in data:
            return jsonify({'error': '缺少 Token'}), 400

        token = data['token']
        if verify_token(token):
            return jsonify({'success': True, 'message': 'Token 有效'}), 200
        else:
            return jsonify({'error': 'Token 無效'}), 401

    except Exception as e:
        logger.error(f"Token 驗證 API 失敗: {e}")
        return jsonify({'error': '驗證失敗'}), 500

@app.route('/api/admin/twelvedata/toggle-pause', methods=['POST'])
def toggle_twelvedata_pause():
    """切換 TwelveData 暫停狀態（需要認證）"""
    # 檢查 token
    auth_header = request.headers.get('Authorization')
    if not auth_header or not auth_header.startswith('Bearer '):
        return jsonify({'error': '需要認證'}), 401

    token = auth_header.split(' ')[1]
    admin_token = os.environ.get('ADMIN_TOKEN', '')

    if not token or token != admin_token:
        return jsonify({'error': '無效的認證'}), 401

    try:
        # 切換暫停狀態
        TWELVEDATA_CACHE["is_paused"] = not TWELVEDATA_CACHE["is_paused"]

        status = "暫停" if TWELVEDATA_CACHE["is_paused"] else "恢復"
        logger.info(f"管理員手動{status} TwelveData 自動更新")

        return jsonify({
            'success': True,
            'message': f'TwelveData 自動更新已{status}',
            'is_paused': TWELVEDATA_CACHE["is_paused"]
        }), 200

    except Exception as e:
        logger.error(f"切換 TwelveData 暫停狀態失敗: {e}")
        return jsonify({
            'success': False,
            'error': f'切換暫停狀態失敗: {str(e)}'
        }), 500

@app.route('/api/admin/twelvedata/enabled-items', methods=['GET'])
def get_enabled_twelvedata_items():
    """獲取啟用 TwelveData 自動更新的項目數量（需要認證）"""
    # 檢查 token
    auth_header = request.headers.get('Authorization')
    if not auth_header or not auth_header.startswith('Bearer '):
        return jsonify({'error': '需要認證'}), 401

    token = auth_header.split(' ')[1]
    admin_token = os.environ.get('ADMIN_TOKEN', '')

    if not token or token != admin_token:
        return jsonify({'error': '無效的認證'}), 401

    try:
        # 載入當前價格數據
        current_prices = load_prices()
        settings = load_settings()
        twelvedata_config = settings.get('twelvedata', {})

        enabled_items = []

        # 收集需要 TwelveData API 的基礎金屬
        required_base_metals = set()

        # 檢查基礎金屬（美元/盎司）
        for metal, data in current_prices.get('metals', {}).items():
            if data.get('auto_update', False) and data.get('enabled', True):
                symbol = twelvedata_config.get('metal_symbols', {}).get(metal)
                if symbol:
                    required_base_metals.add(metal)

        # 檢查台幣金屬（依賴基礎金屬）
        for metal_twd, data in current_prices.get('metals_twd', {}).items():
            if data.get('auto_update', False) and data.get('enabled', True):
                # metal_twd 格式：gold_twd -> gold
                base_metal = metal_twd.replace('_twd', '')
                symbol = twelvedata_config.get('metal_symbols', {}).get(base_metal)
                if symbol:
                    required_base_metals.add(base_metal)

        # 檢查實物交易金屬（依賴基礎金屬）
        for physical_metal, data in current_prices.get('physical_metals', {}).items():
            if data.get('auto_update', False) and data.get('enabled', True):
                # physical_metal 格式：gold_gram -> gold
                base_metal = physical_metal.replace('_gram', '')
                symbol = twelvedata_config.get('metal_symbols', {}).get(base_metal)
                if symbol:
                    required_base_metals.add(base_metal)

        # 將需要的基礎金屬添加到啟用項目
        for metal in required_base_metals:
            symbol = twelvedata_config.get('metal_symbols', {}).get(metal)
            if symbol:
                enabled_items.append({
                    'name': metal,
                    'type': 'metal',
                    'symbol': symbol
                })

        # 檢查其他材料（cobalt_kg, tin_kg）
        for material, data in current_prices.get('other_materials', {}).items():
            if data.get('auto_update', False) and data.get('enabled', True):
                base_name = material.replace('_kg', '')
                symbol = twelvedata_config.get('industrial_symbols', {}).get(base_name)
                if symbol:
                    enabled_items.append({
                        'name': material,
                        'type': 'material',
                        'symbol': symbol
                    })

        # 檢查匯率
        for currency, data in current_prices.get('currencies', {}).items():
            if data.get('auto_update', False) and data.get('enabled', True):
                symbol = twelvedata_config.get('currency_symbols', {}).get(currency)
                if symbol:
                    enabled_items.append({
                        'name': currency,
                        'type': 'currency',
                        'symbol': symbol
                    })

        return jsonify({
            'success': True,
            'enabled_count': len(enabled_items),
            'enabled_items': enabled_items
        }), 200

    except Exception as e:
        logger.error(f"獲取啟用項目失敗: {e}")
        return jsonify({
            'success': False,
            'error': f'獲取啟用項目失敗: {str(e)}'
        }), 500

if __name__ == '__main__':
    # 初始化數據結構
    if not init_data_structure():
        sys.exit(1)

    # 檢查權限
    if not check_permissions():
        sys.exit(1)

    # 顯示首次生成的 token（如果有）
    if new_admin_token:
        print(f"🔑 管理員 token: {new_admin_token}")
        print("⚠️  請妥善保管上述 token，首次登入時需要使用")

    # 從環境變數獲取配置
    host = os.environ.get('FLASK_HOST', '0.0.0.0')
    port = int(os.environ.get('FLASK_PORT', 5000))
    debug = os.environ.get('FLASK_ENV') != 'production'

    # 啟動Flask應用
    app.run(debug=debug, host=host, port=port)
