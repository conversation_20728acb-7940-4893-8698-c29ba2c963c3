金屬價格管理系統 - 第三方組件版權聲明
===========================================

本項目使用了以下第三方組件和庫，特此聲明其版權信息和許可證：

## Python 後端依賴
=================

### Flask 2.3.3
- 版權: Copyright 2010 Pallets
- 許可證: BSD-3-Clause License
- 網址: https://flask.palletsprojects.com/
- 用途: Web 應用框架

### Flask-CORS 4.0.0
- 版權: Copyright 2013 Cory Dolphin
- 許可證: MIT License
- 網址: https://flask-cors.readthedocs.io/
- 用途: 跨域資源共享支援

### Flask-WTF 1.1.1
- 版權: Copyright 2010 Dan Jacob
- 許可證: BSD License
- 網址: https://flask-wtf.readthedocs.io/
- 用途: Flask 表單處理

### WTForms 3.0.1
- 版權: Copyright 2008 WTForms Team
- 許可證: BSD License
- 網址: https://wtforms.readthedocs.io/
- 用途: 表單驗證和渲染

### Werkzeug 2.3.7
- 版權: Copyright 2007 Pallets
- 許可證: BSD-3-Clause License
- 網址: https://werkzeug.palletsprojects.com/
- 用途: WSGI 工具庫

### Pillow 11.3.0
- 版權: Copyright 2010-2023 by Jeffrey A. Clark (Alex) and contributors
- 許可證: HPND License
- 網址: https://python-pillow.org/
- 用途: 圖像處理庫

### Requests 2.32.3
- 版權: Copyright 2019 Kenneth Reitz
- 許可證: Apache License 2.0
- 網址: https://requests.readthedocs.io/
- 用途: HTTP 請求庫

### Nuitka 2.7.11
- 版權: Copyright 2023 Kay Hayen
- 許可證: Apache License 2.0
- 網址: https://nuitka.net/
- 用途: Python 編譯器（開發工具）

## 前端 CSS/JS 框架
==================

### Bootstrap 5.1.3
- 版權: Copyright 2011-2021 The Bootstrap Authors
- 許可證: MIT License
- 網址: https://getbootstrap.com/
- 用途: CSS 框架和 UI 組件
- 文件: static/vendor/bootstrap/

### Bulma 0.9.4
- 版權: Copyright 2023 Jeremy Thomas
- 許可證: MIT License
- 網址: https://bulma.io/
- 用途: 現代 CSS 框架
- 文件: static/vendor/bulma/

### Font Awesome 6.0.0
- 版權: Copyright 2023 Fonticons, Inc.
- 許可證: 
  - Icons: CC BY 4.0 License
  - Fonts: SIL OFL 1.1 License
  - Code: MIT License
- 網址: https://fontawesome.com/
- 用途: 圖標字體庫
- 文件: static/vendor/fontawesome/

### jQuery 3.6.0
- 版權: Copyright OpenJS Foundation and other contributors
- 許可證: MIT License
- 網址: https://jquery.com/
- 用途: JavaScript 庫
- 文件: static/vendor/jquery/

### Toastr
- 版權: Copyright 2012-2015 John Papa and Hans Fjällemark
- 許可證: MIT License
- 網址: https://github.com/CodeSeven/toastr
- 用途: 通知提示庫
- 文件: static/vendor/toastr/

### Chart.js
- 版權: Copyright 2014-2023 Chart.js Contributors
- 許可證: MIT License
- 網址: https://www.chartjs.org/
- 用途: 圖表繪製庫
- 文件: static/vendor/chartjs/

## 外部服務和 API
================

### TradingView Widget
- 版權: Copyright TradingView, Inc.
- 許可證: TradingView Widget License
- 網址: https://www.tradingview.com/
- 用途: 金融圖表和報價小工具
- 使用方式: 嵌入式 Widget

### TwelveData API
- 版權: Copyright TwelveData
- 許可證: TwelveData API Terms of Service
- 網址: https://twelvedata.com/
- 用途: 金融市場數據 API
- 使用方式: REST API 調用

## 許可證全文
=============

### MIT License
Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.

### BSD-3-Clause License
Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions are met:

1. Redistributions of source code must retain the above copyright notice,
   this list of conditions and the following disclaimer.
2. Redistributions in binary form must reproduce the above copyright notice,
   this list of conditions and the following disclaimer in the documentation
   and/or other materials provided with the distribution.
3. Neither the name of the copyright holder nor the names of its contributors
   may be used to endorse or promote products derived from this software
   without specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE
LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
POSSIBILITY OF SUCH DAMAGE.

### Apache License 2.0
Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

### SIL Open Font License 1.1 (Font Awesome 字體)
This Font Software is licensed under the SIL Open Font License, Version 1.1.
This license is copied below, and is also available with a FAQ at:
http://scripts.sil.org/OFL

### Creative Commons Attribution 4.0 (Font Awesome 圖標)
Font Awesome icons are licensed under CC BY 4.0 License
https://creativecommons.org/licenses/by/4.0/

## 本地化文件聲明
================

本項目已將以下 CDN 資源本地化存儲：
- Bootstrap 5.1.3 (CSS + JS)
- Bulma 0.9.4 (CSS)
- Font Awesome 6.0.0 (CSS + JS + 字體文件)
- jQuery 3.6.0 (JS)
- Toastr (CSS + JS)
- Chart.js (JS)

所有本地化文件均保持原始版權聲明，僅用於提高載入速度和離線使用。

## 外部服務使用聲明
==================

本項目使用以下外部服務，使用時請遵守其服務條款：
- TradingView Widget: 僅用於顯示金融圖表，遵循 TradingView 使用條款
- Chart.js CDN: 用於圖表繪製功能
- TwelveData API: 用於獲取金融市場數據，需要 API Key

## 免責聲明
===========

1. 所有第三方組件的使用均遵循其各自的許可證條款
2. 金融數據僅供參考，不構成投資建議
3. 如有任何版權問題，請聯繫項目維護者
4. 使用本項目時請確保遵守所有相關法律法規

## 聯繫信息
===========

如有版權相關問題或需要更多信息，請通過以下方式聯繫：
- 項目維護者: 迪奕科技
- 更新日期: 2025-08-27

---
本文件包含了項目中使用的所有第三方組件的完整版權聲明和許可證信息。
