# Flask API 金屬價格系統 - 實物交易表格修改工作流程

## 專案概況

### 系統架構
- **後端**: Flask API (Python)
- **前端**: HTML + JavaScript (Bulma CSS框架)
- **數據流**: Flask API → JavaScript → HTML表格顯示
- **主要文件**:
  - `app.py`: Flask 後端主程序
  - `templates/market-data.html`: 金屬價格頁面模板
  - `static/js/price-reader.js`: 前端價格讀取器

### 當前系統狀態
- 使用階段四數據結構 (v4.0)
- 支援多種金屬價格顯示（美元/盎司、台幣/台錢、台幣/公克）
- 具備完整的前後端分離架構
- 已啟用 debug 模式，修改後自動重載

## 需求分析

### 第一個需求：新增「買入(錢)」欄位
**目標路徑**: `http://127.0.0.1:5000/market-data.html`

**當前表格結構**:
```
實物交易以公克報價為主
┌─────────┬─────────┬─────────┐
│  項目   │ 買入[g] │ 賣出[g] │
├─────────┼─────────┼─────────┤
│  金 Au  │  xxxx   │  xxxx   │
│  銀 Ag  │  xxxx   │  xxxx   │
│   ...   │   ...   │   ...   │
└─────────┴─────────┴─────────┘
```

**目標表格結構**:
```
實物交易以公克報價為主
┌─────────┬─────────┬──────────┬─────────┐
│  項目   │ 買入[g] │ 買入(錢) │ 賣出[g] │
├─────────┼─────────┼──────────┼─────────┤
│  金 Au  │  xxxx   │   xxxx   │  xxxx   │
│  銀 Ag  │  xxxx   │   xxxx   │  xxxx   │
│   ...   │   ...   │    ...   │   ...   │
└─────────┴─────────┴──────────┴─────────┘
```

### 第二個需求：計算邏輯
**計算公式**: `買入(錢) = 買入[g] × 3.75`
- 3.75 是台錢到公克的換算係數 (1台錢 = 3.75公克)
- 需要在前端 JavaScript 中實現此計算邏輯

## 技術實現分析

### 1. 數據流程
```
Flask API (/api/prices) 
    ↓ (提供買入[g]價格)
JavaScript (price-reader.js)
    ↓ (計算買入(錢) = 買入[g] × 3.75)
HTML Template (market-data.html)
    ↓ (顯示新的表格結構)
用戶瀏覽器
```

### 2. 涉及的文件和修改點

#### A. HTML 模板修改 (`templates/market-data.html`)
**位置**: 第516-567行 - 實物交易表格
**需要修改**:
- 表頭：新增「買入(錢)」欄位
- 數據行：為每種金屬新增對應的顯示元素

#### B. JavaScript 邏輯修改 (`static/js/price-reader.js`)
**位置**: 第265-275行 - `updateNewStructurePrices()` 方法
**需要修改**:
- 新增計算邏輯：`買入(錢) = 買入[g] × 3.75`
- 新增對應的 DOM 元素更新邏輯

#### C. 後端 API (無需修改)
- 當前 API 已提供所需的買入[g]價格數據
- 計算邏輯在前端實現，後端無需變更

### 3. 具體修改步驟

#### 步驟 1: 修改 HTML 表格結構
1. 在表頭新增「買入(錢)」欄位
2. 為8種金屬各新增一個顯示元素
3. 調整 CSS 樣式以適應新的欄位

#### 步驟 2: 修改 JavaScript 計算邏輯
1. 在 `updateNewStructurePrices()` 方法中新增計算邏輯
2. 新增 `updateElementPriceTael()` 輔助方法處理台錢顯示
3. 確保格式化顯示正確

#### 步驟 3: 測試驗證
1. 檢查表格顯示是否正確
2. 驗證計算結果是否準確
3. 確認響應式設計在不同螢幕尺寸下正常

## 風險評估與注意事項

### 1. JavaScript 運行穩定性
- **風險**: 修改可能導致整個 price-reader.js 運行故障
- **預防措施**: 
  - 採用漸進式修改，每次小幅調整
  - 保留原有邏輯不變，只新增功能
  - 充分測試各種瀏覽器兼容性

### 2. 表格佈局影響
- **風險**: 新增欄位可能影響現有表格佈局
- **預防措施**:
  - 檢查響應式設計在手機端的顯示
  - 確保 CSS 樣式適當調整
  - 測試不同螢幕解析度下的顯示效果

### 3. 數據一致性
- **風險**: 計算結果與預期不符
- **預防措施**:
  - 使用固定的換算係數 (3.75)
  - 實施適當的數值格式化
  - 新增錯誤處理機制

## 測試建議

### 1. 功能測試
- 驗證新欄位是否正確顯示
- 檢查計算結果是否準確 (買入[g] × 3.75)
- 確認所有8種金屬都有對應顯示

### 2. 兼容性測試
- 測試不同瀏覽器 (Chrome, Firefox, Safari, Edge)
- 測試不同設備 (桌面、平板、手機)
- 驗證響應式設計是否正常

### 3. 性能測試
- 確認頁面載入速度未受影響
- 檢查 JavaScript 執行效率
- 驗證 API 調用頻率正常

## 實施時程建議

1. **第一階段** (30分鐘): HTML 模板修改
2. **第二階段** (45分鐘): JavaScript 邏輯實現
3. **第三階段** (30分鐘): 樣式調整與測試
4. **第四階段** (15分鐘): 最終驗證與優化

**總預估時間**: 約 2 小時

## 詳細技術實現方案

### HTML 修改詳細方案

#### 當前表頭結構 (第520-524行)
```html
<div class="metal-table-header">
    <div class="metal-table-cell">項目</div>
    <div class="metal-table-cell">買入[g]</div>
    <div class="metal-table-cell">賣出[g]</div>
</div>
```

#### 修改後表頭結構
```html
<div class="metal-table-header">
    <div class="metal-table-cell">項目</div>
    <div class="metal-table-cell">買入[g]</div>
    <div class="metal-table-cell">買入(錢)</div>
    <div class="metal-table-cell">賣出[g]</div>
</div>
```

#### 當前數據行結構 (以金為例，第526-530行)
```html
<div class="metal-table-row">
    <div class="metal-table-cell metal-name">金 Au</div>
    <div class="metal-table-cell" id="gold-gram-buy">載入中...</div>
    <div class="metal-table-cell" id="gold-gram-sell">載入中...</div>
</div>
```

#### 修改後數據行結構
```html
<div class="metal-table-row">
    <div class="metal-table-cell metal-name">金 Au</div>
    <div class="metal-table-cell" id="gold-gram-buy">載入中...</div>
    <div class="metal-table-cell" id="gold-tael-buy">載入中...</div>
    <div class="metal-table-cell" id="gold-gram-sell">載入中...</div>
</div>
```

### JavaScript 修改詳細方案

#### 需要新增的計算邏輯
```javascript
// 在 updateNewStructurePrices() 方法中新增
const metals = ['gold', 'silver', 'palladium', 'platinum', 'rhodium', 'ruthenium', 'iridium', 'osmium'];
metals.forEach(metal => {
    // 原有邏輯
    this.updateElementPrice(`${metal}-gram-buy`, physicalTable[metal]?.buy_twd_per_gram);
    this.updateElementPrice(`${metal}-gram-sell`, physicalTable[metal]?.sell_twd_per_gram);

    // 新增：計算買入(錢)
    const gramPrice = physicalTable[metal]?.buy_twd_per_gram;
    if (gramPrice !== undefined && gramPrice !== null) {
        const taelPrice = gramPrice * 3.75; // 1台錢 = 3.75公克
        this.updateElementPrice(`${metal}-tael-buy`, taelPrice);
    }
});
```

#### 需要新增的格式化方法
```javascript
// 新增台錢價格格式化方法
formatTaelPrice(price) {
    if (typeof price !== 'number' || isNaN(price)) {
        return '載入中...';
    }

    // 台錢價格通常較大，顯示整數或1位小數
    const decimals = price > 1000 ? 0 : 1;

    return new Intl.NumberFormat('zh-TW', {
        minimumFractionDigits: decimals,
        maximumFractionDigits: decimals
    }).format(price);
}
```

### CSS 樣式調整方案

#### 響應式設計考量
```css
/* 確保新欄位在手機端正常顯示 */
@media screen and (max-width: 768px) {
    .metal-table-cell {
        font-size: 0.7rem;
        padding: 5px 2px;
        min-height: 28px;
    }

    /* 如果空間不足，可考慮隱藏賣出[g]欄位 */
    .metal-table-header .metal-table-cell:nth-child(4),
    .metal-table-row .metal-table-cell:nth-child(4) {
        display: none;
    }
}
```

## 實際修改步驟詳解

### 步驟 1: 備份與準備
1. 確認當前系統運行正常
2. 記錄當前表格顯示狀態
3. 準備測試用的金屬價格數據

### 步驟 2: HTML 模板修改
1. 修改表頭，新增「買入(錢)」欄位
2. 為8種金屬各新增 `id="metal-tael-buy"` 的元素
3. 調整 CSS 確保欄位寬度適當

### 步驟 3: JavaScript 邏輯修改
1. 在 `updateNewStructurePrices()` 方法中新增計算邏輯
2. 新增台錢價格的格式化處理
3. 確保錯誤處理機制完善

### 步驟 4: 測試與驗證
1. 檢查瀏覽器開發者工具中的 JavaScript 錯誤
2. 驗證計算結果：手動計算幾個樣本進行對比
3. 測試不同螢幕尺寸下的顯示效果

## 潛在問題與解決方案

### 問題 1: 表格寬度問題
**現象**: 新增欄位後表格過寬，影響手機端顯示
**解決方案**:
- 調整 CSS 中的 `flex` 比例
- 在小螢幕上隱藏部分欄位
- 使用更小的字體和內邊距

### 問題 2: 計算精度問題
**現象**: 浮點數計算可能產生精度誤差
**解決方案**:
```javascript
// 使用 Math.round 確保精度
const taelPrice = Math.round((gramPrice * 3.75) * 100) / 100;
```

### 問題 3: 數據載入順序問題
**現象**: 新欄位可能在原數據載入前就嘗試計算
**解決方案**:
- 確保在 `physicalTable[metal]?.buy_twd_per_gram` 存在時才計算
- 新增適當的 null/undefined 檢查

## 測試用例設計

### 測試用例 1: 基本功能測試
- **輸入**: 金的買入[g]價格 = 2000
- **預期輸出**: 買入(錢) = 7500 (2000 × 3.75)
- **驗證方法**: 手動檢查頁面顯示

### 測試用例 2: 邊界值測試
- **輸入**: 極小值 (0.01) 和極大值 (99999)
- **預期輸出**: 正確的乘法結果和格式化顯示
- **驗證方法**: 檢查數值格式和精度

### 測試用例 3: 錯誤處理測試
- **輸入**: null, undefined, 非數字值
- **預期輸出**: 顯示「載入中...」或適當的錯誤提示
- **驗證方法**: 模擬 API 錯誤情況

## 後續維護考量

- 如需修改換算係數，只需調整 JavaScript 中的常數 (3.75)
- 未來如需新增其他計算欄位，可參考此次實現方式
- 建議建立測試用例以確保未來修改不會破壞現有功能
- 考慮將換算係數設為可配置參數，便於未來調整
